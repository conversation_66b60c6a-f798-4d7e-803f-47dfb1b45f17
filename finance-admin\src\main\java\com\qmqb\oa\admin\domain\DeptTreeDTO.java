package com.qmqb.oa.admin.domain;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@Data
public class DeptTreeDTO<T> {

    /**
     * ID
     */
    private T id;

    /**
     * 父节点ID
     */
    private T parentId;

    /**
     * 名称
     */
    private CharSequence name;

    /**
     * 顺序 越小优先级越高 默认0
     */
    private Comparable<?> weight = 0;

    /**
     * 扩展字段
     */
    private Map<String, Object> extra;

    /**
     * 子节点
     */
    private List<DeptTreeDTO<T>> childList;
}
