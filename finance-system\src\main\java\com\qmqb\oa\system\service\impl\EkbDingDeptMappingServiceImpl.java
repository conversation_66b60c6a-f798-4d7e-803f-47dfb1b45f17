package com.qmqb.oa.system.service.impl;

import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.qmqb.oa.system.mapper.EkbDingDeptMappingMapper;
import com.qmqb.oa.system.service.EkbDingDeptMappingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 钉钉与易快报部门映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class EkbDingDeptMappingServiceImpl extends ServiceImpl<EkbDingDeptMappingMapper, EkbDingDeptMapping> implements EkbDingDeptMappingService {

    @Override
    public void updateBatch(List<EkbDingDeptMapping> batchUpdateList) {

    }
}
