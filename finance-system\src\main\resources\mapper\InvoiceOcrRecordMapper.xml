<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.InvoiceOcrRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.InvoiceOcrRecord">
        <id column="id" property="id"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="business_id" property="businessId"/>
        <result column="invoice_code" property="invoiceCode"/>
        <result column="invoice_number" property="invoiceNumber"/>
        <result column="invoice_date" property="invoiceDate"/>
        <result column="check_code" property="checkCode"/>
        <result column="invoice_amount" property="invoiceAmount"/>
        <result column="capital_amount" property="capitalAmount"/>
        <result column="invoice_tax" property="invoiceTax"/>
        <result column="exclude_tax_amount" property="excludeTaxAmount"/>
        <result column="buyer_name" property="buyerName"/>
        <result column="buyer_tax_number" property="buyerTaxNumber"/>
        <result column="buyer_addr_phone" property="buyerAddrPhone"/>
        <result column="buyer_bank_account" property="buyerBankAccount"/>
        <result column="seller_name" property="sellerName"/>
        <result column="seller_tax_number" property="sellerTaxNumber"/>
        <result column="seller_addr_phone" property="sellerAddrPhone"/>
        <result column="seller_bank_account" property="sellerBankAccount"/>
        <result column="sheet" property="sheet"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="invoice_details" property="invoiceDetails"/>
        <result column="invoice_code_parse" property="invoiceCodeParse"/>
        <result column="image_url" property="imageUrl"/>
        <result column="use_state" property="useState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        process_instance_id,
        business_id,
        invoice_code,
        invoice_number,
        invoice_date,
        check_code,
        invoice_amount,
        capital_amount,
        invoice_tax,
        exclude_tax_amount,
        buyer_name,
        buyer_tax_number,
        buyer_addr_phone,
        buyer_bank_account,
        seller_name,
        seller_tax_number,
        seller_addr_phone,
        seller_bank_account,
        sheet,
        invoice_type,
        invoice_details,
        invoice_code_parse,
        image_url,
        use_state,
        create_time,
        update_time,
        tenant_id,
        is_deleted
    </sql>

    <sql id="selectInvoiceOcrRecordVo">
        select <include refid="Base_Column_List"/>
        from t_invoice_ocr_record
    </sql>

    <select id="selectInvoiceOcrRecordList" parameterType="com.qmqb.oa.system.domain.InvoiceOcrRecord"
            resultMap="BaseResultMap">
        <include refid="selectInvoiceOcrRecordVo"/>
        <where>
            is_deleted = 0 AND use_state = 1
            <if test="invoiceCode != null  and invoiceCode != ''">
                and invoice_code = #{invoiceCode}
            </if>
            <if test="invoiceNumber != null  and invoiceNumber != ''">
                and invoice_number = #{invoiceNumber}
            </if>
            <if test="params.beginInvoiceDate != null and params.beginInvoiceDate != '' and params.endInvoiceDate != null and params.endInvoiceDate != ''">
                and invoice_date between #{params.beginInvoiceDate} and #{params.endInvoiceDate}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="businessId != null  and businessId != ''">
                and business_id = #{businessId}
            </if>
<!--            <if test="buyerAddrPhone != null  and buyerAddrPhone != ''">-->
<!--                and buyer_addr_phone = #{buyerAddrPhone}-->
<!--            </if>-->
<!--            <if test="buyerBankAccount != null  and buyerBankAccount != ''">-->
<!--                and buyer_bank_account = #{buyerBankAccount}-->
<!--            </if>-->
<!--            <if test="sellerName != null  and sellerName != ''">-->
<!--                and seller_name like concat('%', #{sellerName}, '%')-->
<!--            </if>-->
<!--            <if test="sellerTaxNumber != null  and sellerTaxNumber != ''">-->
<!--                and seller_tax_number = #{sellerTaxNumber}-->
<!--            </if>-->
<!--            <if test="sellerAddrPhone != null  and sellerAddrPhone != ''">-->
<!--                and seller_addr_phone = #{sellerAddrPhone}-->
<!--            </if>-->
<!--            <if test="sellerBankAccount != null  and sellerBankAccount != ''">-->
<!--                and seller_bank_account = #{sellerBankAccount}-->
<!--            </if>-->
        </where>
        ORDER BY create_time desc
    </select>

</mapper>
