CREATE TABLE `t_repay_stat_jindie`
(
    `id`                      bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '' 自增id '',
    `batch_no`                varchar(256) DEFAULT NULL COMMENT '' 批次号 '',
    `fbillhead`               varchar(50)  DEFAULT NULL COMMENT '' 单据头(序号)'',
    `faccountbookid`          varchar(50)  DEFAULT NULL COMMENT ''(单据头) 账簿#编码 '',
    `faccountbookid_name`     varchar(50)  DEFAULT NULL COMMENT ''(单据头) 账簿#名称 '',
    `fdate`                   varchar(20)  DEFAULT NULL COMMENT ''(单据头) 日期 '',
    `fvouchergroupid`         varchar(50)  DEFAULT NULL COMMENT ''(单据头) 凭证字#编码 '',
    `fvouchergroupid_name`    varchar(50)  DEFAULT NULL COMMENT ''(单据头) 凭证字#名称 '',
    `fvouchergroupno`         varchar(50)  DEFAULT NULL COMMENT ''(单据头) 凭证号 '',
    `faccbookorgid`           varchar(50)  DEFAULT NULL COMMENT ''(单据头) 核算组织#编码 '',
    `faccbookorgid_name`      varchar(50)  DEFAULT NULL COMMENT ''(单据头) 核算组织#名称 '',
    `split`                   varchar(50)  DEFAULT NULL COMMENT '' 间隔列 '',
    `fentity`                 varchar(50)  DEFAULT NULL COMMENT '' 分录(序号)'',
    `fexplanation`            varchar(200) DEFAULT NULL COMMENT ''(分录) 摘要 '',
    `faccountid`              varchar(50)  DEFAULT NULL COMMENT ''(分录) 科目编码#编码 '',
    `faccountid_name`         varchar(50)  DEFAULT NULL COMMENT ''(分录) 科目编码#名称 '',
    `facctfullname`           varchar(200) DEFAULT NULL COMMENT ''(分录) 科目全名 '',
    `fdetailid_fflex14`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 银行#编码 '',
    `fdetailid_fflex14_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 银行#名称 '',
    `fdetailid_fflex15`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 银行账号#编码 '',
    `fdetailid_fflex15_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 银行账号#名称 '',
    `fdetailid_fflex12`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 物料分组#编码 '',
    `fdetailid_fflex12_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 物料分组#名称 '',
    `fdetailid_fflex13`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 客户分组#编码 '',
    `fdetailid_fflex13_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 客户分组#名称 '',
    `fdetailid_ff100003`      varchar(50)  DEFAULT NULL COMMENT '' 产品#编码 '',
    `fdetailid_ff100003_name` varchar(200) DEFAULT NULL COMMENT ''(分录) 产品#名称 '',
    `fdetailid_ff100004`      varchar(50)  DEFAULT NULL COMMENT ''(分录) 信托包#编码 '',
    `fdetailid_ff100004_name` varchar(50)  DEFAULT NULL COMMENT ''(分录) 信托包#名称 '',
    `fdetailid_fflex16`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 其他往来单位#编码 '',
    `fdetailid_fflex16_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 他往来单位#名称 '',
    `fdetailid_ff100002`      varchar(50)  DEFAULT NULL COMMENT ''(分录) 项目#编码 '',
    `fdetailid_ff100002_name` varchar(50)  DEFAULT NULL COMMENT ''(分录) 项目#名称 '',
    `fdetailid_fflex11`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 组织机构#编码 '',
    `fdetailid_fflex11_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 组织机构#名称 '',
    `fdetailid_fflex5`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 部门#编码 '',
    `fdetailid_fflex5_name`   varchar(50)  DEFAULT NULL COMMENT ''(分录) 部门#名称 '',
    `fdetailid_fflex6`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 客户#编码 '',
    `fdetailid_fflex6_name`   varchar(50)  DEFAULT NULL COMMENT ''(分录) 客户#名称 '',
    `fdetailid_fflex4`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 往来单位#编码 '',
    `fdetailid_fflex4_name`   varchar(50)  DEFAULT NULL COMMENT ''(分录) 往来单位#名称 '',
    `fdetailid_fflex9`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 费用项目#编码 '',
    `fdetailid_fflex9_name`   varchar(50)  DEFAULT NULL COMMENT ''(分录) 费用项目#名称 '',
    `fdetailid_fflex10`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 资产类别#编码 '',
    `fdetailid_fflex10_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 资产类别#名称 '',
    `fdetailid_fflex7`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 员工#编码 '',
    `fdetailid_fflex7_name`   varchar(50)  DEFAULT NULL COMMENT ''(分录) 员工#名称 '',
    `fdetailid_fflex8`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 物料#编码 '',
    `fdetailid_fflex8_name`   varchar(50)  DEFAULT NULL COMMENT ''(分录) 物料#名称 '',
    `fcurrencyid`             varchar(50)  DEFAULT NULL COMMENT ''(分录) 币别#编码 '',
    `fcurrencyid_name`        varchar(50)  DEFAULT NULL COMMENT ''(分录) 币别#名称 '',
    `fexchangeratetype`       varchar(50)  DEFAULT NULL COMMENT ''(分录) 汇率类型#编码 '',
    `fexchangeratetype_name`  varchar(50)  DEFAULT NULL COMMENT ''(分录) 汇率类型#名称 '',
    `fdebit`                  varchar(50)  DEFAULT NULL COMMENT ''(分录) 借方金额 '',
    `fcredit`                 varchar(50)  DEFAULT NULL COMMENT ''(分录) 贷方金额 '',
    PRIMARY KEY (`id`),
    KEY `idx_batch_no` (`batch_no`) USING BTREE
)