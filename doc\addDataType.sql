alter table t_repay_gen_record add column data_type int(4) default 1 comment '数据类型:1-资金资产还款, 2-履约保证金退款';


ALTER TABLE `t_ekb_message`
    ADD COLUMN `remark` varchar(512) NULL COMMENT '备注' AFTER `edit_time`,
ADD COLUMN `action_name` varchar(124) NULL COMMENT '活动名称' AFTER `remark`;

ALTER TABLE `t_ekb_message`
    MODIFY COLUMN `msg_action` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '出站消息类别: flow.rejected=被驳回;freeflow.retract=单据撤回;freeflow.delete=单据删除;backlog.sending=待寄送;flow.paid=已支付/审批完成;freeflow.mention=被@;backlog.paying=待支付;freeflow.comment=评论;backlog.approving=待审批;freeflow.carbonCopy=抄送' AFTER `user_id`;

ALTER TABLE `t_ekb_message`
    MODIFY COLUMN `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注' AFTER `edit_time`;

ALTER TABLE `t_ekb_ding_todo_message`
    MODIFY COLUMN `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注' AFTER `action_name`;

ALTER TABLE `t_ekb_ding_todo_message`
    MODIFY COLUMN `ding_task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '钉钉待办任务id' AFTER `ding_source_id`;

