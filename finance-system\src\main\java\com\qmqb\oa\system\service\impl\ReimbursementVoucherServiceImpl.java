package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.system.domain.ReimbursementVoucher;
import com.qmqb.oa.system.mapper.ReimbursementVoucherMapper;
import com.qmqb.oa.system.service.ReimbursementVoucherService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报销凭证表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
public class ReimbursementVoucherServiceImpl extends ServiceImpl<ReimbursementVoucherMapper, ReimbursementVoucher> implements ReimbursementVoucherService {

    /**
     * 根据processInstanceId查询报销凭证信息
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public List<ReimbursementVoucher> getByProcessInstanceId(String processInstanceId) {
        QueryWrapper<ReimbursementVoucher> wrapper = new QueryWrapper<>();
        wrapper.eq("process_instance_id", processInstanceId);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据制单日期查询当月最新一条报销凭证信息
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    @Override
    public ReimbursementVoucher getLatestByMakeDate(Date beginDate, Date endDate) {
        QueryWrapper<ReimbursementVoucher> wrapper = new QueryWrapper<>();
        wrapper.between("make_date", beginDate, endDate);
        wrapper.orderByDesc("voucher_number");
        wrapper.last("LIMIT 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 查询报销凭证列表
     * @param reimbursementVoucher 报销凭证
     * @return 报销凭证
     */
    @Override
    public List<ReimbursementVoucher> selectReimbursementVoucherList(ReimbursementVoucher reimbursementVoucher) {
        return this.baseMapper.selectReimbursementVoucherList(reimbursementVoucher);
    }

}
