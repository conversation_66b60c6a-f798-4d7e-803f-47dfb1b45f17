package com.qmqb.oa.admin.api.client;


import com.alibaba.fastjson.JSON;
import com.dingtalk.api.response.OapiOcrStructuredRecognizeResponse;
import com.dingtalk.api.response.OapiProcessinstanceFileUrlGetResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.hzed.structure.oss.AliOssTemplate;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.domain.Invoice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class DingTalkOApiClientTest extends BaseTest {

    @Autowired
    DingTalkOApiClient dingTalkOApiClient;
    @Autowired
    AliOssTemplate aliOssTemplate;

    @Test
    public void getAccessToken() {
        String accessToken = dingTalkOApiClient.getAccessToken();
        log.info("accessToken: {}", accessToken);
    }

    @Test
    public void listProcessInstanceIds() {
        List<String> ids = dingTalkOApiClient.listProcessInstanceIds("PROC-7KYJ5N9W-G5C158XH12BH6BJJBOSY2-9WSVMUPJ-E", null, null, null);
        log.info("ids: {}", ids.toString());
    }

    @Test
    public void getProcessInstance() {
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = dingTalkOApiClient.getProcessInstance("4f08f13f-b77b-4e36-9a4c-7af404697b2e");
        log.info("processInstance: {}", processInstance);
    }

    @Test
    public void getProcessFile() {
        OapiProcessinstanceFileUrlGetResponse.AppSpaceResponse processFile = dingTalkOApiClient.getProcessFile("", "");
        log.info("processFile: {}", processFile);
    }

    @Test
    public void getTodoNum() {
        Long todoNum = dingTalkOApiClient.getTodoNum("");
        log.info("todoNum: {}", todoNum);
    }

    @Test
    public void addComment() {
        Boolean addComment = dingTalkOApiClient.addComment("", "", "", Lists.newArrayList());
        log.info("addComment: {}", addComment);
    }

    @Test
    public void executeTask() {
    }

    @Test
    public void ocr() {
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult ocr = dingTalkOApiClient.ocr("http://e-pay.oss-cn-shenzhen.aliyuncs.com/test-a/image/20210507/9e844187ffffd2673fda6ce19b4f15eb.png", "invoice");
        log.info("ocr: {}", ocr);
    }

    @Test
    public void getDepartment() {
        OapiV2DepartmentGetResponse.DeptGetResponse department = dingTalkOApiClient.getDepartment("94202416");
        log.info("department: {}", JSON.toJSONString(department));
    }

    @Test
    public void testUpload() throws Exception {
        String accessLink = "http://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev//ocr/meal/20210511/57bcd92f1669471c1ad113c13e4372f8.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice = dingTalkOApiClient.ocr(accessLink, "invoice");
        log.info("专票发票联ocr: {}", invoice.getData());

        String accessLink1 = "http://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev//ocr/meal/20210511/8c03698c1a25edade04e2287341a9113.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice1 = dingTalkOApiClient.ocr(accessLink1, "invoice");
        log.info("专票抵扣联ocr: {}", invoice1.getData());

        String accessLink2 = "https://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev/local/ocr/meal/20210511/%E6%99%AE%E7%A5%A8.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice2 = dingTalkOApiClient.ocr(accessLink2, "invoice");
        log.info("普票ocr: {}", invoice2.getData());

        String accessLink3 = "https://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev/local/ocr/meal/20210511/%E6%99%AE%E7%A5%A8%E7%94%B5%E5%AD%90.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice3 = dingTalkOApiClient.ocr(accessLink3, "invoice");
        log.info("普票电子ocr: {}", invoice3.getData());

        String accessLink4 = "https://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev/local/ocr/meal/20210511/%E6%9C%BA%E6%89%93%E5%8F%91%E7%A5%A8.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice4 = dingTalkOApiClient.ocr(accessLink4, "invoice");
        log.info("机打发票ocr: {}", invoice4.getData());

        String accessLink5 = "https://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev/local/ocr/meal/20210511/%E6%9C%BA%E6%89%93%E5%8F%91%E7%A5%A8%E7%94%B5%E5%AD%90.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice5 = dingTalkOApiClient.ocr(accessLink5, "invoice");
        log.info("机打发票电子ocr: {}", invoice5.getData());

        String accessLink6 = "https://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev/local/ocr/meal/20210511/%E7%94%B5%E5%AD%90%E6%99%AE%E7%A5%A8%E8%B4%A7%E7%89%A9.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice6 = dingTalkOApiClient.ocr(accessLink6, "invoice");
        log.info("电子普票货物ocr: {}", invoice6.getData());

        String accessLink7 = "https://e-pay.oss-cn-shenzhen.aliyuncs.com/finance-atuo/dev/local/ocr/meal/20210511/%E7%94%B5%E5%AD%90%E6%99%AE%E7%A5%A8%E9%A1%B9%E7%9B%AE%E5%90%8D%E7%A7%B0.png";
        OapiOcrStructuredRecognizeResponse.OcrStructuredResult invoice7 = dingTalkOApiClient.ocr(accessLink7, "invoice");
        log.info("电子普票项目名称ocr: {}", invoice7.getData());
    }

    public static void main(String[] args) {
        String str = "{\n" +
                "\t\"发票代码\": \"3100204130\",\n" +
                "\t\"发票号码\": \"18671465\",\n" +
                "\t\"开票日期\": \"2021年4月13日\",\n" +
                "\t\"校验码\": \"\",\n" +
                "\t\"发票金额\": \"1528\",\n" +
                "\t\"大写金额\": \"壹仟伍佰贰拾捌圆整\",\n" +
                "\t\"发票税额\": \"86.49\",\n" +
                "\t\"不含税金额\": \"1441.51\",\n" +
                "\t\"受票方名称\": \"广州市全民钱包科技有限公司\",\n" +
                "\t\"受票方税号\": \"91440101MA59R2NL5W\",\n" +
                "\t\"受票方地址、电话\": \"广州市天河区兴国路21号020-31420107\",\n" +
                "\t\"受票方开户行、账号\": \"招商银行广州中山二路支行120912637210919\",\n" +
                "\t\"销售方名称\": \"上海晨衡酒店管理有限公司\",\n" +
                "\t\"销售方税号\": \"91310101MA1FPF1L81\",\n" +
                "\t\"销售方地址、电话\": \"上海黄城区周中路125弄20一层四层丶五朋s01s18丶30号031-60799099\",\n" +
                "\t\"销售方开户行、账号\": \"浙江泰聚商业银行股份有限公司上海松江支行银行31010030201000033373\",\n" +
                "\t\"联次\": \"第二联：发票联\",\n" +
                "\t\"发票类型\": \"专用发票\",\n" +
                "\t\"发票详单\": \"[{\\\"货物或应税劳务、服务名称\\\":\\\"*住宿服务*住宿费\\\",\\\"规格型号\\\":\\\"\\\",\\\"单位\\\":\\\"\\\",\\\"数量\\\":\\\"\\\",\\\"单价\\\":\\\"\\\",\\\"金额\\\":\\\"1441.51\\\",\\\"税率\\\":\\\"6%\\\",\\\"税额\\\":\\\"86.49\\\"}]\",\n" +
                "\t\"发票代码解析\": \"{\\\"文字版\\\":\\\"1\\\",\\\"印刷批次\\\":\\\"4\\\",\\\"年份\\\":\\\"2020\\\",\\\"联次\\\":\\\"3\\\",\\\"金额版\\\":\\\"电脑版\\\",\\\"行政区划代码\\\":\\\"上海市\\\"}\"\n" +
                "}";
        Invoice invoice = JSON.parseObject(str, Invoice.class);
        System.out.println("invoiceOcr = " + invoice);

    }
}
