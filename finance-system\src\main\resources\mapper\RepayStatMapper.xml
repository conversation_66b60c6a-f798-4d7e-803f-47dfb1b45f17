<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.RepayStatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.RepayStat">
        <id column="id" property="id" />
        <result column="real_repayment_date" property="realRepaymentDate" />
        <result column="batch_no" property="batchNo" />
        <result column="voucher_type" property="voucherType" />
        <result column="voucher_code" property="voucherCode" />
        <result column="source_type" property="sourceType" />
        <result column="diff_voucher" property="diffVoucher" />
        <result column="order_num" property="orderNum" />
        <result column="remark" property="remark" />
        <result column="subject_code" property="subjectCode" />
        <result column="subject_name" property="subjectName" />
        <result column="currency" property="currency" />
        <result column="exchange_rate" property="exchangeRate" />
        <result column="number" property="number" />
        <result column="price" property="price" />
        <result column="direction" property="direction" />
        <result column="local_amount" property="localAmount" />
        <result column="foreign_amount" property="foreignAmount" />
        <result column="debt_no" property="debtNo" />
        <result column="debt_date" property="debtDate" />
        <result column="order_no" property="orderNo" />
        <result column="order_date" property="orderDate" />
        <result column="repay_date" property="repayDate" />
        <result column="clerk_code" property="clerkCode" />
        <result column="clerk_name" property="clerkName" />
        <result column="bank_no" property="bankNo" />
        <result column="settled_type" property="settledType" />
        <result column="company_code" property="companyCode" />
        <result column="company_name" property="companyName" />
        <result column="dept_code" property="deptCode" />
        <result column="dept_name" property="deptName" />
        <result column="stock_code" property="stockCode" />
        <result column="stock_name" property="stockName" />
        <result column="user_code" property="userCode" />
        <result column="user_name" property="userName" />
        <result column="project_code" property="projectCode" />
        <result column="project_name" property="projectName" />
        <result column="ex_ass1_code" property="exAss1Code" />
        <result column="ex_ass1" property="exAss1" />
        <result column="ex_ass2_code" property="exAss2Code" />
        <result column="ex_ass2" property="exAss2" />
        <result column="ex_ass3_code" property="exAss3Code" />
        <result column="ex_ass3" property="exAss3" />
        <result column="ex_ass4_code" property="exAss4Code" />
        <result column="ex_ass4" property="exAss4" />
        <result column="ex_ass5_code" property="exAss5Code" />
        <result column="ex_ass5" property="exAss5" />
        <result column="ex_ass6_code" property="exAss6Code" />
        <result column="ex_ass6" property="exAss6" />
        <result column="ex_ass7_code" property="exAss7Code" />
        <result column="ex_ass7" property="exAss7" />
        <result column="ex_ass8_code" property="exAss8Code" />
        <result column="ex_ass8" property="exAss8" />
        <result column="ex_ass9_code" property="exAss9Code" />
        <result column="ex_ass9" property="exAss9" />
        <result column="ex_ass10_code" property="exAss10Code" />
        <result column="ex_ass10" property="exAss10" />
        <result column="dbCreateDt" property="dbcreatedt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, real_repayment_date, batch_no, voucher_type, voucher_code, source_type, diff_voucher, order_num, remark, subject_code, subject_name, currency, exchange_rate, number, price, direction, local_amount, foreign_amount, debt_no, debt_date, order_no, order_date, repay_date, clerk_code, clerk_name, bank_no, settled_type, company_code, company_name, dept_code, dept_name, stock_code, stock_name, user_code, user_name, project_code, project_name, ex_ass1_code, ex_ass1, ex_ass2_code, ex_ass2, ex_ass3_code, ex_ass3, ex_ass4_code, ex_ass4, ex_ass5_code, ex_ass5, ex_ass6_code, ex_ass6, ex_ass7_code, ex_ass7, ex_ass8_code, ex_ass8, ex_ass9_code, ex_ass9, ex_ass10_code, ex_ass10, dbCreateDt
    </sql>

</mapper>
