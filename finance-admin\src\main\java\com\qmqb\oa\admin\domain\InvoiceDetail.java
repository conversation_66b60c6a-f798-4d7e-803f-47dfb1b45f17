package com.qmqb.oa.admin.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <p>
 * 发票详单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
public class InvoiceDetail {

    /**
     * 货物或应税劳务、服务名称
     */
    @JSONField(name = "货物或应税劳务、服务名称")
    private String goodsLabourServiceName;

    /**
     * 规格型号
     */
    @JSONField(name = "规格型号")
    private String specificationsModel;

    /**
     * 单位
     */
    @JSONField(name = "单位")
    private String unit;

    /**
     * 数量
     */
    @JSONField(name = "数量")
    private String quantity;


    /**
     * 单价
     */
    @JSONField(name = "单价")
    private String unitPrice;

    /**
     * 金额
     */
    @JSONField(name = "金额")
    private String amount;

    /**
     * 税率
     */
    @JSONField(name = "税率")
    private String taxRate;

    /**
     * 税额
     */
    @JSONField(name = "税额")
    private String taxAmount;


}
