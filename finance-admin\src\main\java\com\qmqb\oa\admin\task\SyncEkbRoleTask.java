package com.qmqb.oa.admin.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qmqb.oa.admin.config.CommonConfig;
import com.qmqb.oa.admin.config.EkbConfig;
import com.qmqb.oa.admin.constant.EkbConstants;
import com.qmqb.oa.admin.domain.dto.EkbSyncLeaderRoleDTO;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAccessTokenRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchUpdateRoleUserInfoReq;
import com.qmqb.oa.admin.service.hsfk.DingTalkUserService;
import com.qmqb.oa.admin.service.hsfk.EkbLoginService;
import com.qmqb.oa.admin.service.hsfk.H5AppDingClientService;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.common.utils.http.HttpRestService;
import com.qmqb.oa.system.domain.*;
import com.qmqb.oa.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步易快报角色
 *
 * <AUTHOR>
 * @date 2024/12/9
 */
@Slf4j
@Component("syncEkbRoleTask")
@RequiredArgsConstructor
public class SyncEkbRoleTask {
    private final DingDeptService dingDeptService;
    private final DingAppConfService dingAppConfService;
    private final H5AppDingClientService h5AppDingClientService;
    private final DingUserService dingUserService;
    private final EkbDingUserMappingService userMappingService;
    private final DingTalkUserService dingTalkUserService;
    private final EkbConfig ekbConfig;
    private final EkbLoginService ekbLoginService;
    private final HttpRestService httpRestService;
    private final CommonConfig commonConfig;
    private final EkbDingDeptMappingService ekbDingDeptMappingService;


    /**
     * 同步领导角色信息到易快报
     * 此方法首先从DingAppConf表中获取所有启用的应用配置，然后根据每个应用的公司ID找到最高级别的部门，
     * 并将该信息存储在映射中接下来，根据领导角色的不同级别，计算出需要同步的部门级别范围，
     * 并调用相应的方法来同步领导角色信息最后，根据需要更新本地用户信息和易快报角色信息
     */
    public void syncLeader() {
        List<DingAppConf> appConfList = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class)
                .eq(DingAppConf::getAppSwitch, 1)
        );
        Map<Integer, Integer> companyLevelMap = Maps.newHashMap();
        for (DingAppConf dingAppConf : appConfList) {
            DingDept maxLevelDept = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class)
                    .eq(DingDept::getCompanyId, dingAppConf.getCompanyId())
                    .orderByDesc(DingDept::getDeptLevel)
                    .last(" limit 1"));
            Integer maxDeptLevel = maxLevelDept.getDeptLevel();
            companyLevelMap.put(dingAppConf.getCompanyId(), maxDeptLevel);
        }
        List<EkbConstants.LeaderRoleEnum> roleEnums = Arrays.stream(EkbConstants.LeaderRoleEnum.values()).sorted().collect(Collectors.toList());
        for (EkbConstants.LeaderRoleEnum leaderRoleEnum : roleEnums) {
            MdcUtil.setTrace();
            MdcUtil.setModuleName("开始同步角色：" + leaderRoleEnum.getEkbRoleName());
            log.info("开始同步");
            //初始化是否需要角色同步的标志
            boolean needRoleSync = false;
/*            //本地数据需要更新的用户列表
            List<DingUser> updateUserList = Lists.newArrayList();*/
            //易快报需要更新角色下的用户列表
            List<EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq> ekbUpdateRoleUserList = Lists.newArrayList();
            try {
                for (Map.Entry<Integer, Integer> entry : companyLevelMap.entrySet()) {
                    int companyId = entry.getKey();
                    int maxDeptLevel = entry.getValue();
                    int startDeptLevel = maxDeptLevel;
                    if (leaderRoleEnum.equals(EkbConstants.LeaderRoleEnum.CENTER_LEADER)) {
                        startDeptLevel = 2;
                    } else if (leaderRoleEnum.equals(EkbConstants.LeaderRoleEnum.N_2_LEADER)) {
                        startDeptLevel = maxDeptLevel - 2;
                    }
                    //同步领导角色
                    EkbSyncLeaderRoleDTO leaderRoleDTO = doSyncLeader(startDeptLevel, companyId, leaderRoleEnum);
                    needRoleSync = needRoleSync || leaderRoleDTO.getNeedSyncEkbRole();
                    ekbUpdateRoleUserList.addAll(leaderRoleDTO.getEkbRoleList());
/*                    //更新本地数据
                    updateUserList.addAll(leaderRoleDTO.getUpdateUserList());*/
                }
                //强制同步角色执行，如果后续需要不强制，删除此条
                needRoleSync = true;
                if (needRoleSync) {
                    //去重
                    ekbUpdateRoleUserList = ekbUpdateRoleUserList.stream().distinct().collect(Collectors.toList());
                    //同步易快报角色
                    syncRoleToEkb(leaderRoleEnum, ekbUpdateRoleUserList);
                }

            } catch (Exception e) {
                log.error("同步易快报角色失败", e);
            } finally {
                MdcUtil.clear();
            }
        }

    }


    /**
     * 同步所有领导角色到易快报
     *
     * @param startDeptLevel 开始的部门级别
     * @param companyId      公司id
     */
    private EkbSyncLeaderRoleDTO doSyncLeader(int startDeptLevel, int companyId, EkbConstants.LeaderRoleEnum roleEnum) {
        log.info("companyId:{},开始同步部门等级小于或等于{}的角色", companyId, startDeptLevel);
        List<DingAppConf> appConfList = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, companyId));
        DingAppConf dingAppConf = appConfList.get(0);
        //初始化是否需要角色同步的标志
        boolean needRoleSync = false;
/*        //本地数据需要更新的用户列表
        List<DingUser> updateUserList = Lists.newArrayList();*/
        //易快报需要更新角色下的用户列表
        List<EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq> ekbUpdateRoleUserList = Lists.newArrayList();
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        //查询当前主体下的所有子部门
        List<DingDept> deptList = Lists.newArrayList();
        if (EkbConstants.LeaderRoleEnum.ALL_LEADER.equals(roleEnum) || EkbConstants.LeaderRoleEnum.CENTER_LEADER.equals(roleEnum)) {
            //获取所有大于等于2并且小于等于startDeptLevel的部门
            LambdaQueryWrapper<DingDept> deptWrapper = Wrappers.lambdaQuery(DingDept.class)
                    .eq(DingDept::getCompanyId, companyId)
                    .le(DingDept::getDeptLevel, startDeptLevel)
                    .ge(DingDept::getDeptLevel, 2);
            deptList = dingDeptService.list(deptWrapper);
            if(EkbConstants.LeaderRoleEnum.ALL_LEADER.equals(roleEnum)){
                //查所有部门时，需要过滤三级部门
                List<DingDept> deptLevel3List = getDeptLevel3List(companyId);
                deptList.removeAll(deptLevel3List);
            }
        } else {
            //三级部门，需要deptLevel =2的中心部门为支线，分别查询出满足条件的部门
            deptList = getDeptLevel3List(companyId);
            log.info("三级部门->子部门层级大于或等于4所有部门列表：{}", JSON.toJSONString(deptList));
        }

        for (DingDept dingDept : deptList) {
            log.info("查询部门：" + dingDept.getDeptChainName());
            Long deptId = dingDept.getDeptId();
            //通过钉钉接口查询当前部门下的用户
            List<OapiV2UserListResponse.ListUserResponse> apiUserList = h5AppDingClientService.getUserListByDeptId(deptId, accessToken);
            log.info("查询部门：" + dingDept.getDeptChainName() + " 用户详请：" + JSON.toJSONString(apiUserList));
            if (Objects.isNull(apiUserList) || apiUserList.size() == 0) {
                continue;
            }
            //找出当前部门的领导
            List<OapiV2UserListResponse.ListUserResponse> leaderList = apiUserList.stream().filter(OapiV2UserListResponse.ListUserResponse::getLeader).collect(Collectors.toList());
            if (leaderList.size() == 0) {
                //跳过当前部门
                continue;
            }
            for (OapiV2UserListResponse.ListUserResponse user : leaderList) {
                EkbDingUserMapping userMapping = userMappingService.findOneByDingUserId(user.getUserid());
                //查询易快报的部门信息
                EkbDingDeptMapping deptMapping = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                        .eq(EkbDingDeptMapping::getCompanyId, companyId)
                        .eq(EkbDingDeptMapping::getDingDeptId, dingDept.getDeptId()));
                EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq ekbUpdateRoleUserInfoReq = new EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq();
                ekbUpdateRoleUserInfoReq.setPathType("id");
                //同步角色时，只传当前部门的易快报部门id，不传用户所在的其它部门ekbUpdateRoleUserInfoReq.setPath(Lists.newArrayList(userMapping.getEkbDeptIdList().split(",")))
                ekbUpdateRoleUserInfoReq.setPath(Lists.newArrayList(deptMapping.getEkbDeptId()));
                ekbUpdateRoleUserInfoReq.setStaffs(Lists.newArrayList(userMapping.getEkbUserId()));
                ekbUpdateRoleUserList.add(ekbUpdateRoleUserInfoReq);
            }
            log.info("部门{}，领导列表{}", dingDept.getDeptChainName(), JSON.toJSONString(ekbUpdateRoleUserList));


        }

        return EkbSyncLeaderRoleDTO.builder().needSyncEkbRole(needRoleSync).ekbRoleList(ekbUpdateRoleUserList).build();

    }

    /**
     * 获取第三级部门列表
     * 该方法旨在筛选出公司内所有属于第三级并且有子部门层级大于或等于4的部门
     * 这些部门被视为“中心部门”，并将返回包括它们及其特定层级子部门在内的列表
     *
     * @param companyId 公司ID，用于查询特定公司的部门信息
     * @return 返回包含第三级部门及其特定层级子部门的列表
     */
    private List<DingDept> getDeptLevel3List(int companyId ) {
        List<DingDept> deptList =  Lists.newArrayList();
        List<DingDept> deptLevel3List = dingDeptService.list(Wrappers.lambdaQuery(DingDept.class)
                .eq(DingDept::getCompanyId, companyId)
                .eq(DingDept::getDeptLevel, 2));
        //筛选出最大部门等级大于或等于4层的中心部门，并且保存好部门的最大部门等级
        List<DingDept> deptLevel3MaxLevelList = Lists.newArrayList();
        for (DingDept dingDept : deptLevel3List) {
            Integer maxChildDeptLevel = dingDeptService.getMaxChildDeptLevel(dingDept.getDeptChainName(), companyId);
            if (maxChildDeptLevel >= 4) {
                //设置好中心部门的最大部门等级
                dingDept.setDeptLevel(maxChildDeptLevel);
                deptLevel3MaxLevelList.add(dingDept);
            }
        }
        log.info("三级部门->子部门层级大于或等于4的中心部门列表：{}", JSON.toJSONString(deptLevel3MaxLevelList));
        //遍历最大部门等级大于或等于4层的中心部门，查出子部门等级小于或等于maxDeptLevel - 2的子部门
        for (DingDept dept : deptLevel3MaxLevelList) {
            List<DingDept> childList = dingDeptService.childList(dept.getDeptChainName(), companyId, (dept.getDeptLevel() - 2));
            //中心部门及其子部门列表
            log.info("中心部门：" + dept.getDeptChainName() + "->子部门列表：" + JSON.toJSONString(childList));
            deptList.addAll(childList);
        }

        return deptList;
    }

    /**
     * 根据钉钉用户详情更新DingUser对象
     * 此方法用于同步钉钉用户最新的信息到本地DingUser对象中，主要更新领导在部门的信息和更新时间
     *
     * @param user       用户对象，包含需要被更新的用户ID
     * @param userDetail 钉钉API返回的用户详细信息，用于更新用户数据
     * @return 返回一个更新后的DingUser对象
     */
    private DingUser getUpdateUser(DingUser user, OapiV2UserGetResponse.UserGetResponse userDetail) {
        DingUser updateUser = new DingUser();
        updateUser.setId(user.getId());
        updateUser.setUserName(user.getUserName());
        updateUser.setUpdateTime(LocalDateTime.now());
        return updateUser;
    }

    /**
     * 同步角色信息到易快报系统
     * 此方法用于根据领导角色枚举和一组更新的用户信息，同步更新易快报中的角色用户信息
     *
     * @param leaderRoleEnum        领导角色枚举，用于确定易快报中的角色ID
     * @param ekbUpdateRoleUserList 包含更新的用户信息列表，用于同步到易快报
     */
    private void syncRoleToEkb(EkbConstants.LeaderRoleEnum leaderRoleEnum, List<EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq> ekbUpdateRoleUserList) {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        EkbBatchUpdateRoleUserInfoReq ekbBatchUpdateRoleUserInfoReq = new EkbBatchUpdateRoleUserInfoReq();
        if (CollectionUtils.isEmpty(ekbUpdateRoleUserList)) {
            EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq req = new EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq();
            req.setStaffs(new ArrayList<>());
            req.setPathType("id");
            req.setPath(new ArrayList<>());
            ekbUpdateRoleUserList.add(req);
            //todo 当角色池为部门角色时，同步角色数据不能为空，
            return;
        }
        ekbBatchUpdateRoleUserInfoReq.setContents(ekbUpdateRoleUserList);
        String oldUrl = ekbConfig.getUpdateRoleUserInfoUrl();
        String preUrl = oldUrl.substring(0, oldUrl.indexOf("roledefs/$"));
        String afterUrl = oldUrl.substring(oldUrl.indexOf("/staffs"));
        //默认使用生产环境角色id
        String ekbRoleId = leaderRoleEnum.getEkbRoleId();
        if (!commonConfig.isProdEnv()) {
            //非生产环境
            ekbRoleId = leaderRoleEnum.getEkbTestRoleId();
        }
        String newUrl = preUrl + "roledefs/$" + ekbRoleId + afterUrl + accessTokenRep.getAccessToken();
        log.info("【易快报-更新角色下员工信息】,request:{},", JSON.toJSONString(ekbBatchUpdateRoleUserInfoReq));
        httpRestService.put(newUrl, JSON.toJSONString(ekbBatchUpdateRoleUserInfoReq), Object.class, "【易快报-更新角色下员工信息】", false);
    }


    public static void main(String[] args) {
        DingDept dept1 = new DingDept();
        dept1.setId(1L);
        dept1.setDeptName("name1");
        DingDept dept2 = new DingDept();
        dept2.setId(2L);
        dept2.setDeptName("name2");
        DingDept dept3 = new DingDept();
        dept3.setId(3L);
        dept3.setDeptName("name3");
        List<DingDept> list = Lists.newArrayList();
        list.add(dept1);
        list.add(dept2);
        list.add(dept3);
        List<DingDept> childList = Lists.newArrayList();
        childList.add(dept1);
        list.removeAll(childList);

        System.out.println(JSON.toJSONString(list));

    }
}
