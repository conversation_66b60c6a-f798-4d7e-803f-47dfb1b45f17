package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

import java.util.List;

/**
 * 易快报更新角色下员工信息请求体
 * <AUTHOR>
 * @date 2024/7/29
 */
@Data
public class EkbBatchUpdateRoleUserInfoReq {

    /**
     * 一个部门对应一个元素
     * pathType传1
     * path为部门id
     * staffs为该部门下的员工列表
     */
    private List<EkbUpdateRoleUserInfoReq> contents;

    @Data
    public static class EkbUpdateRoleUserInfoReq {

        /**
         * 当 pathType = name 或不传时，path 传入部门或自定义档案项名称
         * 当 pathType = code 时，path 传入部门或自定义档案项编码
         * 当 pathType = id 时，path 传入部门或自定义档案项ID
         * 不传默认name
         * 需传值id
         */
        private String pathType;

        /**
         * 部门或自定义档案值
         * pathType = id 时只传最终路径，角色类型为「普通角色」时非必填
         * 部门id
         */
        private List<String> path;

        /**
         * 员工集合
         * 传入 [] 时会删除 path 值所对应的这条数据
         */
        private List<String> staffs;

     
    }
}
