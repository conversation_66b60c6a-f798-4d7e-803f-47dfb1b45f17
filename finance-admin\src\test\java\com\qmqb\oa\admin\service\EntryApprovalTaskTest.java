package com.qmqb.oa.admin.service;

import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.admin.task.EntryApprovalTask;
import com.qmqb.oa.common.enums.Tenant;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 11:07 2025/3/25
 * @Description TODO
 * @MethodName
 * @return null
 */
public class EntryApprovalTaskTest extends BaseTest {

    @Autowired
    EntryApprovalTask entryApprovalTask;

    @Test
    public void robotApproval() {
        entryApprovalTask.entry(Tenant.ZJ.getValue());
    }

    @Test
    public void executeProcess() {
        TenantContextHolder.setTenantId(Tenant.FSBYL.getValue());
        entryApprovalTask.executeProcess("gNvP542xR3i0vfeO6Cwx2Q02371679047109");
    }

    @Test
    public void syncDimissionEmployee() throws InterruptedException {
        entryApprovalTask.syncDimissionEmployee();
    }
}
