package com.qmqb.oa.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {

    public static final String PROJECT_BASE_PACKAGES = "com.qmqb.pub";
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = "sub";

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * oss文件上传目录
     */
    public static final String FILE_TYPE = "ocr/invoice";

    /**
     * 数字
     */
    public static class CommonVal {
        public static final int MINUS_TWO = -2;
        public static final int MINUS_ONE = -1;
        public static final int ZERO = 0;
        public static final int ONE = 1;
        public static final int TWO = 2;
        public static final int THREE = 3;
        public static final int FOUR = 4;
        public static final int FIVE = 5;
        public static final int SIX = 6;
        public static final int NINE = 9;
        public static final int TEN = 10;
        public static final int SIXTEEN = 16;
        public static final int TWENTY = 20;
        public static final int TWENTY_ONE = 21;
        public static final int THIRTY_TWO = 32;
        public static final int FORTY_THREE = 43;
        public static final int FIFTY = 50;
        public static final int ONE_HUNDRED_AND_TWENTY_EIGHT = 128;
        public static final int TWOHUNDDRED = 200;
        public static final int FOUR_HUNDRED_AND_FIFTY = 450;
        public static final int HALF_THOUSAND = 500;
        public static final int THOUSAND = 1000;
        public static final int FIVE_MINUTE = 300000;
        public static final int THIRTY_MINUTE = 1800000;
    }

    /**
     * 数字
     */
    public static class CommonValStr {
        public static final String MINUS_ONE = "-1";
        public static final String ZERO = "0";
        public static final String ONE = "1";
        public static final String TWO = "2";
        public static final String THREE = "3";
        public static final String FOUR = "4";
        public static final String FIVE = "5";
        public static final String NINE = "9";
        public static final String TEN = "10";
        public static final String THIRTY = "30";
    }
}
