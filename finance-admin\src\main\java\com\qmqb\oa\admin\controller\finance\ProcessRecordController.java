package com.qmqb.oa.admin.controller.finance;

import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.domain.entity.SysRole;
import com.qmqb.oa.common.core.domain.model.LoginUser;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.common.enums.ProcessType;
import com.qmqb.oa.common.utils.ServletUtils;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.framework.web.service.TokenService;
import com.qmqb.oa.system.domain.ProcessRecord;
import com.qmqb.oa.system.service.ProcessRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审批处理记录Controller
 *
 * <AUTHOR>
 * @date 2021-06-07
 */
@RestController
@RequestMapping("/finance/processRecord")
public class ProcessRecordController extends BaseController {
    @Autowired
    private ProcessRecordService processRecordService;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询审批处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('finance:processRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessRecord processRecord) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        List<String> roles = loginUser.getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        List<Integer> valuesByTypes = ProcessType.getValuesByTypes(roles);
        processRecord.setProcessTypes(valuesByTypes);
        startPage();
        List<ProcessRecord> list = processRecordService.selectProcessRecordList(processRecord);
        return getDataTable(list);
    }

    /**
     * 导出审批处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('finance:processRecord:export')")
    @Log(title = "审批处理记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ProcessRecord processRecord) {
        List<ProcessRecord> list = processRecordService.selectProcessRecordList(processRecord);
        ExcelUtil<ProcessRecord> util = new ExcelUtil<ProcessRecord>(ProcessRecord.class);
        return util.exportExcel(list, "审批处理记录");
    }

    /**
     * 获取审批处理记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('finance:processRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(processRecordService.getById(id));
    }

    /**
     * 新增审批处理记录
     */
    @PreAuthorize("@ss.hasPermi('finance:processRecord:add')")
    @Log(title = "审批处理记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessRecord processRecord) {
        return toAjax(processRecordService.save(processRecord));
    }

    /**
     * 修改审批处理记录
     */
    @PreAuthorize("@ss.hasPermi('finance:processRecord:edit')")
    @Log(title = "审批处理记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessRecord processRecord) {
        return toAjax(processRecordService.updateById(processRecord));
    }

    /**
     * 删除审批处理记录
     */
    @PreAuthorize("@ss.hasPermi('finance:processRecord:remove')")
    @Log(title = "审批处理记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processRecordService.removeByIds(Arrays.asList(ids)));
    }
}
