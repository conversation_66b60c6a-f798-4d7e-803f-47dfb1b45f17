-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('离职员工', '1068', '1', 'employee', 'system/employee/index', 1, 0, 'C', '0', '0', 'system:employee:list', '#', 'admin', sysdate(), '', null, '离职员工菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('离职员工查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:employee:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('离职员工新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:employee:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('离职员工修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:employee:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('离职员工删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:employee:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('离职员工导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:employee:export',       '#', 'admin', sysdate(), '', null, '');
