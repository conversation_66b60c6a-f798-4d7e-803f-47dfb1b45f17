<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--配置spring环境变量-->
    <springProperty scope="context" name="server.port" source="server.port"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>

    <property name="logback.log.dir" value="/www/wwwlogs/${spring.application.name}"/>
    <property name="logback.application.name" value="${spring.application.name}"/>

    <property name="console.log.pattern"
              value="%red(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight([%-5level]) [%X{PtxId},%X{PspanId}] [%X{Pub-hostName}] [%X{Pub-IpStr}] %boldYellow([%X{Pub-TraceId},%X{Pub-SpanId}]) [%X{Pub-UriPath}] [%X{Pub-ModuleName}] [%X{ext}] %green([%t]) %boldMagenta([%c{0}:%L]%n) - %msg%n"/>
    <property name="log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] [%X{PtxId},%X{PspanId}] [%X{Pub-hostName}] [%X{Pub-IpStr}] [%X{Pub-TraceId},%X{Pub-SpanId}] [%X{Pub-UriPath}] [%X{Pub-ModuleName}] [%X{ext}] [%t] [%c{0}:%L] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 系统日志输出-全量日志 -->
    <appender name="file_all" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logback.log.dir}/allLog/${logback.application.name}.${server.port}.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>
                ${logback.log.dir}/allLog/${logback.application.name}.%d{yyyy-MM-dd}.${server.port}.log
            </fileNamePattern>
            <!-- 日志最大的历史 30天 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logback.log.dir}/errorLog/${logback.application.name}.error.${server.port}.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>
                ${logback.log.dir}/errorLog/${logback.application.name}.error.%d{yyyy-MM-dd}.${server.port}.log
            </fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- info异步输出 -->
    <appender name="async_all" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="file_all"/>
    </appender>

    <!-- error异步输出 -->
    <appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="file_error"/>
    </appender>

    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="async_all"/>
        <appender-ref ref="async_error"/>
    </root>

    <springProfile name="local,dev,test,testA,testB,testC,testD">
        <logger name="org.springframework" level="info"/>
        <logger name="org.springframework.web" level="error"/>
        <logger name="com.hzed.structure" level="debug"/>
        <logger name="com.baomidou" level="info"/>
        <logger name="com.baomidou.dynamic.datasource" level="debug"/>
        <logger name="com.qmqb.oa" level="debug"/>
        <logger name="com.qmqb.oa.system.mapper" level="debug"/>
    </springProfile>

    <springProfile name="prod">
        <logger name="org.springframework" level="warn"/>
        <logger name="org.springframework.web" level="error"/>
        <logger name="com.hzed.structure" level="info"/>
        <logger name="com.qmqb.oa" level="info"/>
        <logger name="com.qmqb.oa.system.mapper" level="warn"/>
    </springProfile>

</configuration>
