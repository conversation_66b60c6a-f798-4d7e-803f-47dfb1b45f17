package com.qmqb.oa.admin.support;

import cn.hutool.core.date.DateUtil;
import com.hzed.structure.notice.service.IDingTalkService;
import com.qmqb.oa.common.utils.ExceptionUtil;
import com.qmqb.oa.common.utils.spring.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FinancialRobotWatch {

    private final IDingTalkService dingTalkService;

    /**
     * 告警模板
     */
    private final static String WARN_TEMPLATE = "告警通知\n" +
            "来源：%s\n" +
            "触发时间：%s\n" +
            "摘要：%s\n" +
            "描述：%s\n" +
            "监控：%s\n" +
            "详情：\n" +
            "%s";

    /**
     * 钉钉预警
     *
     * @param processInstanceId
     * @param e
     */
    public void warn(String processInstanceId, Exception e) {
        try {
            String activeProfile = SpringUtils.getActiveProfile();
            String applicationName = SpringUtils.getApplicationName();
            String now = DateUtil.now();
            String warn = String.format(WARN_TEMPLATE, applicationName, now, activeProfile,
                    String.format("执行审批实例操作异常：实例ID[%s]", processInstanceId), "FinancialRobotWatch", ExceptionUtil.getExceptionMessage(e));
            dingTalkService.sendTextMessage(warn);
        } catch (Exception ignore) {
            log.error("钉钉预警异常", ignore);
        }
    }
}
