package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.DingTalkClockResult;
import com.qmqb.oa.system.domain.vo.ClockStatisticsVo;

import java.util.List;

/**
 * 钉钉打卡结果Service接口
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
public interface DingTalkClockResultService extends IService<DingTalkClockResult> {
    /**
     * 查询钉钉打卡结果
     *
     * @param id 钉钉打卡结果ID
     * @return 钉钉打卡结果
     */
    DingTalkClockResult selectDingtalkClockResultById(Long id);

    /**
     * 查询钉钉打卡结果列表
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 钉钉打卡结果集合
     */
    List<DingTalkClockResult> selectDingtalkClockResultList(DingTalkClockResult dingtalkClockResult);

    /**
     * 新增钉钉打卡结果
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 结果
     */
    int insertDingtalkClockResult(DingTalkClockResult dingtalkClockResult);

    /**
     * 修改钉钉打卡结果
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 结果
     */
    int updateDingtalkClockResult(DingTalkClockResult dingtalkClockResult);

    /**
     * 批量删除钉钉打卡结果
     *
     * @param ids 需要删除的钉钉打卡结果ID
     * @return 结果
     */
    int deleteDingtalkClockResultByIds(Long[] ids);

    /**
     * 删除钉钉打卡结果信息
     *
     * @param id 钉钉打卡结果ID
     * @return 结果
     */
    int deleteDingtalkClockResultById(Long id);

    List<ClockStatisticsVo> statistics(DingTalkClockResult dingTalkClockResult);
}
