package com.qmqb.oa.admin.controller.hrm;

import com.qmqb.oa.system.domain.vo.ClockStatisticsVo;
import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.page.PageDomain;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.core.page.TableSupport;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.system.domain.DingTalkClockResult;
import com.qmqb.oa.system.service.DingTalkClockResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 钉钉打卡结果Controller
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@Api(tags = "钉钉打卡结果Controller")
@RestController
@RequestMapping("/hrm/result")
public class DingTalkClockResultController extends BaseController {
    @Autowired
    private DingTalkClockResultService dingtalkClockResultService;

    /**
     * 查询钉钉打卡结果列表
     */
    @PreAuthorize("@ss.hasPermi('hrm:result:list')")
    @GetMapping("/list")
    public TableDataInfo list(DingTalkClockResult dingtalkClockResult) {
        startPage();
        List<DingTalkClockResult> list = dingtalkClockResultService.selectDingtalkClockResultList(dingtalkClockResult);
        return getDataTable(list);
    }

    /**
     * 导出钉钉打卡结果列表
     */
    @PreAuthorize("@ss.hasPermi('hrm:result:export')")
    @Log(title = "钉钉打卡结果", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DingTalkClockResult dingtalkClockResult) {
        List<DingTalkClockResult> list = dingtalkClockResultService.selectDingtalkClockResultList(dingtalkClockResult);
        ExcelUtil<DingTalkClockResult> util = new ExcelUtil<DingTalkClockResult>(DingTalkClockResult.class);
        return util.exportExcel(list, "result");
    }

    /**
     * 获取钉钉打卡结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('hrm:result:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dingtalkClockResultService.selectDingtalkClockResultById(id));
    }

    /**
     * 新增钉钉打卡结果
     */
    @PreAuthorize("@ss.hasPermi('hrm:result:add')")
    @Log(title = "钉钉打卡结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DingTalkClockResult dingtalkClockResult) {
        return toAjax(dingtalkClockResultService.insertDingtalkClockResult(dingtalkClockResult));
    }

    /**
     * 修改钉钉打卡结果
     */
    @PreAuthorize("@ss.hasPermi('hrm:result:edit')")
    @Log(title = "钉钉打卡结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DingTalkClockResult dingtalkClockResult) {
        return toAjax(dingtalkClockResultService.updateDingtalkClockResult(dingtalkClockResult));
    }

    /**
     * 删除钉钉打卡结果
     */
    @PreAuthorize("@ss.hasPermi('hrm:result:remove')")
    @Log(title = "钉钉打卡结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dingtalkClockResultService.deleteDingtalkClockResultByIds(ids));
    }

    /**
     * 统计
     */
    @ApiOperation("统计")
    @PreAuthorize("@ss.hasPermi('hrm:result:statistics')")
    @GetMapping("/statistics")
    public TableDataInfo statistics(DingTalkClockResult dingTalkClockResult) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<ClockStatisticsVo> list = dingtalkClockResultService.statistics(dingTalkClockResult);
        return page(list, pageNum, pageSize);
    }

}
