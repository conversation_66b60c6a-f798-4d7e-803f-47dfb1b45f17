package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 易快报到钉钉消息二次处理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
public interface EkbDingTodoMessageService extends IService<EkbDingTodoMessage> {
    /**
     * 根据流程和消息id查找用户唯一的消息记录
     * @param flowId
     * @param messageId
     * @param dingUnionId
     * @return
     */
    EkbDingTodoMessage findOneByFlowIdAndMessageId(String flowId, String messageId, String dingUnionId);

    /**
     * 根据流程和ekbMsgAction查找用户唯一的消息记录
     * @param flowId
     * @param ekbMsgAction
     * @return
     */
    EkbDingTodoMessage findOneByFlowIdAndEkbMsgAction(String flowId, String ekbMsgAction);

    /**
     * 根据流程和ekbMsgAction查找用户唯一的消息记录
     * @param flowId
     * @param ekbMsgAction
     * @param dingUnionId
     * @return
     */
    List<EkbDingTodoMessage> findListByFlowIdAndEkbMsgAction(String flowId, String ekbMsgAction, String dingUnionId);


    /**
     * 根据流程id 和ekbMsgAction 列表查询dingTodoMessage
     * @param flowId
     * @param ekbMsgActions
     * @return
     */
    List<EkbDingTodoMessage> findListByFlowIdAndEkbMsgActions(String flowId, List<String> ekbMsgActions);


    /**
     * 修改remark字段扩展信息
     * @param ids
     * @param params
     * @return
     */
    int setMultiRemarkInfo(Long[] ids, @Param("params") Map<String, Object> params);

    /**
     * 根据单据流程id和用户id集合查询待办任务列表
     * @param flowId
     * @param userIdSet
     * @return
     */
    List<EkbDingTodoMessage> todoListByFlowIdAndUserIdSet(String flowId, Set<String> userIdSet);
}
