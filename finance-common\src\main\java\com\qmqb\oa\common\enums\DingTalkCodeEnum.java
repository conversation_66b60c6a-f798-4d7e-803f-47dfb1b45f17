package com.qmqb.oa.common.enums;

/**
 * 钉钉错误码
 *
 * <AUTHOR>
 */
public enum DingTalkCodeEnum {


    /**
     * 钉钉错误码
     */
    INVALID_CLIENTID_OR_SECRET("invalidClientIdOrSecret", "无效的clientId或者clientSecret"),
    INVALID_AUTHENTICATION("InvalidAuthentication", "不合法的access_token"),
    ACCESS_TOKEN_PERMISSION_DENIED("Forbidden.AccessDenied.AccessTokenPermissionDenied", "没有调用该接口的权限"),
    IP_NOT_IN_WHITELIST("Forbidden.AccessDenied.IpNotInWhiteList", "访问ip不在白名单之中"),
    QPS_LIMIT_FOR_API("Forbidden.AccessDenied.QpsLimitForApi", "您的服务器调用钉钉开放平台当前接口的所有请求都被暂时禁用了"),
    QPS_LIMIT_FOR_APPKEY_AND_API("Forbidden.AccessDenied.QpsLimitForAppkeyAndApi", "您的企业应用调用钉钉开放平台当前接口的所有请求都被暂时禁用了"),
    QPM_LIMIT_FOR_APPKEY_AND_API("Forbidden.AccessDenied.QpmLimitForAppkeyAndApi", "您的企业应用调用钉钉开放平台当前接口的所有请求都被暂时禁用了"),

    ;

    /**
     * 错误码
     */
    private final String code;
    /**
     * 错误信息
     */
    private final String message;

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    DingTalkCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
}

