<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.SysEventMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.SysEvent">
        <id column="id" property="id" />
        <result column="request_no" property="requestNo" />
        <result column="event_source" property="eventSource" />
        <result column="company_id" property="companyId" />
        <result column="event_type" property="eventType" />
        <result column="req_data" property="reqData" />
        <result column="req_params" property="reqParams" />
        <result column="event_time" property="eventTime" />
        <result column="status" property="status" />
        <result column="fail_count" property="failCount" />
        <result column="fail_reason" property="failReason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>



</mapper>
