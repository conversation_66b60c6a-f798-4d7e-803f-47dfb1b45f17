package com.qmqb.oa.admin.service.hsfk;

import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import shade.com.alibaba.fastjson2.JSONObject;

import java.util.Objects;

/**
 * 通讯录企业删除部门事件
 *
 * <AUTHOR>
 * @date 2024/8/31
 */
@Slf4j
@Service
public class DingDeptRemoveOrgServiceImpl implements DingTalkEventPolicyPatternService{

    @Autowired
    private DingTalkDeptService dingTalkDeptService;
    @Autowired
    private SysEventService sysEventService;

    @Override
    public Boolean isCeLueModel(String type) {
        return Objects.equals(DingTalkEventType.DEPT_DELETE.getEventType(), type);
    }

    @Override
    public void toHandleEvent(JSONObject bizData, Integer companyId) {
        dingTalkDeptService.deleteDept(bizData, companyId);
    }

    @Override
    public void toExecuteEvent(SysEvent sysEvent) {
        JSONObject bizData = JSONObject.parseObject(sysEvent.getReqData());
        dingTalkDeptService.deleteDept(bizData, sysEvent.getCompanyId());
        sysEventService.update4SuccessById(sysEvent);
    }
}