<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.EkbDingUserMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.EkbDingUserMapping">
        <id column="id" property="id" />
        <result column="ding_user_id" property="dingUserId" />
        <result column="ding_user_name" property="dingUserName" />
        <result column="ding_dept_id_list" property="dingDeptIdList" />
        <result column="mobile" property="mobile" />
        <result column="ekb_user_id" property="ekbUserId" />
        <result column="ekb_user_name" property="ekbUserName" />
        <result column="ekb_default_dept_id" property="ekbDefaultDeptId" />
        <result column="ekb_dept_id_list" property="ekbDeptIdList" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ding_user_id, ding_user_name, ding_dept_id_list, mobile, ekb_user_id, ekb_user_name, ekb_default_dept_id, ekb_dept_id_list, remark, create_time, update_time
    </sql>
    
    
    <select id="selectUserIdAndCompanyDeptId" resultType="com.qmqb.oa.system.domain.vo.RoleUserInfoVo">
        select ekb.ekb_user_id as ekbUserId, teddm.ekb_dept_id as ekbRoleDeptId
        from t_ekb_ding_user_mapping ekb
                 join t_ding_user tdu on ekb.ding_user_id = tdu.user_id
                 join t_ekb_ding_dept_mapping teddm on tdu.company_id = teddm.company_id
        where ekb.ekb_user_id is not null and teddm.ding_dept_level = 1
    </select>
    <select id="findOneByEkbUserId" resultType="com.qmqb.oa.system.domain.EkbDingUserMapping">
        select um.*
        from t_ekb_ding_user_mapping um
        inner join t_ding_user u on um.ding_user_id = u.user_id
        where um.ekb_user_id = #{ekbUserId}
        order by u.company_id asc
        limit 1
    </select>


</mapper>
