package com.qmqb.oa.admin.controller.finance;

import com.hzed.structure.common.util.AssertUtil;
import com.hzed.structure.common.util.IdUtil;
import com.qmqb.oa.admin.task.ReimbursementProcessTask;
import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.system.domain.ReimbursementVoucher;
import com.qmqb.oa.system.service.ReimbursementVoucherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报销凭证Controller
 *
 * <AUTHOR>
 * @date 2021-05-21
 */
@RestController
@RequestMapping("/finance/voucher")
public class ReimbursementVoucherController extends BaseController {

    @Autowired
    private ReimbursementVoucherService reimbursementVoucherService;
    @Autowired
    private ReimbursementProcessTask reimbursementProcessTask;

    /**
     * 查询报销凭证列表
     */
    @PreAuthorize("@ss.hasPermi('finance:voucher:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReimbursementVoucher reimbursementVoucher) {
        startPage();
        List<ReimbursementVoucher> list = reimbursementVoucherService.selectReimbursementVoucherList(reimbursementVoucher);
        return getDataTable(list);
    }

    /**
     * 统计报销金额
     */
    @GetMapping("/count")
    public AjaxResult count(ReimbursementVoucher reimbursementVoucher) {
        List<ReimbursementVoucher> list = reimbursementVoucherService.selectReimbursementVoucherList(reimbursementVoucher);
        int size = list.size();
        BigDecimal total = list.stream().map(ReimbursementVoucher::getDomesticCurrency).reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<String, Object> data = new HashMap<>(2);
        data.put("count", size);
        data.put("total", total);
        return AjaxResult.success(data);
    }

    /**
     * 导出报销凭证列表
     */
    @PreAuthorize("@ss.hasPermi('finance:voucher:export')")
    @Log(title = "报销凭证", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ReimbursementVoucher reimbursementVoucher) {
        AssertUtil.notNull(reimbursementVoucher.getLoanSubjectCode(), "请选择科目编码");
        AssertUtil.notNull(reimbursementVoucher.getExportMakeDate(), "请选择制表日期");
        AssertUtil.notNull(reimbursementVoucher.getTenantId(), "请选择公司");
        return reimbursementProcessTask.export(reimbursementVoucher);
    }

    /**
     * 获取报销凭证详细信息
     */
    @PreAuthorize("@ss.hasPermi('finance:voucher:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(reimbursementVoucherService.getById(id));
    }

    /**
     * 新增报销凭证
     */
    @PreAuthorize("@ss.hasPermi('finance:voucher:add')")
    @Log(title = "报销凭证", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ReimbursementVoucher reimbursementVoucher) {
        reimbursementVoucher.setProcessInstanceId("manual-" + IdUtil.getMillisecond());
        return toAjax(reimbursementVoucherService.save(reimbursementVoucher));
    }

    /**
     * 修改报销凭证
     */
    @PreAuthorize("@ss.hasPermi('finance:voucher:edit')")
    @Log(title = "报销凭证", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ReimbursementVoucher reimbursementVoucher) {
        return toAjax(reimbursementVoucherService.updateById(reimbursementVoucher));
    }

    /**
     * 删除报销凭证
     */
    @PreAuthorize("@ss.hasPermi('finance:voucher:remove')")
    @Log(title = "报销凭证", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(reimbursementVoucherService.removeByIds(Arrays.asList(ids)));
    }
}
