package com.qmqb.oa.admin.config;


import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Description:
 * <AUTHOR>
 * @since 2019/5/21
 */
@Data
@Configuration
public class CommonConfig implements InitializingBean {

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${server.port}")
    private String port;

    private String hostName;

    /**
     * @Description: TODO(判断是生产环境)
     * @return 
     * @date 2018年9月4日 上午11:14:07
     */
    public boolean isProdEnv() {
    	return "prod".equals(env);
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }



}
