package com.qmqb.oa.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * 请求日志配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Data
@Configuration
@ConfigurationProperties("request.log")
public class RequestLogConfig {
    /**
     * 不希望记录日志的URL Pattern, 如果有多个可以采用英文半角逗号(",")分隔; 单个Pattern语法支持AntPathMatcher匹配。
     */
    private String[] excludePatterns;
    /**
     * 不需要记录日志的请求method。譬如通常可以忽略的method: OPTIONS
     */
    private String[] excludeMethods;
}
