package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.DingUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 钉钉员工表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface DingUserService extends IService<DingUser> {

    /**
     * 根据用户id
     * @param userId
     * @return
     */
    List<DingUser> listByUserid(String userId);

    /**
     * 根据用户名称及手机号查找钉钉用户列表
     * @param userName
     * @param mobile
     * @return
     */
    List<DingUser> listByUserNameAndMobile(String userName, String mobile);

    /**
     * 根据 companyId 和 unionId查找用户
     * @param companyId
     * @param unionId
     * @return
     */
    DingUser findOneByCompanyIdAndUnionId(Integer companyId, String unionId);

    /**
     * 根据手机号查找钉钉用户
     * @param mobile
     * @return
     */
    List<DingUser> listByMobile(String mobile);
}
