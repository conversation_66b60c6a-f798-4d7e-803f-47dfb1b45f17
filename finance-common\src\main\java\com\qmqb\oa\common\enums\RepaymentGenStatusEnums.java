package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 还款生成记录状态枚举
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum RepaymentGenStatusEnums {
    /**
     * 生成记录的初始化状态
     */
    INITED("初始化", 0),
    /**
     * 已经处理完数据，并且存入详情表中
     */
    FINISHED("BI处理完数据状态", 1),
    ;
    /**
     * 描述
     */
    private String desc;
    /**
     * 状态
     */
    private Integer status;
}
