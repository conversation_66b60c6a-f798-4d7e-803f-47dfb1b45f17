package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.mapper.DingUserMapper;
import com.qmqb.oa.system.service.DingUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 钉钉员工表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class DingUserServiceImpl extends ServiceImpl<DingUserMapper, DingUser> implements DingUserService {
    @Override
    public List<DingUser> listByUserid(String userId) {
        LambdaQueryWrapper<DingUser> queryWrapper = Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getUserId, userId);
        return list(queryWrapper);
    }

    @Override
    public List<DingUser> listByUserNameAndMobile(String userName, String mobile) {
        LambdaQueryWrapper<DingUser> queryWrapper = Wrappers.lambdaQuery(DingUser.class)
                .eq(DingUser::getUserName, userName)
                .eq(DingUser::getMobile, mobile);
        return list(queryWrapper);
    }

    /**
     * 根据 companyId 和 unionId查找用户
     * @param companyId
     * @param unionId
     * @return
     */
    @Override
    public DingUser findOneByCompanyIdAndUnionId(Integer companyId, String unionId) {
        LambdaQueryWrapper<DingUser> queryWrapper = Wrappers.lambdaQuery(DingUser.class)
                .eq(DingUser::getCompanyId, companyId)
                .eq(DingUser::getUnionId, unionId);
        return getOne(queryWrapper);
    }

    /**
     * 根据手机号查找钉钉用户
     *
     * @param mobile
     * @return
     */
    @Override
    public List<DingUser> listByMobile(String mobile) {
        LambdaQueryWrapper<DingUser> queryWrapper = Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getMobile, mobile);
        return list(queryWrapper);
    }
}
