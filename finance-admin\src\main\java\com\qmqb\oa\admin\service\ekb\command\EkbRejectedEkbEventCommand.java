package com.qmqb.oa.admin.service.ekb.command;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hzed.structure.common.api.ResultCode;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.tool.api.ApiResponse;
import com.qmqb.oa.admin.domain.dto.EkbCurrentApprove;
import com.qmqb.oa.admin.domain.dto.EkbEventCommandDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbDingTodoResultDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.common.enums.EkbActionEnum;
import com.qmqb.oa.common.enums.EkbFlowStageNameEnums;
import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Description ekb 拒绝事件
 * @Date 2024\10\12 0012 10:50
 * @Version 1.0
 */

@Slf4j
@Service(value = "ekbRejectedCommand")
public class EkbRejectedEkbEventCommand extends AbstractEkbEventCommand<EkbReceiveEkbEventRequest, EkbEventCommandDTO> {

    @Override
    public boolean validation() {
        if (!super.checkBaseParams()) {
            return false;
        }

        if (!getParamObject().getAction().equals(EkbActionEnum.FLOW_REJECTED.getAction())) {
            return false;
        }

        return true;
    }

    @Override
    public ApiResponse<EkbEventCommandDTO> execute() {
        if (!validation()) {
            return ApiResponse.fail("reject事件参数校验失败");
        }
        if (eventHasExecute()) {
            return ApiResponse.success(ResultCode.SUCCESS, "ekb reject event deal success");
        }

        EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();
        List<String> msgActions = Lists.newArrayList(EkbActionEnum.BACKLOG_APPROVING.getAction(), EkbActionEnum.BACKLOG_PAYING.getAction());

        List<EkbDingTodoMessage> currentApprovingTodoMsgs = getCurrentDealTodoMsgList(request.getFlowId(), msgActions);
        if (CollUtil.isEmpty(currentApprovingTodoMsgs)) {
            return ApiResponse.fail("reject事件未找到对应的待审批数据列表");
        }
        //查询出当前审批节点的所有审批人的未处理的待办任务列表
        List<EkbDingTodoMessage> necessaryDealEkbTasks = getUnHandlerTodoListOnSameNode(request, eventCommandDTO);

        //更新易快报到钉钉待办消息表钉钉发送状态
        Map<Long, Boolean> batchUpdateResult = updateTodoDingMsg(request, necessaryDealEkbTasks);
        ApiResponse apiResponse = null;
        Long[] taskId = new Long[1];
        boolean isExistsExecuteFailure = CollUtil.isNotEmpty(batchUpdateResult);
        for (Map.Entry<Long, Boolean> updateResult : batchUpdateResult.entrySet()) {
            if (updateResult.getValue()) {
                taskId[0] = updateResult.getKey();
                //更新待办消息结果处理状态
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build().
                        setTaskProcessedResult(EkbFlowStageNameEnums.REJECT.getDbCode())
                        .setMsgAction(EkbActionEnum.FLOW_REJECTED.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
            } else {
                //失败的记录下最后执行的action
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build()
                        .setMsgAction(EkbActionEnum.FLOW_REJECTED.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
                isExistsExecuteFailure = true;
            }
        }

        //存在执行失败的任务
        if (isExistsExecuteFailure) {
            apiResponse = ApiResponse.fail();
        } else {
            apiResponse = ApiResponse.success(ResultCode.SUCCESS, "ekb rejected event deal success");
        }

        //设置处理后的事件传输实体
        apiResponse.setData(getEkbEventCommandDTO());
        return apiResponse;

    }

    public static void main(String[] args) {
        String s = "{\"submitDate\":null,\"userInfo\":{\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSCbZ\",\"name\":\"欧阳滢\",\"cellphone\":\"18520628822\",\"email\":\"\"},\"formSpecification\":{\"specificationId\":\"ID01zWgGLb7MJ1:4ef54915adbaf5e1e0d007e4498e331a48a936ab\",\"specificationName\":\"306-7.1退款\"},\"法人实体\":{\"id\":\"ID01zPEcMjsMb5\",\"code\":\"132\",\"name\":\"共赢非融资性担保（深圳）有限公司\",\"path\":\"共赢非融资性担保（深圳）有限公司\"},\"corporationId\":\"ID01zNdgxCRa06\",\"submitterId\":{\"name\":\"潘思敏\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSYzl\"},\"action\":\"backlog.processed\",\"messageId\":\"ID01EOr4kd6YJ9\",\"state\":\"paying\",\"flowId\":\"ID01EOr8alZmdp\",\"nodeId\":\"FLOW:1103561171:1194857705\",\"currentApprovers\":[{\"name\":\"欧阳滢\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSCbZ\"},{\"name\":\"赖菲菲\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSARp\"},{\"name\":\"曾怡\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSIAn\"},{\"name\":\"张钦娜\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSOIf\"}],\"preNodeApprovedTime\":1733107443532,\"actionName\":\"已处理\",\"details\":[{\"feeTypeId\":\"ID01zRktGtGN1t\",\"feeTypeForm\":{\"amount\":{\"standard\":\"1111\",\"standardUnit\":\"元\",\"standardScale\":2,\"standardSymbol\":\"￥\",\"standardNumCode\":\"156\",\"standardStrCode\":\"CNY\"},\"detailId\":\"ID01EOr8alZmKr\",\"detailNo\":1,\"invoiceForm\":null,\"u_合同台账\":null},\"specificationId\":\"ID01zRktGtGN1t:expense:7a1547247a4f67fb5153e25e19359afe2f3f4faa\"}]}";
        JSONObject jsonObject = JSON.parseObject(s);
        JSONArray currentApprovers = jsonObject.getJSONArray("currentApprovers");
        for (Object obj : currentApprovers) {
            EkbCurrentApprove currentApprove = JSONObject.parseObject(JSON.toJSONString(obj),EkbCurrentApprove.class);
            System.out.println(currentApprove.getId() + "-" + currentApprove.getName());
        }
        boolean ebot = currentApprovers.contains("ebot");
        System.out.println(ebot);
        System.out.println(JSON.toJSONString(currentApprovers));
        List<String> userList = currentApprovers.toJavaList(String.class);
        Set<String> set = new HashSet<>();
        set.add("{\"name\":\"张钦娜\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSOIf\"}");
        set.addAll(userList);
        System.out.println(set);
    }

}
