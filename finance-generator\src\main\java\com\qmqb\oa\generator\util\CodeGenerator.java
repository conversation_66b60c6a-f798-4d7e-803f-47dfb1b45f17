package com.qmqb.oa.generator.util;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.util.ArrayList;
import java.util.List;

/**
 * mybatis-plus代码生成器
 *
 * <AUTHOR>
 */
public class CodeGenerator {


    public static void main(String[] args) {
        List<String> tableNameList = new ArrayList<>();
        //表名
        tableNameList.add("t_ekb_ding_twice_message");
        tableNameList.add("t_ekb_message");
//        tableNameList.add("t_ekb_user_tmp");
        CodeGenerator.generator(tableNameList);
    }


    /**
     * 代码生成器
     *
     * @param tableNameList 表名
     */
    private static void generator(List<String> tableNameList) {
        AutoGenerator autoGenerator = new AutoGenerator();

        PackageConfig packageConfig = new PackageConfig();
        packageConfig.setParent("com.qmqb.oa.system");
        packageConfig.setEntity("domain");

        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDbQuery(new MySqlQuery());
        dataSourceConfig.setDbType(DbType.MYSQL);
        dataSourceConfig.setTypeConvert(new MySqlTypeConvert());
        dataSourceConfig.setUrl("****************************************************************************************************************************************************************************");
        dataSourceConfig.setDriverName("com.mysql.cj.jdbc.Driver");
        dataSourceConfig.setUsername("u_account_auto");
        dataSourceConfig.setPassword("u_account_auto");

        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setTablePrefix("t_");
        strategyConfig.setInclude(tableNameList.toArray(new String[0]));
        strategyConfig.setCapitalMode(true);
        strategyConfig.setNaming(NamingStrategy.underline_to_camel);
        strategyConfig.setColumnNaming(NamingStrategy.underline_to_camel);
        strategyConfig.setEntitySerialVersionUID(true);
        strategyConfig.setChainModel(true);
        strategyConfig.setEntityLombokModel(true);
        strategyConfig.setEntityBooleanColumnRemoveIsPrefix(true);
        strategyConfig.setVersionFieldName("version");
        strategyConfig.setLogicDeleteFieldName("is_deleted");

        TemplateConfig template = new TemplateConfig();
        template.setXml(null);
        template.setController(null);

        //直接生成到finance-system类对应的位置
        String outPath = CodeGenerator.class.getResource("/").getPath();
        String target = outPath.substring(0, outPath.lastIndexOf("finance"));
//        String target = "F:\\www";
        String javaOutPath = target + "/finance-system/src/main/java/";
        String xmlOutPath = target + "/finance-system/src/main/resources/mapper/";

        List<FileOutConfig> fileOutConfigList = new ArrayList<>();
        //调整xml生成目录
        fileOutConfigList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return xmlOutPath + tableInfo.getEntityName() + "Mapper.xml";
            }
        });
        InjectionConfig injectionConfig = new InjectionConfig() {
            @Override
            public void initMap() {

            }
        };
        injectionConfig.setFileOutConfigList(fileOutConfigList);

        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setOutputDir(javaOutPath);
        globalConfig.setOpen(false);
        globalConfig.setBaseResultMap(true);
        globalConfig.setBaseColumnList(true);
        globalConfig.setMapperName("%sMapper");
        globalConfig.setServiceName("%sService");
        globalConfig.setServiceImplName("%sServiceImpl");
        globalConfig.setAuthor("qmqb");
        globalConfig.setIdType(IdType.ASSIGN_ID);
        globalConfig.setDateType(DateType.TIME_PACK);

        ConfigBuilder configBuilder = new ConfigBuilder(packageConfig, dataSourceConfig, strategyConfig, template, globalConfig);
        autoGenerator.setCfg(injectionConfig);
        autoGenerator.setConfig(configBuilder);
        autoGenerator.execute();
    }
}
