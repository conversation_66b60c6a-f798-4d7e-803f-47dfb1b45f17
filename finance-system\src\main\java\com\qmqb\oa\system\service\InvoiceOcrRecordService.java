package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.InvoiceOcrRecord;

import java.util.List;

/**
 * <p>
 * 发票OCR识别记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
public interface InvoiceOcrRecordService extends IService<InvoiceOcrRecord> {

    /**
     * 根据发票代码查询已核销发票OCR记录
     *
     * @param invoiceCode   发票代码
     * @param invoiceNumber 发票号码
     * @param sheet         联次
     * @return
     */
    Boolean checkRepeatInvoice(String invoiceCode, String invoiceNumber, String sheet);

    /**
     * 根据审批实例ID查询发票OCR记录
     *
     * @param processInstanceId 审批实例ID
     * @return
     */
    List<InvoiceOcrRecord> listByProcessInstanceId(String processInstanceId);

    /**
     * 查询发票识别记录列表
     *
     * @param invoiceOcrRecord 发票识别记录
     * @return 发票识别记录集合
     */
    List<InvoiceOcrRecord> selectInvoiceOcrRecordList(InvoiceOcrRecord invoiceOcrRecord);

    /**
     * 根据审批实例id和金额获取发票
     * @param processInstanceId
     * @param amount
     * @return
     */
    InvoiceOcrRecord getOneByProcessInstanceIdAndAmount(String processInstanceId, String amount);
}
