package ${packageName}.service.impl;

import java.util.List;
    #foreach ($column in $columns)
        #if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
        import com.qmqb.oa.common.utils.DateUtils;
            #break
        #end
    #end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
    #if($table.sub)
    import java.util.ArrayList;

    import com.qmqb.oa.common.utils.StringUtils;
    import org.springframework.transaction.annotation.Transactional;
    import ${packageName}.domain.${subClassName};
    #end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}ID
     * @return ${functionName}
     */
    @Override
    public ${ClassName} select${ClassName}ById(${pkColumn.javaType} ${pkColumn.javaField}) {
        return ${className}Mapper.select${ClassName}ById(${pkColumn.javaField});
    }

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> select${ClassName}List(${ClassName} ${className}) {
        return ${className}Mapper.select${ClassName}List(${className});
    }

    /**
     * 新增${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int insert${ClassName}(${ClassName} ${className}) {
        #foreach ($column in $columns)
            #if($column.javaField == 'createTime')
                ${className}.setCreateTime(DateUtils.getNowDate());
            #end
        #end
        #if($table.sub)
            int rows = ${className}Mapper.insert${ClassName}(${className});
            insert${subClassName}(${className});
            return rows;
        #else
            return ${className}Mapper.insert${ClassName}(${className});
        #end
    }

    /**
     * 修改${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int update${ClassName}(${ClassName} ${className}) {
        #foreach ($column in $columns)
            #if($column.javaField == 'updateTime')
                ${className}.setUpdateTime(DateUtils.getNowDate());
            #end
        #end
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${className}.get${pkColumn.capJavaField}())
            ;
            insert${subClassName}(${className});
        #end
        return ${className}Mapper.update${ClassName}(${className});
    }

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}ID
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int delete${ClassName}ByIds(${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}s(${pkColumn.javaField}s);
        #end
        return ${className}Mapper.delete${ClassName}ByIds(${pkColumn.javaField}s);
    }

    /**
     * 删除${functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}ID
     * @return 结果
     */
    @Override
    public int delete${ClassName}ById(${pkColumn.javaType} ${pkColumn.javaField}) {
        #if($table.sub)
                ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${pkColumn.javaField});
        #end
        return ${className}Mapper.delete${ClassName}ById(${pkColumn.javaField});
    }
    #if($table.sub)

        /**
         * 新增${subTable.functionName}信息
         *
         * @param ${className} ${functionName}对象
         */
        public void insert${subClassName}(${ClassName} ${className}) {
            List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
            Long ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
            if (StringUtils.isNotNull(${subclassName}List)) {
                List<${subClassName}> list = new ArrayList<${subClassName}>();
                for (${subClassName} ${subclassName} :${subclassName}List)
                {
                    ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                    list.add(${subclassName});
                }
                if (list.size() > 0) {
                        ${className}Mapper.batch${subClassName}(list);
                }
            }
        }
    #end

}
