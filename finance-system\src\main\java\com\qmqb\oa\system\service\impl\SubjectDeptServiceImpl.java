package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.system.domain.SubjectDept;
import com.qmqb.oa.system.mapper.SubjectDeptMapper;
import com.qmqb.oa.system.service.SubjectDeptService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;
import java.util.function.Function;

/**
 * <p>
 * 科目部门关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Service
public class SubjectDeptServiceImpl extends ServiceImpl<SubjectDeptMapper, SubjectDept> implements SubjectDeptService {


    @Override
    public boolean deleteSubjectDeptBySubjectIds(Long[] ids) {
        QueryWrapper<SubjectDept> wrapper = new QueryWrapper<>();
        wrapper.in("subject_id", Arrays.asList(ids));
        return this.remove(wrapper);
    }

    public boolean checkSubjectDeptBySubjectIds(Long[] ids) {
        QueryWrapper<SubjectDept> wrapper = new QueryWrapper<>();
        wrapper.in("subject_id", Arrays.asList(ids));
        wrapper.last("limit 1");
        return !Objects.isNull(getObj(wrapper, Function.identity()));
    }
}
