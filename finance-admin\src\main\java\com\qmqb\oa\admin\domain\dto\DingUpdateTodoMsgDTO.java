package com.qmqb.oa.admin.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 钉钉更新待办消息接口请求实体
 * <AUTHOR>
 * @date 2024/10/11
 */
@Data
@Accessors(chain = true)
@Builder
public class DingUpdateTodoMsgDTO {
    /**
     * 易快报消息id
     */
    private String messageId;
    /**
     * 我方公司主体
     */
    private Integer companyId;
    /**
     * 钉钉待办消息id
     */
    private String taskId;

    /**
     * 创建待办任务的钉unionId
     */
    private String unionId;

    /**
     * 待办接收人的unionIds
     */
    private List<String> recipientUnionIds;

    /**
     * 待办内容
     */
    private String content;

    private Boolean isDone;

}
