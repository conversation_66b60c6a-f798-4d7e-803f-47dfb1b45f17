package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.hzed.structure.common.util.BeanUtil;
import com.hzed.structure.common.util.ReflectUtil;
import com.taobao.api.ApiException;
import com.taobao.api.internal.mapping.ApiField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/16
 */
@Slf4j
@Component
public class H5AppDingClientService {
    private static com.aliyun.dingtalkoauth2_1_0.Client oauth_client;

    static {
        createClient();
    }

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static void createClient() {
        try {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
            config.protocol = "https";
            config.regionId = "central";
            oauth_client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
        } catch (Exception e) {
            log.error("init createClient fail", e);
        }
    }


    /**
     * 获取accessToken
     *
     * @param appKey
     * @param appSecret
     * @return
     * @throws Exception
     */
    public String getAccessToken(String appKey, String appSecret) {
        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest req = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                .setAppKey(appKey)
                .setAppSecret(appSecret);
        try {
            log.info("[DingTalk API] 获取企业内部应用的accessToken, 请求参数:{}", JSON.toJSONString(req));
            GetAccessTokenResponse rsp = oauth_client.getAccessToken(req);
            log.info("[DingTalk API] 获取企业内部应用的accessToken, 响应参数:{}", JSON.toJSONString(rsp));
            return rsp.body.accessToken;
        } catch (Exception e) {
            return null;
        }

    }

    /**
     * 根据免登码获取用户详情
     *
     * @param authCode
     * @param accessToken
     * @return
     * @throws Exception
     */
    public OapiV2UserGetuserinfoResponse getUserInfoByAuthCode(String authCode, String accessToken) throws Exception {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(authCode);
        OapiV2UserGetuserinfoResponse rsp = client.execute(req, accessToken);
        log.info("钉钉用户详情：{}", rsp.getBody());
        return rsp;
    }

    /**
     * 根据userId查询用户详情
     *
     * @param userId
     * @param accessToken
     * @throws Exception
     */
    public OapiV2UserGetResponse.UserGetResponse getUserDetailByUserId(String userId, String accessToken) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");
        try {
            OapiV2UserGetResponse rsp = client.execute(req, accessToken);
            log.info("钉钉用户详情：{}", JSON.toJSONString(rsp.getBody()));
            if (!rsp.isSuccess()) {
                log.error("根据userId查询用户详情失败，userId:{},返回code:{},返回msg:{}", userId, rsp.getErrcode(), rsp.getErrmsg());
                return null;
            } else {
                return rsp.getResult();
            }
        } catch (ApiException e) {
            log.error("根据userId查询用户详情失败", e);
            return null;
        }
    }


    /**
     * 根据部门ID获取用户列表
     * 该方法通过调用钉钉API获取指定部门下的所有用户信息
     *
     * @param deptId      部门ID，用于指定需要获取用户列表的部门
     * @param accessToken 钉钉开放平台访问令牌，用于验证身份
     * @return 返回用户列表，如果获取失败则返回null
     */
    public List<OapiV2UserListResponse.ListUserResponse> getUserListByDeptId(Long deptId, String accessToken) {
        // 初始化钉钉客户端，指向钉钉API的用户列表服务
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        // 创建用户列表请求对象
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        // 设置请求参数：部门ID、游标位置、每页大小
        req.setDeptId(deptId);
        req.setCursor(0L);
        req.setSize(100L);

        // 执行API调用并处理响应
        OapiV2UserListResponse rsp;
        try {
            rsp = client.execute(req, accessToken);
            log.info("deptId:{},部门钉钉用户列表：{}", rsp.getBody());
            // 检查响应是否成功
            if (!rsp.isSuccess()) {
                // 如果获取用户列表失败，记录错误日志并返回null
                log.error("获取部门的用户列表失败，部门id：{},响应code：{},响应msg:{}", deptId, rsp.getErrcode(), rsp.getErrmsg());
                return null;
            } else {
                // 如果获取用户列表成功，记录信息日志并返回用户列表
                log.info("获取部门的用户列表成功，部门id：{},用户列表:{}", deptId, JSON.toJSONString(rsp.getResult()));
                List<OapiV2UserListResponse.ListUserResponse> apiUserList = rsp.getResult().getList();
                return apiUserList;
            }
        } catch (Exception e) {
            // 如果发生异常，记录错误日志并返回null
            log.error("获取部门的用户列表失败，部门id：{}", deptId, e);
            return null;
        }

    }


    /**
     * 获取推送失败的事件列表
     *
     * @param accessToken
     * @throws ApiException
     */
    public OapiCallBackGetCallBackFailedResultResponse getCallBackFailedResult(String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/call_back/get_call_back_failed_result");
        OapiCallBackGetCallBackFailedResultRequest req = new OapiCallBackGetCallBackFailedResultRequest();
        req.setHttpMethod("GET");
        OapiCallBackGetCallBackFailedResultResponse rsp = client.execute(req, accessToken);
        log.info("获取推送失败的事件列表:{}", JSON.toJSONString(rsp));
        return rsp;
    }

    /**
     * 获取部门父部门列表
     * @param deptId
     * @param accessToken
     * @return
     */
    public OapiV2DepartmentListparentbydeptResponse.DeptListParentByDeptIdResponse listParentByDept(long deptId,String accessToken){
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listparentbydept");
        OapiV2DepartmentListparentbydeptRequest req = new OapiV2DepartmentListparentbydeptRequest();
        req.setDeptId(deptId);
        OapiV2DepartmentListparentbydeptResponse rsp;
        try {
            rsp = client.execute(req, accessToken);
            if(!rsp.isSuccess()){
                // 处理失败
                log.error("获取部门父部门列表失败，部门id：{},响应code：{},响应msg:{}", deptId, rsp.getErrcode(), rsp.getErrmsg());
                return null;
            }else{
                //处理成功
                log.info("获取部门父部门列表成功，部门id：{},部门父部门列表:{}", deptId, JSON.toJSONString(rsp.getResult()));
                return rsp.getResult();
            }
        } catch (Exception e){
            log.error("获取部门父部门列表失败", e);
            return null;
        }
    }

    /**
     * 获取部门子部门列表
     * @param deptId
     * @param accessToken
     * @return
     */
    public List<OapiV2DepartmentListsubResponse.DeptBaseResponse> listChildByDept(long deptId, String accessToken){
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(deptId);
        OapiV2DepartmentListsubResponse rsp;
        try {
            rsp = client.execute(req, accessToken);
            if(!rsp.isSuccess()){
                // 处理失败
                log.error("获取部门子部门列表失败，部门id：{},响应code：{},响应msg:{}", deptId, rsp.getErrcode(), rsp.getErrmsg());
                return null;
            }else{
                //处理成功
                log.info("获取部门子部门列表成功，部门id：{},部门父部门列表:{}", deptId, JSON.toJSONString(rsp.getResult()));
                return rsp.getResult();
            }
        } catch (Exception e){
            log.error("获取部门子部门列表失败", e);
            return null;
        }
    }

    /**
     * 解析@ApiField字段值
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> parseApiField(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            Field[] fields = ReflectUtil.getFields(obj.getClass());
            Map<String, Object> params = new HashMap<>(16);
            for (Field field : fields) {
                ApiField apiField = field.getAnnotation(ApiField.class);
                if (Objects.nonNull(apiField)) {
                    if (BeanUtil.isBean(field.getType())) {
                        Map<String, Object> subParams = parseApiField(ReflectUtil.getFieldValue(obj, field));
                        params.put(apiField.value(), subParams);
                    } else {
                        params.put(apiField.value(), ReflectUtil.getFieldValue(obj, field));
                    }
                }
            }
            return params;
        } catch (Exception e) {
            log.error("解析@ApiField字段值异常", e);
            return null;
        }
    }
}
