package com.qmqb.oa.admin.config;

import com.hzed.structure.common.constant.StringPool;
import com.hzed.structure.common.util.date.DateUtil;
import com.hzed.structure.common.util.str.StringUtil;
import com.hzed.structure.oss.rule.OssRule;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * OSS文件命名规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@Configuration
public class CustomOssRule implements OssRule {

    /**
     * 获取存储桶规则
     *
     * @param bucketName 存储桶名称
     * @return String
     */
    @Override
    public String bucketName(String bucketName) {
        return bucketName;
    }

    /**
     * 获取文件名规则
     *
     * @param baseDir          路径前缀 比如:fqy
     * @param fileType         文件类型路径,可以用来区分不同的文件类型 比如:image
     * @param originalFilename 文件名
     * @return String
     */
    @Override
    public String fileName(String baseDir, String fileType, String originalFilename) {
        String basePath = StringPool.EMPTY;
        if (StringUtil.isNotEmpty(baseDir)) {
            basePath = baseDir.concat(StringPool.SLASH);
        }
        if (StringUtil.isNotEmpty(fileType)) {
            basePath = basePath.concat(fileType).concat(StringPool.SLASH);
        }
        return basePath + DateUtil.pureFormatToday() + StringPool.SLASH + originalFilename;
    }
}
