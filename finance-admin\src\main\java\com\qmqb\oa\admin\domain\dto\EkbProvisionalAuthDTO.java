package com.qmqb.oa.admin.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description 通过获取临时授权接口获得消息的url传输实体
 * @Date 2024\10\9 0009 19:39
 * @Version 1.0
 */

@Data
@Accessors(chain = true)
@Builder
public class EkbProvisionalAuthDTO {

    /**
     * 第三方员工ID
     */
    private String ekbUserId;
    /**
     * 客户端 1-app;2-PC
     */
    private Integer client;
    /**
     * 页面类型
     */
    private String pageType;
    /**
     * 流程id
     */
    private String flowId;
}
