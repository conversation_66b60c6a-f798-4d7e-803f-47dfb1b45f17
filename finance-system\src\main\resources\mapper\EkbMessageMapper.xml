<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.EkbMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.EkbMessage">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="flow_id" property="flowId" />
        <result column="user_name" property="userName" />
        <result column="user_id" property="userId" />
        <result column="msg_action" property="msgAction" />
        <result column="action_name" property="actionName" />
        <result column="state" property="state" />
        <result column="dingtalk_push_status" property="dingtalkPushStatus" />
        <result column="create_time" property="createTime" />
        <result column="edit_time" property="editTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, flow_id, user_name, user_id, msg_action, state,action_name, dingtalk_push_status, create_time, edit_time
    </sql>

</mapper>
