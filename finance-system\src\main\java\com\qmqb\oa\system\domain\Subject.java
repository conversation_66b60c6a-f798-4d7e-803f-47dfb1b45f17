package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.oa.common.core.domain.entity.SysDept;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 科目表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_subject")
public class Subject implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父科目编码
     */
    private String parentSubjectCode;

    /**
     * 科目编码
     */
    private String subjectCode;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 借贷方向: 0-贷方, 1-借方
     */
    private Integer direction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-百益来
     */
    private Integer tenantId;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @TableLogic
    private Integer isDeleted;

    @TableField(exist = false)
    private List<Long> deptIds;

    @TableField(exist = false)
    private List<SysDept> depts;

}
