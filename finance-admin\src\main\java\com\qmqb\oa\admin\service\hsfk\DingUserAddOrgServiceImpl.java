package com.qmqb.oa.admin.service.hsfk;

import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import shade.com.alibaba.fastjson2.JSONObject;

import java.util.Objects;

/**
 * 通讯录企业增加员工事件
 *
 * <AUTHOR>
 * @date 2024/8/31
 */
@Slf4j
@Service
public class DingUserAddOrgServiceImpl implements DingTalkEventPolicyPatternService{

    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Autowired
    private SysEventService sysEventService;

    @Override
    public Boolean isCeLueModel(String type) {
        return Objects.equals(DingTalkEventType.USER_ADD_ORG.getEventType(), type);
    }

    @Override
    public void toHandleEvent(JSONObject bizData, Integer companyId) throws Exception {
        dingTalkUserService.addUser(bizData, companyId);
    }

    @Override
    public void toExecuteEvent(SysEvent sysEvent) throws Exception {
        JSONObject bizData = JSONObject.parseObject(sysEvent.getReqData());
        dingTalkUserService.addUser(bizData, sysEvent.getCompanyId());
        sysEventService.update4SuccessById(sysEvent);
    }
}