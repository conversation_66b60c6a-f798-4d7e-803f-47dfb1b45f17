package com.qmqb.oa.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.BusinessNotice;

/**
 * 业务提醒通知Service接口
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
public interface BusinessNoticeService extends IService<BusinessNotice> {
    /**
     * 查询业务提醒通知
     *
     * @param id 业务提醒通知ID
     * @return 业务提醒通知
     */
    BusinessNotice selectBusinessNoticeById(Long id);

    /**
     * 查询业务提醒通知列表
     *
     * @param businessNotice 业务提醒通知
     * @return 业务提醒通知集合
     */
    List<BusinessNotice> selectBusinessNoticeList(BusinessNotice businessNotice);

    /**
     * 新增业务提醒通知
     *
     * @param businessNotice 业务提醒通知
     * @return 结果
     */
    int insertBusinessNotice(BusinessNotice businessNotice);

    /**
     * 修改业务提醒通知
     *
     * @param businessNotice 业务提醒通知
     * @return 结果
     */
    int updateBusinessNotice(BusinessNotice businessNotice);

    /**
     * 批量删除业务提醒通知
     *
     * @param ids 需要删除的业务提醒通知ID
     * @return 结果
     */
    int deleteBusinessNoticeByIds(Long[] ids);

    /**
     * 删除业务提醒通知信息
     *
     * @param id 业务提醒通知ID
     * @return 结果
     */
    int deleteBusinessNoticeById(Long id);

}
