package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 字典类型
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-25
 */
@Getter
@AllArgsConstructor
public enum DictType {

    /**
     * 字典类型
     */
    GOODS_SERVICES_TYPES("goods_services_types", "商品和服务分类简称"),
    FINANCE_APPROVER("finance_approver", "财务审批人"),
    CASHIER_APPROVER("cashier_approver", "出纳审批人"),
    APPROVE_LIST_TIME("approve_list_time", "拉取审批列表时间"),
    COOPERATION_COMPANY("COOPERATION_COMPANY", "往来单位"),
    DATA_TYPE("data_type", "数据类型"),
    SURNAME("surname", "姓氏"),

    ;

    private final String type;
    private final String name;
}
