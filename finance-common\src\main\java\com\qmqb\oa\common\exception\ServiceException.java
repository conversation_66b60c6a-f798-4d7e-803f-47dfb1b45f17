package com.qmqb.oa.common.exception;

/**
 * 业务层异常
 *
 * <AUTHOR>
 * @date 2024/7/17
 */
public class ServiceException extends RuntimeException {
    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public ServiceException(Throwable cause) {
        super(cause);
    }
    
    public ServiceException(Throwable cause, String message, Object... args) {
        super((args != null && args.length > 0 ? String.format(message, args) : message), cause);
    }

    public ServiceException(String message, Object... args) {
        super((args != null && args.length > 0 ? String.format(message, args) : message));
    }
}
