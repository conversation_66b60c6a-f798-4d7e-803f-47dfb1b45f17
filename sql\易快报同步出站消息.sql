


CREATE TABLE `t_ekb_message` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `message_id` varchar(64) NOT NULL COMMENT '消息id',
                                 `flow_id` varchar(64) NOT NULL COMMENT '单据id',
                                 `user_name` varchar(64) NOT NULL COMMENT '接收人名字',
                                 `user_id` varchar(64) NOT NULL COMMENT '接收人userid',
                                 `msg_action` varchar(32) NOT NULL COMMENT '出站消息类别: flow.rejected=被驳回;freeflow.retract=单据撤回;freeflow.delete=单据删除;backlog.sending=待寄送;flow.paid=已支付/审批完成;freeflow.mention=被@;backlog.paying=待支付;freeflow.comment=评论;backlog.approving=待审批;freeflow.carbonCopy=抄送',
                                 `state` varchar(10) NOT NULL COMMENT '单据状态：approving=待审批/审批中;paid=已支付/审批完成;rejected=已驳回;draft=删除/撤回',
                                 `action_name` varchar(124) DEFAULT NULL COMMENT '活动名称',
                                 `dingtalk_push_status` int(4) DEFAULT '0' COMMENT '推送钉钉的状态：0-待推送;1-推送成功;2-推送失败',
                                 `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `edit_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 `remark` varchar(5048) DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_flow_id` (`flow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=671 DEFAULT CHARSET=utf8 COMMENT='易快报消息通知记录表';

CREATE TABLE `t_ekb_ding_todo_message` (
                                           `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `ekb_message_id` varchar(64) NOT NULL COMMENT '易快报消息id',
                                           `ekb_flow_id` varchar(64) NOT NULL COMMENT '易快报单据id',
                                           `ding_source_id` varchar(32) NOT NULL COMMENT '钉钉待办sourceId（请求流水号）',
                                           `ding_task_id` varchar(64) DEFAULT NULL COMMENT '钉钉待办任务id',
                                           `company_id` tinyint(1) NOT NULL COMMENT '公司主键id: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台',
                                           `ding_union_id` varchar(64) NOT NULL COMMENT '用户所在公司钉钉主体的unionId',
                                           `ding_task_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-待处理;1-处理成功;2-处理失败',
                                           `action_name` varchar(64) DEFAULT NULL COMMENT '活动名称',
                                           `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                           `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `ekb_msg_action` varchar(64) DEFAULT NULL COMMENT 'ekb出站消息类别: flow.rejected=被驳回;freeflow.retract=单据撤回;freeflow.delete=单据删除;backlog.sending=待寄送;flow.paid=已支付/审批完成;freeflow.mention=被@;backlog.paying=待支付;freeflow.comment=评论;backlog.approving=待审批;freeflow.carbonCopy=抄送',
                                           PRIMARY KEY (`id`),
                                           KEY `uk_flow_id_union_id` (`ekb_flow_id`,`ding_union_id`,`ekb_msg_action`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=683 DEFAULT CHARSET=utf8 COMMENT='易快报到钉钉待办消息表';

ALTER TABLE t_ding_user
  ADD union_id VARCHAR (64) COMMENT '用户所在公司主体的unionId' AFTER user_id;

ALTER TABLE t_ding_app_conf
  ADD ding_agent_id BIGINT (20) COMMENT '企业内部应用AgentId' AFTER ding_corp_id;

UPDATE t_ding_app_conf SET ding_agent_id = 3156871341 WHERE company_id = 0;


ALTER TABLE `t_sys_event`
    MODIFY COLUMN `req_data` varchar(3028) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求报文' AFTER `req_params`;
UPDATE t_ding_app_conf SET ding_agent_id = 3185555650 WHERE company_id = 4;
