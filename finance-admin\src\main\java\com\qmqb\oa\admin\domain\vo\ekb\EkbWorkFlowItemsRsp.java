package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;
import java.util.List;

/**
 * 易快报-工作流任务状态详情rsp
 * <AUTHOR>
 * @date 2024/7/31
 */
@Data
public class EkbWorkFlowItemsRsp {

    private List<WorkflowItem> items;
    @Data
    public static class WorkflowItem {
        private String flowId;
        private String stageName;
        private List<Operator> operators;
        // 假设我们不知道delegateData的具体结构，所以用Object
        private List<Object> delegateData;
        // 内部类 Operator
    }

    @Data
    public static class Operator {
        private String id;
        private String name;
        private String code;

    }

}
