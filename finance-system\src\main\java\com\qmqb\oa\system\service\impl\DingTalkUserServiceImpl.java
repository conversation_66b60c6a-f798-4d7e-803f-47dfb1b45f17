package com.qmqb.oa.system.service.impl;

import com.qmqb.oa.system.domain.DingTalkUser;
import com.qmqb.oa.system.mapper.DingTalkUserMapper;
import com.qmqb.oa.system.service.DingTalkUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 钉钉用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
public class DingTalkUserServiceImpl extends ServiceImpl<DingTalkUserMapper, DingTalkUser> implements DingTalkUserService {

}
