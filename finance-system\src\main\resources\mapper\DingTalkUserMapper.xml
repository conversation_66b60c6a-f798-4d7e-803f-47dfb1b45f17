<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.DingTalkUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.DingTalkUser">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="avatar" property="avatar" />
        <result column="mobile" property="mobile" />
        <result column="state_code" property="stateCode" />
        <result column="telephone" property="telephone" />
        <result column="job_number" property="jobNumber" />
        <result column="email" property="email" />
        <result column="org_email" property="orgEmail" />
        <result column="title" property="title" />
        <result column="role_list" property="roleList" />
        <result column="dept_id_list" property="deptIdList" />
        <result column="dept_order_list" property="deptOrderList" />
        <result column="leader_in_dept" property="leaderInDept" />
        <result column="manager_userid" property="managerUserid" />
        <result column="hired_date" property="hiredDate" />
        <result column="work_place" property="workPlace" />
        <result column="is_leader" property="leader" />
        <result column="is_boss" property="boss" />
        <result column="is_exclusive_account" property="exclusiveAccount" />
        <result column="is_admin" property="admin" />
        <result column="is_active" property="active" />
        <result column="is_hide_mobile" property="hideMobile" />
        <result column="is_senior" property="senior" />
        <result column="is_real_authed" property="realAuthed" />
        <result column="union_id" property="unionId" />
        <result column="union_emp_ext" property="unionEmpExt" />
        <result column="extension" property="extension" />
        <result column="remark" property="remark" />
        <result column="company" property="company" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, avatar, mobile, state_code, telephone, job_number, email, org_email, title, role_list, dept_id_list, dept_order_list, leader_in_dept, manager_userid, hired_date, work_place, is_leader, is_boss, is_exclusive_account, is_admin, is_active, is_hide_mobile, is_senior, is_real_authed, union_id, union_emp_ext, extension, remark, company, create_by, create_time, update_by, update_time, is_deleted, tenant_id
    </sql>

</mapper>
