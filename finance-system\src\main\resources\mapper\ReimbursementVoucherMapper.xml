<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.ReimbursementVoucherMapper">
    <resultMap type="com.qmqb.oa.system.domain.ReimbursementVoucher" id="ReimbursementVoucherResult">
        <result property="id" column="id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="makeDate" column="make_date"/>
        <result property="voucherType" column="voucher_type"/>
        <result property="voucherNumber" column="voucher_number"/>
        <result property="summary" column="summary"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="currency" column="currency"/>
        <result property="borrowDirection" column="borrow_direction"/>
        <result property="domesticCurrency" column="domestic_currency"/>
        <result property="deptCode" column="dept_code"/>
        <result property="itemCode" column="item_code"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="specialInvoice" column="special_invoice"/>
    </resultMap>

    <sql id="selectReimbursementVoucherVo">
        select id,
               process_instance_id,
               make_date,
               voucher_type,
               voucher_number,
               summary,
               subject_code,
               currency,
               borrow_direction,
               domestic_currency,
               dept_code,
               item_code,
               create_time,
               update_time,
               tenant_id,
               is_deleted,
               company_code,
               company_name,
               special_invoice
        from t_reimbursement_voucher
    </sql>

    <select id="selectReimbursementVoucherList" parameterType="com.qmqb.oa.system.domain.ReimbursementVoucher"
            resultMap="ReimbursementVoucherResult">
        <include refid="selectReimbursementVoucherVo"/>
        <where>
            is_deleted = 0
            <if test="processInstanceId != null and processInstanceId != ''">
                and process_instance_id = #{processInstanceId}
            </if>
            <if test="beginDate != null and beginDate != ''">
                and make_date &gt;= #{beginDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and make_date &lt;= #{endDate}
            </if>
            <if test="voucherType != null">
                and voucher_type = #{voucherType}
            </if>
            <if test="voucherNumber != null  and voucherNumber != ''">
                and voucher_number = #{voucherNumber}
            </if>
            <if test="summary != null  and summary != ''">
                and summary like concat('%', #{summary}, '%')
            </if>
            <if test="subjectCode != null  and subjectCode != ''">
                and subject_code = #{subjectCode}
            </if>
            <if test="currency != null">
                and currency = #{currency}
            </if>
            <if test="borrowDirection != null">
                and borrow_direction = #{borrowDirection}
            </if>
            <if test="domesticCurrency != null">
                and domestic_currency = #{domesticCurrency}
            </if>
            <if test="deptCode != null  and deptCode != ''">
                and dept_code = #{deptCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code = #{itemCode}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>
