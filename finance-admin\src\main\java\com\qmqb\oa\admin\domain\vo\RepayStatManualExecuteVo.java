package com.qmqb.oa.admin.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 还款统计手动执行VO
 */
@Data
public class RepayStatManualExecuteVo {
    /**
     * 统计起始日期
     */
    @ApiModelProperty("统计起始日期")
    String startDate;
    /**
     * 统计结束日期
     */
    @ApiModelProperty("统计结束日期")
    String endDate;

    /**
     * 数据类型:1-资金资产还款, 2-履约保证金退款
     */
    private Integer dataType;
}
