package com.qmqb.oa.admin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRoleListRequest;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiRoleListResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.task.SyncEkbRoleTask;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.system.domain.*;
import com.qmqb.oa.system.service.*;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@ActiveProfiles("test")
public class SyncTest extends BaseTest {

    @Autowired
    private DingTalkDeptService dingTalkDeptService;
    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Autowired
    private DingTalkRoleGroupService dingTalkRoleGroupService;
    @Autowired
    private DingTalkRoleService dingTalkRoleService;
    @Resource
    private SyncEkbRoleTask syncEkbRoleTask;
    @Resource
    private DingDeptService dingDeptService;

    public static String access_token = "8994d45329ca3197bb63eef8fa4e2aec";
//    public static String access_token = "cc316c98575534bd90e12ed383fd4e88";
    public static Integer tenantId = Tenant.OAOP.getValue();;

    @Test
    public void syncRole() throws ApiException {
//        String roles = "[{\"groupId\":319955520,\"name\":\"默认\",\"roles\":[{\"id\":319955521,\"name\":\"主管理员\"},{\"id\":319955522,\"name\":\"子管理员\"},{\"id\":319955523,\"name\":\"负责人\"},{\"id\":319955524,\"name\":\"主管\"}]},{\"groupId\":319955525,\"name\":\"职务\",\"roles\":[{\"id\":319955526,\"name\":\"财务负责人\"},{\"id\":319955527,\"name\":\"人力资源负责人\"},{\"id\":319955528,\"name\":\"出纳\"},{\"id\":319955532,\"name\":\"印章执照管理员\"},{\"id\":319955533,\"name\":\"行政负责人\"},{\"id\":319955535,\"name\":\"CFO\"},{\"id\":386427471,\"name\":\"审核会计\"},{\"id\":386480749,\"name\":\"考勤专员\"},{\"id\":386628543,\"name\":\"法务负责人\"},{\"id\":388121218,\"name\":\"执行董事\"},{\"id\":391378102,\"name\":\"管理部\"},{\"id\":448975356,\"name\":\"业务端财务审核\"},{\"id\":553215181,\"name\":\"账务组\"},{\"id\":991313907,\"name\":\"采购负责人\"},{\"id\":1251606650,\"name\":\"业务端审核会计\"},{\"id\":1469395549,\"name\":\"员工关系专员\"},{\"id\":1469394863,\"name\":\"运维负责人\"},{\"id\":1469486496,\"name\":\"公告管理员\"},{\"id\":1468752920,\"name\":\"出差机票负责人\"},{\"id\":1469636283,\"name\":\"资产管理员\"},{\"id\":1469799003,\"name\":\"薪酬绩效管理员\"},{\"id\":1469516733,\"name\":\"结算组\"},{\"id\":1469662379,\"name\":\"出纳主管\"},{\"id\":1469597302,\"name\":\"库房管理\"},{\"id\":1469343845,\"name\":\"电商产品组\"},{\"id\":1469308893,\"name\":\"借贷产品组\"},{\"id\":1469551532,\"name\":\"设计组\"},{\"id\":1469550732,\"name\":\"项目经理组\"},{\"id\":1477531882,\"name\":\"运营部数据组\"},{\"id\":1506892131,\"name\":\"所有产品人员\"},{\"id\":1506715679,\"name\":\"UI设计人员\"},{\"id\":1515569685,\"name\":\"离职审核会计\"},{\"id\":1530695298,\"name\":\"费用审核会计\"},{\"id\":1917699405,\"name\":\"审核会计组\"},{\"id\":2129062456,\"name\":\"资金核实组员\"}]},{\"groupId\":1469526446,\"name\":\"管理级别\",\"roles\":[{\"id\":1469491657,\"name\":\"开发组长\"},{\"id\":1482524146,\"name\":\"中心负责人\"}]},{\"groupId\":2028785056,\"name\":\"技术角色组\",\"roles\":[{\"id\":2028471870,\"name\":\"钉钉审批-产品经理\"},{\"id\":2028343919,\"name\":\"钉钉审批-测试组\"}]},{\"groupId\":2578054844,\"name\":\"企业文化相关\",\"roles\":[{\"id\":2578255428,\"name\":\"文化审核员\"},{\"id\":2729162381,\"name\":\"图书管理员\"},{\"id\":4086987562,\"name\":\"企业文化工牌制做\"}]},{\"groupId\":2635193080,\"name\":\"财务相关\",\"roles\":[{\"id\":2623637082,\"name\":\"工资账务组\"}]},{\"groupId\":2738998991,\"name\":\"管理部\",\"roles\":[{\"id\":2738942533,\"name\":\"管理部采购\"}]},{\"groupId\":10059977303,\"name\":\"智能合同\",\"roles\":[]}]";
//        String access_token = "924b2a0576dc366d806b773772041159";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/list");
        OapiRoleListRequest req = new OapiRoleListRequest();
        req.setSize(200L);
        req.setOffset(0L);
        OapiRoleListResponse rsp = client.execute(req, access_token);
        List<OapiRoleListResponse.OpenRoleGroup> groups = rsp.getResult().getList();

        ArrayList<DingTalkRoleGroup> list = Lists.newArrayList();
        ArrayList<DingTalkRole> list2 = Lists.newArrayList();
        for (OapiRoleListResponse.OpenRoleGroup dict : groups) {
            Long groupId = dict.getGroupId();
            String name = dict.getName();
            DingTalkRoleGroup roleGroup = new DingTalkRoleGroup();
            roleGroup.setGroupId(groupId);
            roleGroup.setGroupName(name);
            roleGroup.setTenantId(tenantId);
            list.add(roleGroup);
            for (OapiRoleListResponse.OpenRole o : dict.getRoles()) {
                DingTalkRole role = new DingTalkRole();
                role.setRoleId(o.getId());
                role.setRoleName(o.getName());
                role.setGroupId(groupId);
                role.setGroupName(name);
                role.setTenantId(tenantId);
                list2.add(role);
            }
        }
        dingTalkRoleGroupService.saveBatch(list);
        dingTalkRoleService.saveBatch(list2);
    }

    @Test
    public void syncDept() throws ApiException {
//        String access_token = "924b2a0576dc366d806b773772041159";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        int level = 6;
        for (int i = 0; i < level; i++) {
            List<DingTalkDept> depts = dingTalkDeptService.list(Wrappers.lambdaQuery(DingTalkDept.class).eq(DingTalkDept::getTenantId, tenantId).eq(DingTalkDept::getDeptLevel, i + 1));
            if (CollUtil.isEmpty(depts)) {
                throw new RuntimeException("无公司信息");
            }
            for (DingTalkDept dept : depts) {
                OapiV2DepartmentListsubRequest req1 = new OapiV2DepartmentListsubRequest();
                req1.setDeptId(dept.getDeptId());
                req1.setLanguage("zh_CN");
                OapiV2DepartmentListsubResponse rsp1 = client.execute(req1, access_token);
                if (CollUtil.isNotEmpty(rsp1.getResult())) {
                    List<DingTalkDept> depts1 = rsp1.getResult().stream().map(e -> {
                        DingTalkDept dept1 = new DingTalkDept();
                        dept1.setParentId(e.getParentId());
                        dept1.setDeptLevel(dept.getDeptLevel() + 1);
                        dept1.setDeptId(e.getDeptId());
                        dept1.setDeptName(e.getName());
                        dept1.setSourceIdentifier(e.getSourceIdentifier());
                        dept1.setAutoAddUser(e.getAutoAddUser());
                        dept1.setCreateDeptGroup(e.getCreateDeptGroup());
                        dept1.setTenantId(tenantId);
                        return dept1;
                    }).collect(Collectors.toList());
                    dingTalkDeptService.saveBatch(depts1);
                }
            }
        }
    }

    @Test
    public void testUser() throws Exception {
        List<DingTalkDept> depts = dingTalkDeptService.list(Wrappers.lambdaQuery(DingTalkDept.class).eq(DingTalkDept::getTenantId, tenantId));
        List<DingTalkUser> all = new ArrayList<>();
        for (DingTalkDept dept : depts) {

            OapiV2UserListResponse rsp = getRsp(0L, dept);
            Boolean hasMore = rsp.getResult().getHasMore();
            Long nextCursor = rsp.getResult().getNextCursor();
            List<OapiV2UserListResponse.ListUserResponse> list = rsp.getResult().getList();
            while (hasMore) {
                OapiV2UserListResponse rsp1 = getRsp(nextCursor, dept);
                hasMore = rsp1.getResult().getHasMore();
                nextCursor = rsp1.getResult().getNextCursor();
                list.addAll(rsp1.getResult().getList());
            }
            List<DingTalkUser> users = list.stream().map(e -> {
                DingTalkUser user = new DingTalkUser();
                user.setUserId(e.getUserid());
                user.setUserName(e.getName());
                user.setAvatar(e.getAvatar());
                user.setMobile(e.getMobile());
                user.setStateCode(e.getStateCode());
                user.setTelephone(e.getTelephone());
                user.setJobNumber(e.getJobNumber());
                user.setEmail(e.getEmail());
                user.setOrgEmail(e.getOrgEmail());
                user.setTitle(e.getTitle());
//                user.setRoleList();
                user.setDeptIdList(JacksonUtil.toJson(e.getDeptIdList()));
                user.setDeptOrderList(String.valueOf(e.getDeptOrder()));
//                user.setLeaderInDept();
//                user.setManagerUserid();
                user.setHiredDate(Objects.nonNull(e.getHiredDate()) ? LocalDateTimeUtil.of(e.getHiredDate()) : null);
                user.setWorkPlace(e.getWorkPlace());
                user.setLeader(e.getLeader());
                user.setBoss(e.getBoss());
                user.setExclusiveAccount(e.getExclusiveAccount());
                user.setAdmin(e.getAdmin());
                user.setActive(e.getActive());
                user.setHideMobile(e.getHideMobile());
//                user.setSenior();
//                user.setRealAuthed(e);
                user.setUnionId(e.getUnionid());
//                user.setUnionEmpExt(e.getU);
                user.setExtension(e.getExtension());
                user.setRemark(e.getRemark());


                user.setTenantId(tenantId);
                return user;
            }).collect(Collectors.toList());
            all.addAll(users);
        }

        all = all.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(DingTalkUser::getUserId))), ArrayList::new));

        dingTalkUserService.saveBatch(all);
    }

    private static OapiV2UserListResponse getRsp(Long cursor, DingTalkDept dept) throws ApiException {
//        String access_token = "924b2a0576dc366d806b773772041159";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(dept.getDeptId());
        req.setCursor(cursor);
        req.setSize(100L);
        req.setOrderField("modify_desc");
        req.setContainAccessLimit(false);
        req.setLanguage("zh_CN");
        OapiV2UserListResponse rsp = client.execute(req, access_token);
        return rsp;
    }

    @Test

    public void testSyncRole(){
        syncEkbRoleTask.syncLeader();
    }

}
