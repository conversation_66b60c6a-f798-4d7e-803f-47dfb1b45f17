package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.domain.EkbMessage;
import com.qmqb.oa.system.mapper.EkbMessageMapper;
import com.qmqb.oa.system.service.EkbMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 易快报消息通知记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Service
public class EkbMessageServiceImpl extends ServiceImpl<EkbMessageMapper, EkbMessage> implements EkbMessageService {

    @Override
    public EkbMessage findOneByFlowIdAndMessageId(String flowId, String messageId, String userId) {
        LambdaQueryWrapper<EkbMessage> queryWrapper = Wrappers.lambdaQuery(EkbMessage.class)
                .eq(EkbMessage::getUserId, userId)
                .eq(EkbMessage::getFlowId, flowId)
                .eq(EkbMessage::getMessageId, messageId);
        return getOne(queryWrapper);
    }

    @Override
    public List<EkbMessage> listByFlowIdAndMessageIds(String flowId, List<String> messageIds) {
        LambdaQueryWrapper<EkbMessage> queryWrapper = Wrappers.lambdaQuery(EkbMessage.class)
                .eq(EkbMessage::getFlowId, flowId)
                .in(EkbMessage::getMessageId, messageIds);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public EkbMessage findEkbMessageByFlowIdAndMessageId(String flowId, String messageId) {
        LambdaQueryWrapper<EkbMessage> queryWrapper = Wrappers.lambdaQuery(EkbMessage.class)
                .eq(EkbMessage::getFlowId, flowId)
                .eq(EkbMessage::getMessageId, messageId);
        return getOne(queryWrapper);
    }


}
