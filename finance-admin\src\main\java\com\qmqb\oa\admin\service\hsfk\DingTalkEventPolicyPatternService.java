package com.qmqb.oa.admin.service.hsfk;

import com.qmqb.oa.system.domain.SysEvent;
import shade.com.alibaba.fastjson2.JSONObject;

/**
 * 钉钉事件策略模式接口
 *
 * <AUTHOR>
 * @date 2024/8/31
 */

public interface DingTalkEventPolicyPatternService {
    /**
     * 判断事件类型
     * @param eventType
     * @return
     */
    Boolean isCeLueModel(String eventType);

    /**
     *  处理具体的钉钉事件
     * @param bizData
     * @param companyId
     * @throws Exception
     */
    void toHandleEvent(JSONObject bizData, Integer companyId) throws Exception;

    /**
     *  处理执行具体的钉钉事件
     * @param sysEvent
     * @throws Exception
     */
    void toExecuteEvent(SysEvent sysEvent) throws Exception;

}