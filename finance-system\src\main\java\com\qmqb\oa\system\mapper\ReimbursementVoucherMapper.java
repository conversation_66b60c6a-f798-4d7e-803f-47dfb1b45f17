package com.qmqb.oa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.ReimbursementVoucher;

import java.util.List;

/**
 * <p>
 * 报销凭证表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
public interface ReimbursementVoucherMapper extends BaseMapper<ReimbursementVoucher> {

    /**
     * 查询报销凭证列表
     *
     * @param reimbursementVoucher 报销凭证
     * @return 报销凭证集合
     */
    List<ReimbursementVoucher> selectReimbursementVoucherList(ReimbursementVoucher reimbursementVoucher);
}
