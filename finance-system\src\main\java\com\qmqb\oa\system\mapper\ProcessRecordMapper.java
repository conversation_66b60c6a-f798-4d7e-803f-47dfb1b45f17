package com.qmqb.oa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.ProcessRecord;

import java.util.List;

/**
 * <p>
 * 审批实例处理记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
public interface ProcessRecordMapper extends BaseMapper<ProcessRecord> {

    /**
     * 查询审批处理记录列表
     *
     * @param processRecord 审批处理记录
     * @return 审批处理记录集合
     */
    List<ProcessRecord> selectProcessRecordList(ProcessRecord processRecord);
}
