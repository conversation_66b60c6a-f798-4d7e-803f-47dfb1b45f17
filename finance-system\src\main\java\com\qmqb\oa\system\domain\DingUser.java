package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钉钉员工表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_ding_user")
public class DingUser implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 用户的userId
     */
      private String userId;

      /**
       * 用户在公司主体的unionId
       */
      private String unionId;

      /**
     * 名字
     */
      private String userName;

      /**
     * 手机号码
     */
      private String mobile;

      /**
     * 所属部门id列表
     */
      private String deptIdList;

      /**
     * 主体公司: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
      private Integer companyId;

      /**
     * 备注
     */
      private String remark;

      /**
     * 创建时间
     */
      private LocalDateTime createTime;

      /**
     * 更新时间
     */
      private LocalDateTime updateTime;


}
