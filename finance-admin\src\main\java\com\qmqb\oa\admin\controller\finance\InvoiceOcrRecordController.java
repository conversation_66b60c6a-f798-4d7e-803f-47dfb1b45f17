package com.qmqb.oa.admin.controller.finance;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.hzed.structure.common.util.ArrayUtil;
import com.hzed.structure.common.util.CollUtil;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.system.domain.InvoiceOcrRecord;
import com.qmqb.oa.system.service.InvoiceOcrRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 发票识别记录Controller
 *
 * <AUTHOR>
 * @date 2021-06-07
 */
@Api("发票识别记录Controller")
@RestController
@RequestMapping("/finance/ocrRecord")
public class InvoiceOcrRecordController extends BaseController {
    @Autowired
    private InvoiceOcrRecordService invoiceOcrRecordService;
    @Autowired
    private DingTalkOApiClient dingTalkOApiClient;

    /**
     * 查询发票识别记录列表
     */
    @PreAuthorize("@ss.hasPermi('finance:ocrRecord:list')")
    @GetMapping("/list")
    @ApiOperation("查询发票识别记录列表")
    public TableDataInfo list(InvoiceOcrRecord invoiceOcrRecord) {
        startPage();
        String[] invoiceDateRange = invoiceOcrRecord.getInvoiceDateRange();
        if (ArrayUtil.isNotEmpty(invoiceDateRange)) {
            Map<String, Object> params = new HashMap<>(2);
            params.put("beginInvoiceDate", invoiceDateRange[0]);
            params.put("endInvoiceDate", invoiceDateRange[1]);
            invoiceOcrRecord.setParams(params);
        }
        List<InvoiceOcrRecord> list = invoiceOcrRecordService.selectInvoiceOcrRecordList(invoiceOcrRecord);
        return getDataTable(list);
    }

    /**
     * 导出发票识别记录列表
     */
    @PreAuthorize("@ss.hasPermi('finance:ocrRecord:export')")
    @Log(title = "发票识别记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(InvoiceOcrRecord invoiceOcrRecord) {
        String[] invoiceDateRange = invoiceOcrRecord.getInvoiceDateRange();
        if (ArrayUtil.isNotEmpty(invoiceDateRange)) {
            Map<String, Object> params = new HashMap<>(2);
            params.put("beginInvoiceDate", invoiceDateRange[0]);
            params.put("endInvoiceDate", invoiceDateRange[1]);
            invoiceOcrRecord.setParams(params);
        }
        List<InvoiceOcrRecord> list = invoiceOcrRecordService.selectInvoiceOcrRecordList(invoiceOcrRecord);
        ExcelUtil<InvoiceOcrRecord> util = new ExcelUtil<InvoiceOcrRecord>(InvoiceOcrRecord.class);
        return util.exportExcel(list, "发票报销记录");
    }

    /**
     * 获取发票识别记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('finance:ocrRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(invoiceOcrRecordService.getById(id));
    }

    /**
     * 新增发票识别记录
     */
    @PreAuthorize("@ss.hasPermi('finance:ocrRecord:add')")
    @Log(title = "发票识别记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InvoiceOcrRecord invoiceOcrRecord) {
        return toAjax(invoiceOcrRecordService.save(invoiceOcrRecord));
    }

    /**
     * 修改发票识别记录
     */
    @PreAuthorize("@ss.hasPermi('finance:ocrRecord:edit')")
    @Log(title = "发票识别记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InvoiceOcrRecord invoiceOcrRecord) {
        return toAjax(invoiceOcrRecordService.updateById(invoiceOcrRecord));
    }

    /**
     * 删除发票识别记录
     */
    @PreAuthorize("@ss.hasPermi('finance:ocrRecord:remove')")
    @Log(title = "发票识别记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(invoiceOcrRecordService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 同步审批实例业务编号
     */
    @GetMapping("/sync/{tenant_id}")
    public AjaxResult sync(@PathVariable(value = "tenant_id") Integer tenantId) {
        TenantContextHolder.setTenantId(tenantId);
        List<InvoiceOcrRecord> data = invoiceOcrRecordService.list(new QueryWrapper<InvoiceOcrRecord>().eq("tenant_id", tenantId).isNotNull("process_instance_id"));
        List<InvoiceOcrRecord> updateList = new ArrayList<>();
        data.forEach(invoiceOcrRecord -> {
            if (StrUtil.isEmpty(invoiceOcrRecord.getBusinessId())) {
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = dingTalkOApiClient.getProcessInstance(invoiceOcrRecord.getProcessInstanceId());
                String businessId = processInstance.getBusinessId();
                invoiceOcrRecord.setBusinessId(businessId);
                updateList.add(invoiceOcrRecord);
            }
        });
        boolean updateBatch = true;
        if (CollUtil.isNotEmpty(updateList)) {
            updateBatch = invoiceOcrRecordService.updateBatchById(data);
        }
        TenantContextHolder.clearTenantId();
        return toAjax(updateBatch);
    }
}
