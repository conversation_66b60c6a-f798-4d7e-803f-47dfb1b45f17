package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

import java.util.List;

/**
 * 易快报查询员工信息rsp
 * <AUTHOR>
 * @date 2024/7/31
 */
@Data
public class EkbUserRsp {
    /**
     * 员工ID
     */
    private String id;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工别名
     */
    private String nickName;

    /**
     * 员工工号
     */
    private String  code;

    /**
     * 所在部门
     */
    private List<String> departments;

    /**
     * 默认部门
     */
    private String defaultDepartment;

    /**
     * 手机号
     */
    private String cellphone;

    /**
     * true：在职，false：已离职（账号逻辑删除，在系统上不可见）
     */
    private Boolean active;

    /**
     * 第三方平台人员ID
     */
    private String userId;
    /**
     * 登录邮箱（大写字母全转换为小写字母）
     */
    private String email;

    /**
     * 员工个人信息显示邮箱（大写字母保持不变）
     */
    private String showEmail;

    /**
     * 是否外部员工
     */
    private String external;

    /**
     * 是否激活，表示账号是否可用
     */
    private String authState;

    /**
     * 国际区号
     */
    private String globalRoaming;

    /**
     * 备注
     */
    private String note;

    /**
     * 员工自定义字段
     */
    private String staffCustomForm;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新时间
     */
    private String createTime;

}
