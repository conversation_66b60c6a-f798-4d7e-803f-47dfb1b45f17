package com.qmqb.oa.admin.service.ekb.init;


import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.admin.domain.dto.EkbInvoiceTypeDTO;
import com.qmqb.oa.admin.util.ekb.EkbResourceUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * <AUTHOR>
 * @Description ekb 发票类型文件数据本地初始化
 * @Date 2024\11\6 0006 15:06
 * @Version 1.0
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class EkbInvoiceTypeFileReadService {

    private final ResourceLoader resourceLoader;

    @SneakyThrows
    @PostConstruct
    private void initInvoiceTypeParam(){
        try {
            //初始化所有票据数据
            EkbResourceUtil.initAllInvoiceTypes(readEkbInvoiceTypeFile());
        } catch (Exception ex) {
            log.warn("########### initInvoiceTypeParam error = {}", ex);
        }
    }

    /**
     * 读取本地的
     * @return
     * @throws FileNotFoundException
     */
    private List<EkbInvoiceTypeDTO> readEkbInvoiceTypeFile() throws FileNotFoundException {
        Resource resource = resourceLoader.getResource("classpath:EkbInvoiceType.json");
        StringBuilder contentBuilder = new StringBuilder();

        try (InputStream inputStream = resource.getInputStream(); BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                contentBuilder.append(line).append("\n");
            }

        } catch (IOException e) {
            throw new RuntimeException("Failed to load resource: classpath:EkbInvoiceType.json", e);
        }

        // Remove the last newline character if it exists
        if (contentBuilder.length() > 0 && contentBuilder.charAt(contentBuilder.length() - 1) == '\n') {
            contentBuilder.setLength(contentBuilder.length() - 1);
        }

        return JacksonUtil.parseArray(contentBuilder.toString(), EkbInvoiceTypeDTO.class);
    }

}
