package com.qmqb.oa.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.EmployeeSubject;
import org.apache.ibatis.annotations.Param;

/**
 * 员工主体Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
public interface EmployeeSubjectMapper extends BaseMapper<EmployeeSubject> {
    /**
     * 查询员工主体
     *
     * @param id 员工主体ID
     * @return 员工主体
     */
        EmployeeSubject selectEmployeeSubjectById(Long id);

    /**
     * 查询员工主体列表
     *
     * @param employeeSubject 员工主体
     * @return 员工主体集合
     */
    List<EmployeeSubject> selectEmployeeSubjectList(EmployeeSubject employeeSubject);

    /**
     * 新增员工主体
     *
     * @param employeeSubject 员工主体
     * @return 结果
     */
    int insertEmployeeSubject(EmployeeSubject employeeSubject);

    /**
     * 修改员工主体
     *
     * @param employeeSubject 员工主体
     * @return 结果
     */
    int updateEmployeeSubject(EmployeeSubject employeeSubject);

    /**
     * 删除员工主体
     *
     * @param id 员工主体ID
     * @return 结果
     */
    int deleteEmployeeSubjectById(Long id);

    /**
     * 批量删除员工主体
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteEmployeeSubjectByIds(Long[] ids);

    /**
     * 根据姓名查询
     * @param names
     * @return
     */
    List<EmployeeSubject> listByNames(@Param("names")List<String> names);

}
