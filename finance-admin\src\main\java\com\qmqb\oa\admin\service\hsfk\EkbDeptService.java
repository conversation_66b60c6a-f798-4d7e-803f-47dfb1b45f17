package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.qmqb.oa.admin.config.EkbConfig;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAccessTokenRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAddDeptReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAddDeptRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchAddDeptReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchAddDeptRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbDepartmentListRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbUpdateDeptResp;
import com.qmqb.oa.common.utils.http.HttpRestService;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.EkbDingDeptMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 易快报业务接口类
 * <AUTHOR>
 * @date 2024/7/17
 */
@Slf4j
@Component
public class EkbDeptService {

    @Autowired
    private EkbConfig ekbConfig;
    @Autowired
    private HttpRestService httpRestService;
    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private EkbDingDeptMappingService ekbDingDeptMappingService;
    @Autowired
    private EkbLoginService ekbLoginService;


    /**
     * 批量新增部门
     * @param batchAddDeptReq
     */
    public void batchAddDept(EkbBatchAddDeptReq batchAddDeptReq){
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getBatchAddDeptUrl() + accessTokenRep.getAccessToken();

        EkbBatchAddDeptRsp ekbBatchAddDeptRsp = httpRestService.post(url, JSON.toJSONString(batchAddDeptReq), EkbBatchAddDeptRsp.class, "【易快报-批量新增部门】");
        List<EkbAddDeptRsp> ekbAddDeptRspList = ekbBatchAddDeptRsp.getItems();
        List<EkbDingDeptMapping> batchUpdateList = Lists.newArrayListWithCapacity(ekbAddDeptRspList.size());
        ekbAddDeptRspList.forEach(s -> {
            EkbDingDeptMapping ekbDingDeptMapping = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getDeptCode, s.getCode()));
            EkbDingDeptMapping mapping = new EkbDingDeptMapping();
            mapping.setId(ekbDingDeptMapping.getId());
            mapping.setEkbDeptId(s.getId());
            mapping.setEkbDeptName(s.getName());
            mapping.setEkbParentId(s.getParentId());
            mapping.setUpdateTime(LocalDateTime.now());
            batchUpdateList.add(mapping);
        });
        // 更新至钉钉、易快报部门映射表
        ekbDingDeptMappingService.updateBatchById(batchUpdateList);
    }

    /**
     * 获取部门列表
     * @param start 分页起始值，从0开始
     * @param count 查询数据条数，最大不能超过100
     * @return
     */
    public EkbDepartmentListRsp getDepartmentList(Integer start, Integer count){
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String param = new StringBuilder()
                .append("?accessToken=").append(accessTokenRep.getAccessToken())
                .append("&start=").append(start)
                .append("&count=").append(count)
                .toString();
        String url = ekbConfig.getDepartmentsUrl() + param;
        EkbDepartmentListRsp ekbDepartmentListRsp = httpRestService.get(url, EkbDepartmentListRsp.class, "【易快报-获取部门列表】");
        return ekbDepartmentListRsp;
    }

    /**
     * 易快报修改部门信息
     * @param ekbDeptId
     * @param updateDeptReq
     */
    public void updateDept(String ekbDeptId, EkbAddDeptReq updateDeptReq) {

        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String param = new StringBuilder().append(ekbDeptId).append("?accessToken=").append(accessTokenRep.getAccessToken()).toString();
        String url = ekbConfig.getUpdateDepartmentUrl() + param;
        EkbUpdateDeptResp ekbUpdateDeptResp = httpRestService.put(url, JSON.toJSONString(updateDeptReq), EkbUpdateDeptResp.class, "【易快报-修改部门信息】", true);
        EkbUpdateDeptResp.EkbUpdateDept ekbUpdateDept = ekbUpdateDeptResp.getValue();
        EkbDingDeptMapping ekbDingDeptMapping = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                .eq(EkbDingDeptMapping::getEkbDeptId, ekbDeptId));
        EkbDingDeptMapping update = new EkbDingDeptMapping();
        update.setId(ekbDingDeptMapping.getId());
        update.setEkbDeptName(ekbUpdateDept.getName());
        update.setEkbParentId(ekbUpdateDept.getParentId());
        update.setUpdateTime(LocalDateTime.now());
        ekbDingDeptMappingService.updateById(update);

    }

    /**
     * 易快报停用部门
     * @param ekbDeptId
     */
    public void stopDept(String ekbDeptId) {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String param = new StringBuilder()
                .append(ekbDeptId)
                .append("?accessToken=").append(accessTokenRep.getAccessToken())
                .append("&active=false").toString();
        String url = ekbConfig.getDisableOrEnableDepartmentUrl() + param;
        httpRestService.put(url, null, EkbBatchAddDeptRsp.class, "【易快报-停用部门】", true);
        log.info("【易快报-停用部门】成功！易快报部门id:{}", ekbDeptId);

    }
    
}
