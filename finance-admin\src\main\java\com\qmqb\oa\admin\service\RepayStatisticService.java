package com.qmqb.oa.admin.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.common.util.date.DateUtil;
import com.qmqb.oa.admin.domain.vo.RepayGenRecordVo;
import com.qmqb.oa.admin.domain.vo.RepayStatManualExecuteVo;
import com.qmqb.oa.common.constant.RepaymentGenConstant;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.domain.dto.RepayGenConditionDto;
import com.qmqb.oa.common.core.domain.entity.SysDictData;
import com.qmqb.oa.common.enums.DictType;
import com.qmqb.oa.common.enums.RepaymentGenStatusEnums;
import com.qmqb.oa.common.utils.SecurityUtils;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.system.domain.RepayGenRecord;
import com.qmqb.oa.system.domain.RepayStat;
import com.qmqb.oa.system.domain.RepayStatJindie;
import com.qmqb.oa.system.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class RepayStatisticService {
    /**
     * BI需要用到的程序编码 1
     */
    private static final Integer BI_PROGRAM_NO = 1;

    @Autowired
    private RepayGenRecordService repayGenRecordService;
    @Autowired
    private RepayStatService repayStatService;
    @Autowired
    private RepayStatJindieService repayStatJindieService;
    @Autowired
    private SysDictTypeService sysDictTypeService;

    /**
     * 触发统计
     *
     * @return
     */
    public boolean triggerStat(RepayStatManualExecuteVo vo, Integer type) {
        DateTime start = DateTime.of(vo.getStartDate(), DateTimeUtil.PATTERN_DATE);
        DateTime end = DateTime.of(vo.getEndDate(), DateTimeUtil.PATTERN_DATE);
        DateTime startDate = DateUtil.beginOfDay(start);
        DateTime endDate = DateTime.of(DateUtil.format(DateUtil.endOfDay(end), DateTimeUtil.PATTERN_DATETIME), DateTimeUtil.PATTERN_DATETIME);
        RepayGenRecord record = generateInitRepayGenRecordDO(type, startDate, endDate, vo.getDataType());
        return repayGenRecordService.save(record);
    }

    /**
     * 触发统计
     *
     * @return
     */
//    public boolean triggerStat(DateTime startDate, DateTime endDate, Integer type) {
//        RepayGenRecord record = generateInitRepayGenRecordDO(type, startDate, endDate, 1);
//        return repayGenRecordService.save(record);
//    }


    /**
     * 生成初始化的还款记录DO
     *
     * @param type 生成类型 1=手动 2=自动
     * @return DO
     */
    public RepayGenRecord generateInitRepayGenRecordDO(Integer type, DateTime startDate, DateTime endDate, Integer dataType) throws IllegalArgumentException {
        if (!isDateSpaceValid(startDate, endDate)) {
            throw new RuntimeException("时间设置异常");
        }
        String userName = StringUtils.EMPTY;
        if (RepaymentGenConstant.MANUAL_GEN.equals(type)) {
            userName = getCurrentUserName();
        }

        DateTime now = DateTime.now();
        RepayGenRecord record = new RepayGenRecord();
        //设置本次查询的时间范围
        record.setStartDate(startDate);
        record.setEndDate(endDate);
        //设置批次号
        String batchNo = generateBatchNo(type);
        record.setBatchNo(batchNo);
        //BI程序号
        record.setProgramId(BI_PROGRAM_NO);
        //初始化状态
        record.setStatus(RepaymentGenStatusEnums.INITED.getStatus());
        //本次修改人信息
        record.setCreator(userName);
        record.setUpdater(userName);
        //本次修改时间信息
        record.setDbcreatedt(now);
        record.setDbupdatedt(now);
        //设置数据类型
        record.setDataType(dataType);
        return record;

    }

    protected String getCurrentUserName() {
        return SecurityUtils.getUsername();
    }

    /**
     * 检验开始时间是否大于结束时间
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private boolean isDateSpaceValid(DateTime startDate, DateTime endDate) {
        return startDate.isBefore(endDate);
    }


    /**
     * 生成批次号
     *
     * @param type 生成类型 1=手动 2=自动
     * @return
     */
    private String generateBatchNo(Integer type) throws IllegalArgumentException {
        StringBuilder stringBuilder;
        if (RepaymentGenConstant.MANUAL_GEN.equals(type)) {
            stringBuilder = new StringBuilder(RepaymentGenConstant.MANUAL_BATCH_PREFIX);
        } else if (RepaymentGenConstant.AUTO_GEN.equals(type)) {
            stringBuilder = new StringBuilder(RepaymentGenConstant.AUTO_BATCH_PREFIX);
        } else {
            throw new IllegalArgumentException("生成批次号类型未知");
        }
        String nowDate = getNowDateStr();
        stringBuilder.append(nowDate);
        String randomNum = getRandomStr();
        stringBuilder.append(randomNum);
        return stringBuilder.toString();
    }


    /**
     * 获取当前日期时间字符串
     *
     * @return
     */
    private String getNowDateStr() {
        LocalDateTime now = LocalDateTime.now();
        return now.format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 获取六位随机数
     *
     * @return
     */
    private String getRandomStr() {
        Random random = new Random();
        Integer randomNum = random.nextInt(99999);
        return String.format("%06d", randomNum);
    }


    /**
     * 还款记录生成展示
     */
    public List<RepayGenRecordVo> listRecord(RepayGenRecordVo vo) {
        RepayGenConditionDto record = transferToDtoFrom(vo);
        List<RepayGenRecord> dos = repayGenRecordService.listByCondition(record);
        List<RepayGenRecordVo> vos = new ArrayList<>();
        for (RepayGenRecord item : dos) {
            RepayGenRecordVo resultVo = transferToVoFrom(item);
            vos.add(resultVo);
        }
        return vos;
    }

    /**
     * 转化为VO
     *
     * @param repayGenRecord
     * @return
     */
    private RepayGenRecordVo transferToVoFrom(RepayGenRecord repayGenRecord) {
        RepayGenRecordVo vo = new RepayGenRecordVo();
        vo.setBatchNo(repayGenRecord.getBatchNo());
        vo.setBatchNoCreateDt(DateUtil.formatDateTime(repayGenRecord.getDbcreatedt()));
        vo.setEndDate(DateUtil.format(repayGenRecord.getEndDate(), DatePattern.NORM_DATE_PATTERN));
        vo.setStartDate(DateUtil.format(repayGenRecord.getStartDate(), DatePattern.NORM_DATE_PATTERN));
        vo.setStatus(repayGenRecord.getStatus());
        vo.setDataType(repayGenRecord.getDataType());
        return vo;
    }

    /**
     * 转化为DO
     *
     * @param vo
     * @return
     */
    private RepayGenConditionDto transferToDtoFrom(RepayGenRecordVo vo) {
        RepayGenConditionDto dto = new RepayGenConditionDto();
        if (!Objects.isNull(vo.getStartDate())) {
            Date beginDate = DateUtil.parse(vo.getStartDate(), DatePattern.NORM_DATE_PATTERN);
            dto.setStartDate(DateUtil.beginOfDay(beginDate));
        }

        if (!Objects.isNull(vo.getEndDate())) {
            Date endDate = DateUtil.parse(vo.getEndDate(), DatePattern.NORM_DATE_PATTERN);
            dto.setEndDate(DateUtil.endOfDay(endDate));
        }
        dto.setBatchNo(vo.getBatchNo());
        dto.setStatus(vo.getStatus());
        dto.setDataType(vo.getDataType());
        return dto;
    }

    /**
     * 用友还款详情导出(已废弃)
     *
     * @return
     */
    public AjaxResult detailExport(String batchNo) {
        //获取批次号对应的详情数据
        List<RepayStat> repayStats = repayStatService.listExportBy(batchNo);
        if (CollectionUtils.isEmpty(repayStats)) {
            return AjaxResult.error("该批次号数据不存在，导出失败");
        }
        //组装，返回excel流文件
        ExcelUtil<RepayStat> util = new ExcelUtil<RepayStat>(RepayStat.class);
        return util.exportExcel(repayStats, "收款凭证");
    }

    /**
     * 金蝶云还款详情导出
     *
     * @return
     */
    public AjaxResult detailExportJinDie(String batchNo) {
        //获取批次号对应的详情数据
        List<RepayStatJindie> repayStats = repayStatJindieService.listExportBy(batchNo);
        if (CollectionUtils.isEmpty(repayStats)) {
            return AjaxResult.error("该批次号数据不存在，导出失败");
        }


        repayStats.add(0,getRepayStatJindie());
        RepayGenRecord record = repayGenRecordService.getBaseMapper().selectOne(new QueryWrapper<RepayGenRecord>().eq("batch_no", batchNo));
        List<SysDictData> dataList = sysDictTypeService.selectDictDataByTypeAll(DictType.DATA_TYPE.getType());
        SysDictData dictData = dataList.stream().filter(dict -> Objects.equals(record.getDataType(), Integer.parseInt(dict.getDictValue()))).findAny().get();

        //组装，返回excel流文件
        ExcelUtil<RepayStatJindie> util = new ExcelUtil<>(RepayStatJindie.class);
        return util.exportExcel(repayStats, dictData.getDictLabel());
    }

    /**
     * 得到设置特殊的金蝶云还款详情导出单元头
     * @return
     */
    public RepayStatJindie getRepayStatJindie(){
        RepayStatJindie repayStatJindie = new RepayStatJindie();
        repayStatJindie.setBatchNo("BATCHNO");
        repayStatJindie.setFbillhead("FBillHead(GL_VOUCHER)");
        repayStatJindie.setFaccountbookid("FAccountBookID");
        repayStatJindie.setFaccountbookidName("FAccountBookID#Name");
        repayStatJindie.setFdate("FDate");
        repayStatJindie.setFaccbookorgid("FAccountBookID");
        repayStatJindie.setFaccbookorgidName("FAccountBookID#Name");
        repayStatJindie.setFvouchergroupid("FVOUCHERGROUPID");
        repayStatJindie.setFvouchergroupidName("FVOUCHERGROUPID#Name");
        repayStatJindie.setFvouchergroupno("FVOUCHERGROUPNO");
        repayStatJindie.setFaccbookorgid("FACCBOOKORGID");
        repayStatJindie.setFaccbookorgidName("FACCBOOKORGID#Name");
        repayStatJindie.setSplit("*Split*1");
        repayStatJindie.setFentity("FEntity");
        repayStatJindie.setFexplanation("FEXPLANATION");
        repayStatJindie.setFaccountid("FACCOUNTID");
        repayStatJindie.setFaccountidName("FACCOUNTID#Name");
        repayStatJindie.setFacctfullname("FAcctFullName");
        repayStatJindie.setFdetailidFflex14("FDetailID#FFLEX14");
        repayStatJindie.setFdetailidFflex14Name("FDetailID#FFLEX14#Name");
        repayStatJindie.setFdetailidFflex15("FDetailID#FFLEX15");
        repayStatJindie.setFdetailidFflex15Name("FDetailID#FFLEX15#Name");
        repayStatJindie.setFdetailidFflex12("FDetailID#FFLEX12");
        repayStatJindie.setFdetailidFflex12Name("FDetailID#FFLEX12#Name");
        repayStatJindie.setFdetailidFflex13("FDetailID#FFLEX13");
        repayStatJindie.setFdetailidFflex13Name("FDetailID#FFLEX13#Name");
        repayStatJindie.setFdetailidFf100003("FDetailID#FF100003");
        repayStatJindie.setFdetailidFf100003Name("FDetailID#FF100003#Name");
        repayStatJindie.setFdetailidFf100004("FDetailID#FF100004");
        repayStatJindie.setFdetailidFf100004Name("FDetailID#FF100004#Name");
        repayStatJindie.setFdetailidFflex16("FDetailID#FFLEX16");
        repayStatJindie.setFdetailidFflex16Name("FDetailID#FFLEX16#Name");
        repayStatJindie.setFdetailidFf100002("FDetailID#FF100002");
        repayStatJindie.setFdetailidFf100002Name("FDetailID#FF100002#Name");
        repayStatJindie.setFdetailidFflex11("FDetailID#FFLEX11");
        repayStatJindie.setFdetailidFflex11Name("FDetailID#FFLEX11#Name");
        repayStatJindie.setFdetailidFflex5("FDetailID#FFlex5");
        repayStatJindie.setFdetailidFflex5Name("FDetailID#FFlex5#Name");
        repayStatJindie.setFdetailidFflex6("FDetailID#FFlex6");
        repayStatJindie.setFdetailidFflex6Name("FDetailID#FFlex6#Name");
        repayStatJindie.setFdetailidFflex4("FDetailID#FFlex4");
        repayStatJindie.setFdetailidFflex4Name("FDetailID#FFlex4#Name");
        repayStatJindie.setFdetailidFflex7("FDetailID#FFLEX7");
        repayStatJindie.setFdetailidFflex7Name("FDetailID#FFLEX7#Name");
        repayStatJindie.setFdetailidFflex8("FDetailID#FFlex8");
        repayStatJindie.setFdetailidFflex8Name("FDetailID#FFlex8#Name");
        repayStatJindie.setFdetailidFflex9("FDetailID#FFLEX9");
        repayStatJindie.setFdetailidFflex9Name("FDetailID#FFLEX9#Name");
        repayStatJindie.setFdetailidFflex10("FDetailID#FFlex10");
        repayStatJindie.setFdetailidFflex10Name("FDetailID#FFlex10#Name");
        repayStatJindie.setFdetailidFflex10("FDetailID#FFlex7");
        repayStatJindie.setFdetailidFflex10Name("FDetailID#FFlex7#Name");
        repayStatJindie.setFdetailidFflex10("FDetailID#FFlex8");
        repayStatJindie.setFdetailidFflex10Name("FDetailID#FFlex8#Name");
        repayStatJindie.setFcurrencyid("FCURRENCYID");
        repayStatJindie.setFcurrencyidName("FCURRENCYID#Name");
        repayStatJindie.setFexchangeratetype("FEXCHANGERATETYPE");
        repayStatJindie.setFexchangeratetypeName("FEXCHANGERATETYPE#Name");
        repayStatJindie.setFdebit("FDEBIT");
        repayStatJindie.setFcredit("FCREDIT");
        return repayStatJindie;
    }

}
