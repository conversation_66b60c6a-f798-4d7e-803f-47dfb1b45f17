package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 租户: 0-全民, 1-合众
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Getter
@AllArgsConstructor
public enum Tenant {
    /**
     * 全民
     */
    QMQB(0, "广州市全民钱包科技有限公司", "全民钱包", "@lALPDe7s_yoAHBPMjcyN", "@lALPDfJ6fdWUHxHNASzNASw"),
    /**
     * 合众
     */
    SZHZ(1, "深圳合众祥瑞科技发展有限公司", "合众祥瑞", "", ""),
    /**
     * 公共
     */
    PUB(2, "公共", "公共", "", ""),
    /**
     * 佛山百益来信息咨询有限公司
     */
    FSBYL(3, "佛山百益来信息咨询有限公司", "百益来", "@lALPDe7s_2E-A-DMjcyN", "@lALPDfYH_KrLVZPNASzNASw"),
    /**
     * OA办公平台
     */
    OAOP(4, "OA办公平台", "OA", "@lALPDefSAeByevDMjcyN", "@lALPDeC3BJ20wOrNASzNASw"),

    /**
     * TEST主体
     */
    OAOP_TEST(111, "OA测试办公平台", "OATEST", "@lALPDe7s_yn_ueHMjcyN", "@lALPDf0i-cPn-NXNASzNASw"),


    /**
     * 法汇天下
     */
    FHTX(5, "法汇天下（广州）咨询有限公司", "法汇天下", "@lALPDfYH_KrLdc3MjcyN", "@lALPDetfgLx3zsfNASzNASw"),

    /**
     * 仲建信息咨询有限公司
     */
    ZJ(6, "仲建信息咨询有限公司", "仲建", "@lALPDf0i-gLmZc_MjcyN", "@lALPDetfgMsF1J7NASzNASw"),
    ;
    /**
     * 值
     */
    private final Integer value;
    private final String name;
    private final String shortName;
    /**
     * 消息通知类型图标id
     */
    private final String iconImgId;

    /**
     * 支付完成的通知类型图标id
     */
    private final String paidImgId;

    public static String getNameByValue(Integer value) {
        return Arrays.stream(values()).filter(e -> e.value.equals(value)).findFirst().map(Tenant::getName).orElse("");
    }

    public static List<Tenant> company() {
        return Arrays.asList(QMQB, SZHZ, FSBYL, OAOP);
    }

    public static Tenant findByShortName(String shortName) {
        return Arrays.stream(values()).filter(e -> e.shortName.equals(shortName)).findFirst().orElse(PUB);
    }

    public static Tenant findByValue(Integer value) {
        return Arrays.stream(values()).filter(e -> e.value.equals(value)).findFirst().orElse(PUB);
    }
}
