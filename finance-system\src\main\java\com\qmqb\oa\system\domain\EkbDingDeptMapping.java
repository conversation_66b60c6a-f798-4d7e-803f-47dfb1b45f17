package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钉钉与易快报部门映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_ekb_ding_dept_mapping")
public class EkbDingDeptMapping implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 主键id
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 钉钉部门id
     */
      private Long dingDeptId;

      /**
     * 部门名称
     */
      private String deptName;

      /**
     * 部门级别
     */
      private Integer dingDeptLevel;

      /**
     * 父级部门id
     */
      private Long dingParentId;

      /**
     * 公司主键id: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
      private Integer companyId;

      /**
     * 部门编码
     */
      private String deptCode;

      /**
     * 易快报部门id
     */
      private String ekbDeptId;

      /**
     * 易快报部门名称
     */
      private String ekbDeptName;

      /**
     * 易快报父级部门id
     */
      private String ekbParentId;

      /**
     * 备注
     */
      private String remark;

      /**
     * 创建时间
     */
      private LocalDateTime createTime;

      /**
     * 更新时间
     */
      private LocalDateTime updateTime;


}
