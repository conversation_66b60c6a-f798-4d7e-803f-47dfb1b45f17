package com.qmqb.oa.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.BusinessNotice;

/**
 * 业务提醒通知Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
public interface BusinessNoticeMapper extends BaseMapper<BusinessNotice> {
    /**
     * 查询业务提醒通知
     *
     * @param id 业务提醒通知ID
     * @return 业务提醒通知
     */
    BusinessNotice selectBusinessNoticeById(Long id);

    /**
     * 查询业务提醒通知列表
     *
     * @param businessNotice 业务提醒通知
     * @return 业务提醒通知集合
     */
    List<BusinessNotice> selectBusinessNoticeList(BusinessNotice businessNotice);

    /**
     * 新增业务提醒通知
     *
     * @param businessNotice 业务提醒通知
     * @return 结果
     */
    int insertBusinessNotice(BusinessNotice businessNotice);

    /**
     * 修改业务提醒通知
     *
     * @param businessNotice 业务提醒通知
     * @return 结果
     */
    int updateBusinessNotice(BusinessNotice businessNotice);

    /**
     * 删除业务提醒通知
     *
     * @param id 业务提醒通知ID
     * @return 结果
     */
    int deleteBusinessNoticeById(Long id);

    /**
     * 批量删除业务提醒通知
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteBusinessNoticeByIds(Long[] ids);

}
