package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统事件表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_sys_event")
public class SysEvent implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 主键
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 流水号
     */
      private String requestNo;

      /**
     * 事件源：1-钉钉；2-易快报
     */
      private Integer eventSource;

      /**
     * 钉钉公司主体：0-全民钱包；4-OA
     */
      private Integer companyId;

      /**
     * 事件类型
     */
      private String eventType;

      /**
     * 请求报文
     */
      private String reqData;

      /**
     * 事件的时间戳
     */
      private Long eventTime;

      /**
       * 请求接口的param，拼接在url中的参数(如修改和停用部门接口的部门id)；不包含token和其他固定值参数
       */
      private String reqParams;

      /**
     * 状态：0-处理中；1-处理成功；2-处理失败
     */
      private Integer status;

      /**
     * 失败次数
     */
      private Integer failCount;

      /**
       * 失败原因
       */
      private String failReason;


      /**
     * 创建时间
     */
      private LocalDateTime createTime;

      /**
     * 更新时间
     */
      private LocalDateTime updateTime;


}
