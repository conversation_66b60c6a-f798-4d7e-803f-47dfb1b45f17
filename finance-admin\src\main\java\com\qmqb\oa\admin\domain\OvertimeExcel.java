package com.qmqb.oa.admin.domain;

import com.qmqb.oa.common.annotation.Excel;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Data
public class OvertimeExcel {
    @Excel(name = "组名")
    private String group;
    @Excel(name = "姓名")
    private String name;
    @Excel(name = "总共天数")
    private String totalDay;
    @Excel(name = "单位")
    private String unit;
    @Excel(name = "合计总额")
    private String totalAmount;
}
