package com.qmqb.oa.system.mapper;

import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.vo.RoleUserInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 钉钉与易快报员工映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface EkbDingUserMappingMapper extends BaseMapper<EkbDingUserMapping> {

    /**
     * 查询易快报所有用户Id和主体部门id
     * 更新角色下员工信息接口使用
     * @return
     */
    List<RoleUserInfoVo> selectUserIdAndCompanyDeptId();

    /**
     * 根据易快报用户id查询易快报用户信息,如果用户在多个主体都有数据，则按照顺序全民 - OA查询
     * @param ekbUserId
     * @return
     */
    EkbDingUserMapping findOneByEkbUserId(@Param("ekbUserId") String ekbUserId);
}
