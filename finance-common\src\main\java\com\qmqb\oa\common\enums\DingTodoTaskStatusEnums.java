package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 易快报到钉钉待办消息表处理状态枚举
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum DingTodoTaskStatusEnums {
    /**
     * 生成记录的初始化状态
     */
    DEAL("待处理", 0),
    FINISHED("处理成功", 1),
    /**
     * 处理失败
     */
    FAILURE("处理失败", 2),
    ;
    /**
     * 描述
     */
    private String desc;
    /**
     * 状态
     */
    private Integer status;
}
