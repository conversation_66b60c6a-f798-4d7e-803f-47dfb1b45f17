@startuml
participant 钉钉系统 as dingding
participant 财务自动化系统 as finance
participant 易快报 as ekb

autonumber
dingding -> finance: 通讯录事件通知（新增/更新/删除部门或员工）
finance -> finance: 保存事件并返回结果
opt 根据事件类型做对应的处理
    alt 部门事件(新增/更新/删除)
    finance -> finance: 处理部门事件,对应的表t_ding_dept
    finance -> ekb: 同步部门信息到易快报,新增/更新/删除
    ekb -> finance:
        alt 部门信息同步成功
            finance -> finance: 同步更新钉钉易快报映射表t_ekb_ding_dept_mapping
        else 部门信息同步失败
            finance -> finance: 保存部门失败事件到t_sys_event表中，定时任务处理失败的事件
        end
    note right: 但是对于易快报来说，全民钱包公司和OA办公公司是属于部门，而不是公司
    else 员工事件(新增/更新/删除)
        finance -> finance: 处理员工事件,对应的表t_ding_uer
        finance -> ekb: 同步员工信息到易快报,新增/更新/删除
       alt 员工信息同步成功
            finance -> finance: 同步更新钉钉易快报映射表t_ekb_ding_user_mapping
       else 员工信息同步失败
        finance -> finance: 保存员工失败事件到t_sys_event表中，定时任务处理失败的事件
        end

        note right: 同一手机号的员工，有不同钉钉公司主体，在我们数据表中对应多条记录，\n 但是对于易快报来说，只是同一用户，只有一条记录，不需要区分公司主体
    end

end

@enduml