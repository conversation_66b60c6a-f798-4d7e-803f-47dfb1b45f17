package com.qmqb.oa.admin.controller.hsfk;


import com.alibaba.fastjson.JSON;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.tool.api.ApiResponse;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.admin.service.hsfk.EkbUserService;
import com.qmqb.oa.admin.service.hsfk.HeSiFeiKongService;
import com.qmqb.oa.admin.task.SyncDingUserInfoTask;
import com.qmqb.oa.admin.task.SyncEkbRoleTask;
import com.qmqb.oa.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * swagger 用户测试方法
 *
 * <AUTHOR>
 */
@Api("用户信息管理")
@Slf4j
@RestController
@RequestMapping("/ding")
public class DingController extends BaseController {
    @Autowired
    private HeSiFeiKongService heSiFeiKongService;
    @Autowired
    private EkbUserService ekbUserService;
    @Resource
    private SyncEkbRoleTask syncEkbRoleTask;

    @ApiOperation("免登陆首页")
    @GetMapping("/login")
    public Map<String, Object> login(String corpId, String authCode, Integer client) throws Exception {
        log.info("corPid:" + corpId + " code:" + authCode + " client:" + client);
        String s = heSiFeiKongService.toLogin(corpId, authCode, client);
        log.info(s);
        Map<String, Object> map = new HashMap<>();
        map.put("url", s);
        map.put("data", s);
        return map;
    }

    @ApiOperation("接收易快报事件")
    @PostMapping("/receiveEkbEvent")
    public ApiResponse receiveEkbEvent(@RequestBody EkbReceiveEkbEventRequest ekbReceiveEkbEventRequest) {
        log.info("请求参数{}", JSON.toJSONString(ekbReceiveEkbEventRequest));
        ApiResponse apiResponse = heSiFeiKongService.receiveEkbEvent(ekbReceiveEkbEventRequest);
        //清空响应实体
        apiResponse.setData(null);
        return apiResponse;
    }

    @ApiOperation("更新易快报角色下员工信息")
    @GetMapping("/syncEkbRoleUserUpdate")
    public void syncEkbRoleUserUpdate() {
        log.info("更新易快报角色下员工信息开始");
        ekbUserService.updateRoleUserInfo();
        log.info("更新易快报角色下员工信息结束");
    }

    @ApiOperation("同步易快报领导角色")
    @GetMapping("/syncEkbLeaderRole")
    public void syncEkbLeaderRole() {
        log.info("更新易快报领导角色开始");
        syncEkbRoleTask.syncLeader();
        log.info("更新易快报领导角色结束");
    }

}

