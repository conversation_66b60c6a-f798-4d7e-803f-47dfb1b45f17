package com.qmqb.oa.system.service.impl;

import cn.hutool.core.util.ObjectUtil;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.qmqb.oa.system.domain.RepayDetail;
import com.qmqb.oa.system.service.RepayDetailService;
import com.qmqb.oa.system.service.SftpService;
import com.qmqb.oa.common.utils.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Properties;
import java.util.Vector;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * SFTP服务实现类
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class SftpServiceImpl implements SftpService {

    private static final Logger log = LoggerFactory.getLogger(SftpServiceImpl.class);

    @Value("${sftp.host}")
    private String sftpHost;

    @Value("${sftp.port}")
    private int sftpPort;

    @Value("${sftp.username}")
    private String sftpUsername;

    @Value("${sftp.password}")
    private String sftpPassword;

    @Autowired
    private RepayDetailService repayDetailService;

    @Override
    public void downloadFile(String filePath, String fileName, HttpServletRequest request, HttpServletResponse response) {
        Session session = null;
        ChannelSftp channelSftp = null;
        InputStream inputStream = null;

        try {
            // 获取SFTP连接
            SftpConnection sftpConnection = createSftpConnection();
            session = sftpConnection.getSession();
            channelSftp = sftpConnection.getChannelSftp();

            if (ObjectUtil.isEmpty(fileName)) {
                fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            }

            // 获取SFTP上的文件
            try {
                inputStream = channelSftp.get(filePath);
            } catch (Exception e) {
                handleErrorResponse(response, "文件不存在");
                return;
            }

            // 下载文件
            downloadSingleFile(inputStream, fileName, request, response);

        } catch (Exception e) {
            handleErrorResponse(response, "文件下载失败：" + e.getMessage());
        } finally {
            closeSftpConnection(inputStream, channelSftp, session);
        }
    }

    @Override
    public void downloadFolder(String folderPath, String zipFileName, HttpServletRequest request, HttpServletResponse response) {
        Session session = null;
        ChannelSftp channelSftp = null;
        ZipOutputStream zipOut = null;

        try {
            // 获取SFTP连接
            SftpConnection sftpConnection = createSftpConnection();
            session = sftpConnection.getSession();
            channelSftp = sftpConnection.getChannelSftp();

            // 验证文件夹是否存在
            try {
                channelSftp.ls(folderPath);
            } catch (SftpException e) {
                handleErrorResponse(response, "文件夹不存在");
                return;
            }

            // 设置响应头
            setZipResponseHeader(response, request, zipFileName);

            // 创建ZIP输出流
            zipOut = new ZipOutputStream(response.getOutputStream());

            // 递归下载文件夹内容
            downloadFolderRecursively(channelSftp, folderPath, "", zipOut);

            zipOut.flush();

        } catch (Exception e) {
            handleErrorResponse(response, "文件夹下载失败：" + e.getMessage());
        } finally {
            closeZipConnection(zipOut, channelSftp, session);
        }
    }

    @Override
    public void downloadRepayDetailFolder(List<String> batchNos, HttpServletRequest request, HttpServletResponse response) {
        //查找批次号信息
        LambdaQueryWrapper<RepayDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RepayDetail::getBatchNo, batchNos);
        List<RepayDetail> repayDetails = repayDetailService.list(queryWrapper).stream().filter(repayDetail -> StrUtil.isNotBlank(repayDetail.getFilePath())).collect(Collectors.toList());
        //下载文件
        if (repayDetails.isEmpty()) {
            throw new ServiceException("没有找到批次号对应的文件");
        }
        try {
            // 设置ZIP文件名
            String zipFileName = "对账明细文件_" + System.currentTimeMillis();
            setZipResponseHeader(response, request, zipFileName);
            // 创建SFTP连接
            SftpConnection sftpConnection = createSftpConnection();
            Session session = sftpConnection.getSession();
            ChannelSftp channelSftp = sftpConnection.getChannelSftp();
            // 创建ZIP输出流
            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());
            // 遍历所有文件夹并添加到ZIP
            for (RepayDetail repayDetail : repayDetails) {
                String folderPath = repayDetail.getFilePath();
                try {
                    downloadFolderRecursively(channelSftp, folderPath, repayDetail.getBatchNo() + "/", zipOut);
                } catch (SftpException e) {
                    log.error("添加文件夹到ZIP失败: {}", folderPath, e);
                }
            }
            zipOut.flush();
            closeZipConnection(zipOut, channelSftp, session);
        } catch (Exception e) {
            throw new ServiceException("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 创建SFTP连接
     */
    private SftpConnection createSftpConnection() throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(sftpUsername, sftpHost, sftpPort);
        session.setPassword(sftpPassword);

        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        session.connect();

        ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
        channelSftp.connect();

        return new SftpConnection(session, channelSftp);
    }

    /**
     * 下载单个文件
     */
    private void downloadSingleFile(InputStream inputStream, String fileName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" +
                FileUtils.setFileDownloadHeader(request, fileName));

        byte[] buffer = new byte[1024];
        int len;
        while ((len = inputStream.read(buffer)) > 0) {
            response.getOutputStream().write(buffer, 0, len);
        }
        response.getOutputStream().flush();
    }

    /**
     * 设置ZIP响应头
     */
    private void setZipResponseHeader(HttpServletResponse response, HttpServletRequest request, String zipFileName) throws IOException {
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=" +
        FileUtils.setFileDownloadHeader(request, zipFileName + ".zip"));
    }

    /**
     * 处理错误响应
     */
    private void handleErrorResponse(HttpServletResponse response, String message) {
        try {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write(message);
        } catch (Exception ex) {
            log.error("响应写入失败", ex);
        }
    }

    /**
     * 关闭SFTP连接
     */
    private void closeSftpConnection(InputStream inputStream, ChannelSftp channelSftp, Session session) {
        try {
            if (inputStream != null) {
                inputStream.close();
            }
            if (channelSftp != null) {
                channelSftp.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        } catch (Exception e) {
            log.error("关闭SFTP连接失败", e);
        }
    }

    /**
     * 关闭ZIP连接
     */
    private void closeZipConnection(ZipOutputStream zipOut, ChannelSftp channelSftp, Session session) {
        try {
            if (zipOut != null) {
                zipOut.close();
            }
            if (channelSftp != null) {
                channelSftp.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        } catch (Exception e) {
            log.error("关闭连接失败", e);
        }
    }

    /**
     * 递归下载文件夹内容
     */
    private void downloadFolderRecursively(ChannelSftp channelSftp, String path, String parent, ZipOutputStream zipOut) throws Exception {
        Vector<?> files = channelSftp.ls(path);

        for (Object obj : files) {
            ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) obj;
            String filename = entry.getFilename();

            if (".".equals(filename) || "..".equals(filename)) {
                continue;
            }

            String currentPath = path + "/" + filename;
            String zipPath = parent.isEmpty() ? filename : parent + "/" + filename;

            if (entry.getAttrs().isDir()) {
                downloadFolderRecursively(channelSftp, currentPath, zipPath, zipOut);
            } else {
                addFileToZip(channelSftp, currentPath, zipPath, zipOut);
            }
        }
    }

    /**
     * 添加文件到ZIP
     */
    private void addFileToZip(ChannelSftp channelSftp, String currentPath, String zipPath, ZipOutputStream zipOut) throws Exception {
        zipOut.putNextEntry(new ZipEntry(zipPath));
        InputStream is = null;
        try {
            is = channelSftp.get(currentPath);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) > 0) {
                zipOut.write(buffer, 0, len);
            }
        } finally {
            if (is != null) {
                is.close();
            }
            zipOut.closeEntry();
        }
    }

    /**
     * SFTP连接包装类
     */
    private static class SftpConnection {
        private final Session session;
        private final ChannelSftp channelSftp;

        public SftpConnection(Session session, ChannelSftp channelSftp) {
            this.session = session;
            this.channelSftp = channelSftp;
        }

        public Session getSession() {
            return session;
        }

        public ChannelSftp getChannelSftp() {
            return channelSftp;
        }
    }
} 