package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.system.domain.ProcessRecord;
import com.qmqb.oa.system.mapper.ProcessRecordMapper;
import com.qmqb.oa.system.service.ProcessRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 审批实例处理记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@Service
public class ProcessRecordServiceImpl extends ServiceImpl<ProcessRecordMapper, ProcessRecord> implements ProcessRecordService {

    @Autowired
    private ProcessRecordMapper processRecordMapper;

    /**
     * 根据示例id查询
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public ProcessRecord getByProcessInstanceId(String processInstanceId) {
        return this.getOne(new QueryWrapper<ProcessRecord>().eq("process_instance_id", processInstanceId));
    }

    /**
     * 查询审批处理记录列表
     *
     * @param processRecord 审批处理记录
     * @return 审批处理记录
     */
    @Override
    public List<ProcessRecord> selectProcessRecordList(ProcessRecord processRecord) {
        return processRecordMapper.selectProcessRecordList(processRecord);
    }
}
