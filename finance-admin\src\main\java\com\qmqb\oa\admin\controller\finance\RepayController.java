package com.qmqb.oa.admin.controller.finance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qmqb.oa.admin.domain.request.RepayDeatilRequest;
import com.qmqb.oa.admin.domain.vo.RepayDetailVo;
import com.qmqb.oa.admin.domain.vo.RepayGenRecordVo;
import com.qmqb.oa.admin.domain.vo.RepayStatManualExecuteVo;
import com.qmqb.oa.admin.service.RepayDetailBizService;
import com.qmqb.oa.admin.service.RepayStatisticService;
import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.constant.RepaymentGenConstant;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.system.domain.RepayDetail;
import com.qmqb.oa.system.service.RepayDetailService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 科目Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@Api("还款统计相关前端控制器")
@RestController
@RequestMapping("/finance/repayStat")
public class RepayController extends BaseController {
    @Autowired
    private RepayStatisticService repayStatisticService;
    @Autowired
    private RepayDetailBizService repayDetailBizService;

    @ApiOperation("触发统计")
    @PreAuthorize("@ss.hasPermi('finance:reconciliation:addTask')")
    @PostMapping("/triggerStat")
    public AjaxResult triggerStat(@RequestBody RepayStatManualExecuteVo vo) {
        if (repayStatisticService.triggerStat(vo, RepaymentGenConstant.MANUAL_GEN)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error("执行失败");
        }
    }
    @ApiOperation("列出所有生成记录")
    @GetMapping("/list/genRecord")
    public TableDataInfo listGenRecord(RepayGenRecordVo vo) {
        startPage();
        List<RepayGenRecordVo> results = repayStatisticService.listRecord(vo);
        return getDataTable(results);
    }

    @Log(title = "用友信息导出", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('finance:reconciliation:export')")
    @GetMapping("/detailExport")
    public AjaxResult detailExport(String batchNo) {
        return repayStatisticService.detailExport(batchNo);
    }

    @Log(title = "金蝶云信息导出", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('finance:reconciliation:export')")
    @GetMapping("/detailExportJinDie")
    public AjaxResult detailExportJinDie(String batchNo) {
        return repayStatisticService.detailExportJinDie(batchNo);
    }

    @ApiOperation("列出对账明细")
    @GetMapping("/listRepayDetail")
    public IPage<RepayDetailVo> listRepayDetail(RepayDeatilRequest repayDeatilRequest){
        IPage<RepayDetailVo> repayDetailList = repayDetailBizService.listRepayDetail(repayDeatilRequest);
        return repayDetailList;
    }
}
