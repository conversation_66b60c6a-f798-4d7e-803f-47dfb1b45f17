package com.qmqb.oa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.Subject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 科目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
public interface SubjectMapper extends BaseMapper<Subject> {

    /**
     * 查询科目信息
     *
     * @param id
     * @return
     */
    Subject selectSubjectById(Long id);

    /**
     * 查询科目列表
     *
     * @param subject
     * @return
     */
    List<Subject> selectSubjectList(Subject subject);

    /**
     * 查询科目信息
     *
     * @param subjectName
     * @param dingTalkDeptId
     * @return
     */
    Subject selectBySubjectNameAndDingTalkDeptId(@Param("subjectName") String subjectName, @Param("dingTalkDeptId") String dingTalkDeptId);
}
