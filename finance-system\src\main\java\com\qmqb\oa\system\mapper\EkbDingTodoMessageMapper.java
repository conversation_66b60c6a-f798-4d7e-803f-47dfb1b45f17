package com.qmqb.oa.system.mapper;

import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 易快报到钉钉消息二次处理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
public interface EkbDingTodoMessageMapper extends BaseMapper<EkbDingTodoMessage> {
    /**
     * 修改remark字段扩展信息
     * @param ids
     * @param params
     * @return
     */
    int setMultiRemarkInfo(@Param("ids") Long[] ids, @Param("params") Map<String, Object> params);

    /**
     * 根据单据流程id和用户id集合查询待办任务列表
     * @param flowId
     * @param userIdSet
     * @return
     */
    List<EkbDingTodoMessage> todoListByFlowIdAndUserIdSet(@Param("flowId") String flowId, @Param("userIdSet") Set<String> userIdSet);
}
