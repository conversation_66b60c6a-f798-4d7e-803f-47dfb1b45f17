package com.qmqb.oa.admin.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiRoleListResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.DingTalkDept;
import com.qmqb.oa.system.domain.DingTalkRole;
import com.qmqb.oa.system.domain.DingTalkRoleGroup;
import com.qmqb.oa.system.domain.DingTalkUser;
import com.qmqb.oa.system.service.DingTalkDeptService;
import com.qmqb.oa.system.service.DingTalkRoleGroupService;
import com.qmqb.oa.system.service.DingTalkRoleService;
import com.qmqb.oa.system.service.DingTalkUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步通讯录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Slf4j
@Component("syncDingTalkAddressBookTask")
@RequiredArgsConstructor
public class SyncDingTalkAddressBookTask {

    private final DingTalkOApiClient dingTalkOApiClient;
    private final DingTalkRoleGroupService dingTalkRoleGroupService;
    private final DingTalkRoleService dingTalkRoleService;
    private final DingTalkDeptService dingTalkDeptService;
    private final DingTalkUserService dingTalkUserService;

    /**
     * 同步角色列表
     */
    public void syncRole() {
        for (Tenant tenant : Tenant.company()) {
            try {
                Integer tenantId = tenant.getValue();
                TenantContextHolder.setTenantId(tenantId);
                MdcUtil.setTrace();
                MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "角色列表");
                List<OapiRoleListResponse.OpenRoleGroup> data = dingTalkOApiClient.listRole(null, null);
                List<DingTalkRoleGroup> groups = Lists.newArrayList();
                List<DingTalkRole> roles = Lists.newArrayList();
                List<DingTalkRoleGroup> dbGroups = dingTalkRoleGroupService.list();
                List<DingTalkRole> dbRoles = dingTalkRoleService.list();
                for (OapiRoleListResponse.OpenRoleGroup group : data) {
                    Long groupId = group.getGroupId();
                    String name = group.getName();
                    DingTalkRoleGroup saveRoleGroup = new DingTalkRoleGroup();
                    dbGroups.stream()
                            .filter(e -> Objects.equals(e.getGroupId(), groupId))
                            .findAny()
                            .ifPresent(dingTalkRoleGroup -> saveRoleGroup.setId(dingTalkRoleGroup.getId()));
                    saveRoleGroup.setGroupId(groupId);
                    saveRoleGroup.setGroupName(name);
                    saveRoleGroup.setTenantId(tenantId);
                    groups.add(saveRoleGroup);
                    for (OapiRoleListResponse.OpenRole role : group.getRoles()) {
                        DingTalkRole saveRole = new DingTalkRole();
                        dbRoles.stream()
                                .filter(e -> Objects.equals(e.getRoleId(), role.getId()))
                                .findAny()
                                .ifPresent(dingTalkRole -> saveRole.setId(dingTalkRole.getId()));
                        saveRole.setRoleId(role.getId());
                        saveRole.setRoleName(role.getName());
                        saveRole.setGroupId(groupId);
                        saveRole.setGroupName(name);
                        saveRole.setTenantId(tenantId);
                        roles.add(saveRole);
                    }
                }
                dingTalkRoleGroupService.saveOrUpdateBatch(groups);
                dingTalkRoleService.saveOrUpdateBatch(roles);
            } catch (Exception e) {
                log.error("同步角色列表异常:{}", tenant.getName(), e);
            } finally {
                MdcUtil.clear();
                TenantContextHolder.clearTenantId();
            }
        }
    }

    /**
     * 同步部门列表
     */
    public void syncDept() {
        for (Tenant tenant : Tenant.company()) {
            try {
                Integer tenantId = tenant.getValue();
                TenantContextHolder.setTenantId(tenantId);
                MdcUtil.setTrace();
                MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "部门列表");
                DingTalkDept parent = dingTalkDeptService.getOne(Wrappers.lambdaQuery(DingTalkDept.class)
                        .eq(DingTalkDept::getDeptId, 1)
                        .eq(DingTalkDept::getParentId, 0)
                        .eq(DingTalkDept::getTenantId, tenantId));
                if (Objects.isNull(parent)) {
                    parent = new DingTalkDept();
                    parent.setDeptId(1L);
                    parent.setParentId(0L);
                    parent.setDeptLevel(0);
                    parent.setDeptName(tenant.getName());
                    parent.setTenantId(tenant.getValue());
                    dingTalkDeptService.save(parent);
                }
                List<DingTalkDept> dbDepts = dingTalkDeptService.list(Wrappers.lambdaQuery(DingTalkDept.class).eq(DingTalkDept::getTenantId, tenantId));
                // 目前最多6级
                int level = 6;
                for (int i = 1; i <= level; i++) {
                    List<DingTalkDept> depts = dingTalkDeptService.list(Wrappers.lambdaQuery(DingTalkDept.class)
                            .eq(DingTalkDept::getTenantId, tenantId)
                            .eq(DingTalkDept::getDeptLevel, i));
                    if (CollUtil.isEmpty(depts)) {
                        log.info("无部门数据");
                        return;
                    }
                    for (DingTalkDept dept : depts) {
                        List<OapiV2DepartmentListsubResponse.DeptBaseResponse> list = dingTalkOApiClient.listDepartment(dept.getDeptId());
                        if (CollUtil.isNotEmpty(list)) {
                            List<DingTalkDept> saveDepts = list.stream().map(d -> {
                                DingTalkDept saveDept = new DingTalkDept();
                                dbDepts.stream()
                                        .filter(dbDept -> Objects.equals(dbDept.getDeptId(), d.getDeptId()))
                                        .findAny()
                                        .ifPresent(o -> saveDept.setId(o.getId()));
                                saveDept.setParentId(d.getParentId());
                                saveDept.setDeptLevel(dept.getDeptLevel() + 1);
                                saveDept.setDeptId(d.getDeptId());
                                saveDept.setDeptName(d.getName());
                                saveDept.setSourceIdentifier(d.getSourceIdentifier());
                                saveDept.setAutoAddUser(d.getAutoAddUser());
                                saveDept.setCreateDeptGroup(d.getCreateDeptGroup());
                                saveDept.setTenantId(tenantId);
                                return saveDept;
                            }).collect(Collectors.toList());
                            dingTalkDeptService.saveOrUpdateBatch(saveDepts);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("同步部门列表异常:{}", tenant.getName(), e);
            } finally {
                MdcUtil.clear();
                TenantContextHolder.clearTenantId();
            }
        }
    }


    /**
     * 同步部门用户详情
     */
    public void syncUser() {
        for (Tenant tenant : Tenant.company()) {
            try {
                Integer tenantId = tenant.getValue();
                TenantContextHolder.setTenantId(tenantId);
                MdcUtil.setTrace();
                MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "用户列表");
                List<DingTalkDept> dbDepts = dingTalkDeptService.list(Wrappers.lambdaQuery(DingTalkDept.class).eq(DingTalkDept::getTenantId, tenantId));
                List<DingTalkUser> dbUsers = dingTalkUserService.list(Wrappers.lambdaQuery(DingTalkUser.class).eq(DingTalkUser::getTenantId, tenantId));
                List<DingTalkUser> all = new ArrayList<>();
                for (DingTalkDept dept : dbDepts) {
                    List<OapiV2UserListResponse.ListUserResponse> list = dingTalkOApiClient.listUser(dept.getDeptId(), null);
                    List<DingTalkUser> users = list.stream().map(e -> {
                        DingTalkUser saveUser = new DingTalkUser();
                        dbUsers.stream()
                                .filter(o -> Objects.equals(o.getUserId(), e.getUserid()))
                                .findAny()
                                .ifPresent(dingTalkUser -> saveUser.setId(dingTalkUser.getId()));
                        saveUser.setUserId(e.getUserid());
                        saveUser.setUserName(e.getName());
                        saveUser.setAvatar(e.getAvatar());
                        saveUser.setMobile(e.getMobile());
                        saveUser.setStateCode(e.getStateCode());
                        saveUser.setTelephone(e.getTelephone());
                        saveUser.setJobNumber(e.getJobNumber());
                        saveUser.setEmail(e.getEmail());
                        saveUser.setOrgEmail(e.getOrgEmail());
                        saveUser.setTitle(e.getTitle());
//                user.setRoleList();
                        saveUser.setDeptIdList(JacksonUtil.toJson(e.getDeptIdList()));
                        saveUser.setDeptOrderList(String.valueOf(e.getDeptOrder()));
//                user.setLeaderInDept();
//                user.setManagerUserid();
                        saveUser.setHiredDate(Objects.nonNull(e.getHiredDate()) ? LocalDateTimeUtil.of(e.getHiredDate()) : null);
                        saveUser.setWorkPlace(e.getWorkPlace());
                        saveUser.setLeader(e.getLeader());
                        saveUser.setBoss(e.getBoss());
                        saveUser.setExclusiveAccount(e.getExclusiveAccount());
                        saveUser.setAdmin(e.getAdmin());
                        saveUser.setActive(e.getActive());
                        saveUser.setHideMobile(e.getHideMobile());
//                user.setSenior();
//                user.setRealAuthed(e);
                        saveUser.setUnionId(e.getUnionid());
//                user.setUnionEmpExt(e.getU);
                        saveUser.setExtension(e.getExtension());
                        saveUser.setRemark(e.getRemark());
                        saveUser.setTenantId(tenantId);
                        return saveUser;
                    }).collect(Collectors.toList());
                    all.addAll(users);
                }
                all = all.stream()
                        .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(DingTalkUser::getUserId))), ArrayList::new));
                dingTalkUserService.saveOrUpdateBatch(all);
            } catch (Exception e) {
                log.error("同步用户列表异常:{}", tenant.getName(), e);
            } finally {
                MdcUtil.clear();
                TenantContextHolder.clearTenantId();
            }
        }
    }
}
