<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.SubjectMapper">
    <sql id="selectSubjectVo">
        SELECT s.id,
               s.parent_subject_code,
               s.subject_code,
               s.subject_name,
               s.direction,
               s.remark,
               s.create_by,
               s.create_time,
               s.update_by,
               s.update_time,
               s.is_deleted,
               d.dept_id,
               d.parent_id,
               d.ancestors,
               d.dept_name,
               d.order_num,
               d.leader,
               d.phone,
               d.email,
               d.status,
               d.dept_code,
               d.ding_talk_dept_id,
               d.del_flag,
               d.create_by,
               d.create_time
        FROM t_subject s
               LEFT JOIN t_subject_dept sd ON s.id = sd.subject_id
               LEFT JOIN sys_dept d ON sd.dept_id = d.dept_id
    </sql>


    <resultMap type="com.qmqb.oa.system.domain.Subject" id="SubjectResult">
        <result property="id" column="id"/>
        <result property="parentSubjectCode" column="parent_subject_code"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="subjectName" column="subject_name"/>
        <result property="direction" column="direction"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <collection property="depts" javaType="java.util.List" resultMap="SysDeptResult"/>
    </resultMap>

    <resultMap type="com.qmqb.oa.common.core.domain.entity.SysDept" id="SysDeptResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="deptCode" column="dept_code"/>
        <result property="dingTalkDeptId" column="ding_talk_dept_id"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectSubjectList" parameterType="com.qmqb.oa.system.domain.Subject" resultMap="SubjectResult">
        <include refid="selectSubjectVo">
        </include>
        <where>
            is_deleted = 0
            <if test="parentSubjectCode != null  and parentSubjectCode != ''">
                and parent_subject_code = #{parentSubjectCode}
            </if>
            <if test="subjectCode != null  and subjectCode != ''">
                and subject_code = #{subjectCode}
            </if>
            <if test="subjectName != null  and subjectName != ''">
                and subject_name like concat('%', #{subjectName}, '%')
            </if>
            <if test="tenantId != null  and tenantId != ''">
                and tenant_id = #{tenantId}
            </if>
            <if test="direction != null">
                and direction = #{direction}
            </if>
        </where>
    </select>

    <select id="selectSubjectById" parameterType="com.qmqb.oa.system.domain.Subject" resultMap="SubjectResult">
        <include refid="selectSubjectVo"/>
        WHERE s.id = #{id}
    </select>

    <select id="selectBySubjectNameAndDingTalkDeptId" resultType="com.qmqb.oa.system.domain.Subject">
        SELECT s.id,
               s.parent_subject_code,
               s.subject_code,
               s.subject_name,
               s.direction,
               s.remark,
               s.create_by,
               s.create_time,
               s.update_by,
               s.update_time
        FROM `t_subject` s
                     LEFT JOIN t_subject_dept sd ON s.id = sd.subject_id
                     LEFT JOIN sys_dept d ON d.dept_id = sd.dept_id
        WHERE d.ding_talk_dept_id = #{dingTalkDeptId}
          AND s.subject_name = #{subjectName}
    </select>
</mapper>
