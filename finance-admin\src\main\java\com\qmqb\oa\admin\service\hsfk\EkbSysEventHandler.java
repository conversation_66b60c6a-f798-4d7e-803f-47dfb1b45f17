package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.common.util.date.DateUtil;
import com.hzed.structure.tool.util.JacksonUtil;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAddDeptReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchAddDeptReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchAddUserListReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchUpdateUserListReq;
import com.qmqb.oa.common.enums.EkbEventType;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 易快报事件处理handler
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@Slf4j
@Component
public class EkbSysEventHandler {
    @Autowired
    private EkbUserService ekbUserService;
    @Autowired
    private SysEventService sysEventService;
    @Autowired
    private EkbDeptService ekbDeptService;

    @Autowired
    private HeSiFeiKongService heSiFeiKongService;
    public void dealFailedEvent(SysEvent sysEvent) {
        if (sysEvent.getEventSource() == 1) {
            log.info("无法处理非易快报事件");
            return;
        }
        if (EkbEventType.USER_BATCH_ADD.getEventType().equals(sysEvent.getEventType())) {
            //新增员工
            this.dealEvent4BatchAddUserList(sysEvent);
        } else if (EkbEventType.USER_BATCH_MODIFY.getEventType().equals(sysEvent.getEventType())) {
            //修改员工
            this.dealEvent4BatchUpdateUserList(sysEvent);
        } else if (EkbEventType.USER_BATCH_DELETE.getEventType().equals(sysEvent.getEventType())) {
            //删除员工
            this.dealEvent4DisableOrEnableStaff(sysEvent);
        } else if (EkbEventType.ROLE_USER_UPDATE.getEventType().equals(sysEvent.getEventType())) {
            //更新角色下员工信息
            this.dealEvent4UpdateRoleUserInfo(sysEvent);
        }else if (EkbEventType.DEPT_BATCH_CREATE.getEventType().equals(sysEvent.getEventType())) {
            // 批量新增部门
            batchAddDeptRetry(sysEvent);
        } else if (EkbEventType.DEPT_BATCH_MODIFY.getEventType().equals(sysEvent.getEventType())) {
            // 修改部门
            updateDeptRetry(sysEvent);
        } else if (EkbEventType.DEPT_BATCH_DELETE.getEventType().equals(sysEvent.getEventType())) {
            // 删除部门
            deleteDeptRetry(sysEvent);
        } else if (EkbEventType.EKB_EVENT_NOTICE.getEventType().equals(sysEvent.getEventType())) {
            //ekb 事件通知钉钉发消息
            EkbReceiveEkbEventRequest ekbEventRequest = JacksonUtil.parse(sysEvent.getReqData(), EkbReceiveEkbEventRequest.class);
            //系统创建时间作为提交时间
            ekbEventRequest.setSubmitDate(DateTimeUtil.getUnixTimestamp(sysEvent.getCreateTime()));
            heSiFeiKongService.receiveEkbEvent(ekbEventRequest);
        }
    }

    /**
     * 获取异常信息
     * @param e
     * @return
     */
    private String getErrorMsg(Exception e) {
        String errorMsg = com.qmqb.oa.common.utils.StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "";
        return errorMsg.length() > 100 ? errorMsg.substring(0, 100) : errorMsg;
    }

    /**
     * 批量新增用户事件
     *
     * @param sysEvent
     */
    private void dealEvent4BatchAddUserList(SysEvent sysEvent) {
        try {
            EkbBatchAddUserListReq req = JSON.parseObject(sysEvent.getReqData(), EkbBatchAddUserListReq.class);
            ekbUserService.batchAddUser(req);
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报批量新增用户事件】异常", e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }
    }

    /**
     * 停启用员工事件
     *
     * @param sysEvent
     */
    private void dealEvent4DisableOrEnableStaff(SysEvent sysEvent) {
        try {
            List<String> userIdList = JSON.parseObject(sysEvent.getReqData(), List.class);
            ekbUserService.disableOrEnableStaff(userIdList);
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报批量停启用用户事件】异常", e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }
    }

    /**
     * 批量修改用户事件
     *
     * @param sysEvent
     */
    private void dealEvent4BatchUpdateUserList(SysEvent sysEvent) {
        try {
            EkbBatchUpdateUserListReq req = JSON.parseObject(sysEvent.getReqData(), EkbBatchUpdateUserListReq.class);
            ekbUserService.batchUpdateUser(req);
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报批量修改用户事件】异常", e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }
    }

    /**
     * 更新角色下员工信息事件
     *
     * @param sysEvent
     */
    private void dealEvent4UpdateRoleUserInfo(SysEvent sysEvent) {
        try {
            ekbUserService.updateRoleUserInfo();
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报批量修改用户事件】异常", e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }
    }


    /**
     * 批量添加部门事件补偿
     * @param sysEvent
     */
    private void batchAddDeptRetry(SysEvent sysEvent) {
        try {
            EkbBatchAddDeptReq req = JSON.parseObject(sysEvent.getReqData(), EkbBatchAddDeptReq.class);
            ekbDeptService.batchAddDept(req);
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报批量新增部门事件补偿】异常!requestNo:{},reqData:{}", sysEvent.getRequestNo(), sysEvent.getReqData(), e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }

    }

    /**
     * 修改部门信息事件补偿
     * @param sysEvent
     */
    private void updateDeptRetry(SysEvent sysEvent) {
        try {
            EkbAddDeptReq req = JSON.parseObject(sysEvent.getReqData(), EkbAddDeptReq.class);
            ekbDeptService.updateDept(sysEvent.getReqParams(), req);
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报修改部门事件补偿】异常!requestNo:{},reqParam:{},reqData:{}", sysEvent.getRequestNo(), sysEvent.getReqParams(), sysEvent.getReqData(),  e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }

    }

    /**
     * 删除部门事件补偿
     * @param sysEvent
     */
    private void deleteDeptRetry(SysEvent sysEvent) {
        try {
            ekbDeptService.stopDept(sysEvent.getReqParams());
            sysEventService.update4SuccessById(sysEvent);
        } catch (Exception e) {
            log.error("【易快报-删除部门事件补偿】异常!requestNo:{},reqParam:{}", sysEvent.getRequestNo(), sysEvent.getReqParams(), e);
            String errorMsg = getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }

    }


}
