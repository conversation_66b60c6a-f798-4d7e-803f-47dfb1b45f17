package com.qmqb.oa.admin.service.ekb.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hzed.structure.common.api.ResultCode;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.common.util.ObjectUtil;
import com.hzed.structure.common.util.str.FaJsonUtil;
import com.hzed.structure.tool.api.ApiResponse;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.domain.dto.EkbEventCommandDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbDingTodoResultDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.common.enums.EkbActionEnum;
import com.qmqb.oa.common.enums.EkbFlowStageNameEnums;
import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description ekb 删除事件
 * @Date 2024\10\12 0012 10:50
 * @Version 1.0
 */

@Slf4j
@Service(value = "ekbDeleteCommand")
public class EkbDeleteEkbEventCommand extends AbstractEkbEventCommand<EkbReceiveEkbEventRequest, EkbEventCommandDTO> {

    @Override
    public boolean validation() {
        if (!super.checkBaseParams()) {
            return false;
        }

        if (!getParamObject().getAction().equals(EkbActionEnum.FREEFLOW_DELETE.getAction())) {
            return false;
        }

        return true;
    }

    @Override
    public ApiResponse<EkbEventCommandDTO> execute() {
        if (!validation()) {
            return ApiResponse.fail("delete事件参数校验失败");
        }
        if (eventHasExecute()) {
            return ApiResponse.success(ResultCode.SUCCESS, "ekb delete event deal success");
        }
        try {
            //状态有延迟
            Thread.sleep(1000);
        } catch (Exception ex) {
        }

        EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();
        List<String> msgActions = Lists.newArrayList(EkbActionEnum.BACKLOG_APPROVING.getAction(), EkbActionEnum.BACKLOG_PAYING.getAction());

        //查询整个单据的待办任务列表
        List<EkbDingTodoMessage> currentApprovingTodoMsgs = getCurrentDealTodoMsgList(request.getFlowId(), msgActions);
        if (CollUtil.isEmpty(currentApprovingTodoMsgs)) {
            return ApiResponse.fail("reject事件未找到对应的待审批数据列表");
        }

        //需要处理的钉钉待办任务列表
        List<EkbDingTodoMessage> necessaryDealEkbTasks = new ArrayList<>();
        for (EkbDingTodoMessage flow : currentApprovingTodoMsgs) {
            EkbDingTodoMessage addTodoMsg = null;
            if (StringUtil.isBlank(flow.getRemark())) {
                addTodoMsg = flow;
            } else {
                EkbDingTodoResultDTO todoResultDTO = FaJsonUtil.toObject(flow.getRemark(), EkbDingTodoResultDTO.class);
                //开发中或尚未提交的任务进行删除待办任务
                if (StringUtil.isBlank(todoResultDTO.getTaskProcessedResult()) || (StringUtil.equalsAny(todoResultDTO.getTaskProcessedResult(), EkbFlowStageNameEnums.DEV.getDbCode(), EkbFlowStageNameEnums.NO_SUBMIT.getDbCode()))) {
                    addTodoMsg = flow;
                }

            }
            if (ObjectUtil.isNotNull(addTodoMsg)) {
                necessaryDealEkbTasks.add(addTodoMsg);
            }
        }
        log.info("messageId{}删除事件，需要处理的事件列表{}", request.getMessageId(), JSON.toJSONString(necessaryDealEkbTasks));
        //更新易快报到钉钉待办消息表钉钉发送状态
        Map<Long, Boolean> batchUpdateResult = deleteTodoDingMsg(necessaryDealEkbTasks);
        ApiResponse apiResponse = null;
        Long[] taskId = new Long[1];
        boolean isExistsExecuteFailure = false;
        for (Map.Entry<Long, Boolean> updateResult : batchUpdateResult.entrySet()) {
            if (updateResult.getValue()) {
                taskId[0] = updateResult.getKey();
                //更新待办消息结果处理状态
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build().
                        setTaskProcessedResult(EkbFlowStageNameEnums.DELETE.getDbCode())
                        .setMsgAction(EkbActionEnum.FREEFLOW_DELETE.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
            } else {
                //失败的记录下最后执行的action
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build()
                        .setMsgAction(EkbActionEnum.FREEFLOW_DELETE.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
                isExistsExecuteFailure = true;
            }
        }

        //存在执行失败的任务
        if (isExistsExecuteFailure) {
            apiResponse = ApiResponse.fail();
        } else {
            apiResponse = ApiResponse.success(ResultCode.SUCCESS, "ekb delete event deal success");
        }

        //设置处理后的事件传输实体
        apiResponse.setData(getEkbEventCommandDTO());
        return apiResponse;
    }
}
