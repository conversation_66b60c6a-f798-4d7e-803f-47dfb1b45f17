package com.qmqb.oa.common.constant;

/**
 * <p>
 * 字符串常量池
 * </p>
 *
 * @since 2021-02-25
 */
public interface StringPool {
    String TRACE_ID = "TraceId";
    String TRACE_SPAN_ID = "SpanId";
    String TRACE_MODULE_NAME = "ModuleName";
    String TRACE_IP = "IpStr";
    String TRACE_URI = "UriPath";
    String TRACE_HOST_NAME = "HostName";
    String LOCK_PREFIX = "finance-lock:";

    String DEFAULT_STR = "default";

    String AMPERSAND = "&";
    String AND = "and";
    String AT = "@";
    String ASTERISK = "*";
    String ASTERISK_FOUR = "****";
    String STAR = ASTERISK;
    String BACK_SLASH = "\\";
    String DOUBLE_SLASH = "//";
    String COLON = ":";
    String DOUBLE_COLON = "::";
    String COMMA = ",";
    String DASH = "-";
    String DOLLAR = "$";
    String DOT = ".";
    String DOT_DOT = "..";
    String DOT_CLASS = ".class";
    String DOT_JAVA = ".java";
    String EMPTY = "";
    String EQUALS = "=";
    String FALSE = "false";
    String SLASH = "/";
    String HASH = "#";
    String HASH_SLASH = "#/";
    String HAT = "^";
    String LEFT_BRACE = "{";
    String LEFT_BRACKET = "(";
    String LEFT_CHEV = "<";
    String NEWLINE = "\n";
    String N = "n";
    String NO = "no";
    String NULL = "null";
    String NULL_DOT = "null.";
    String OFF = "off";
    String ON = "on";
    String PERCENT = "%";
    String PIPE = "|";
    String PLUS = "+";
    String QUESTION_MARK = "?";
    String EXCLAMATION_MARK = "!";
    String QUOTE = "\"";
    String QUOTE_FOUR = "\\\\";
    String RETURN = "\r";
    String TAB = "\t";
    String RIGHT_BRACE = "}";
    String RIGHT_BRACKET = ")";
    String RIGHT_CHEV = ">";
    String SEMICOLON = ";";
    String SINGLE_QUOTE = "'";
    String BACKTICK = "`";
    String SPACE = " ";
    String TILDA = "~";
    String LEFT_SQ_BRACKET = "[";
    String RIGHT_SQ_BRACKET = "]";
    String TRUE = "true";
    String UNDERSCORE = "_";
    String UTF_8 = "UTF-8";
    String GBK = "GBK";
    String US_ASCII = "US-ASCII";
    String ISO_8859_1 = "ISO-8859-1";
    String Y = "y";
    String YES = "yes";
    String ONE = "1";
    String ZERO = "0";
    String DOLLAR_LEFT_BRACE = "${";
    String CRLF = "\r\n";

    String EL_T = "T";

    String HTML_NBSP = "&nbsp;";
    String HTML_AMP = "&amp";
    String HTML_QUOTE = "&quot;";
    String HTML_LT = "&lt;";
    String HTML_GT = "&gt;";

    String UNKNOWN = "unknown";
    String GET = "GET";
    String POST = "POST";
    String NO_ERROR = "no error";

    String SERIAL_VERSION = "serialVersionUID";
    String SIGN = "sign";
    String TIMESTAMP = "timestamp";
    String EMPTY_JSON = "{}";
    String BASIC_SPACE = "Basic ";
    String BEARER_SPACE = "Bearer ";

    String SECRET_KEY = "&key=";
    String VERSION_PREFIX = "v";
    String VERSION = "version";
    String REQUEST_ID = "requestId";
    String SECRET_APP_KEY = "&appSecret=";
    String HTTP_PROTOCOL = "http://";
    String HTTPS_PROTOCOL = "https://";

    String DOT_COM = ".com";
    String DOT_OSS = ".oss";


    char U_A = 'A';
    char L_A = 'a';
    char U_Z = 'Z';
    char L_Z = 'z';
    char UNDERLINE = '_';
    char ASTERISK_CHAR = '*';
    char AT_CHAR = '@';

    /**
     * 空字符串
     *
     * @return
     */
    static String getEmptyStr() {
        return EMPTY;
    }
}
