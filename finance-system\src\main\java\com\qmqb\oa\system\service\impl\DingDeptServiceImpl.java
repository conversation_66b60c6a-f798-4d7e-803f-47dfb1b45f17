package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.system.domain.DingDept;
import com.qmqb.oa.system.mapper.DingDeptMapper;
import com.qmqb.oa.system.service.DingDeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 钉钉部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class DingDeptServiceImpl extends ServiceImpl<DingDeptMapper, DingDept> implements DingDeptService {

    @Override
    public Integer getMaxChildDeptLevel(String deptChainName, Integer companyId) {
        DingDept dept = this.getOne(Wrappers.lambdaQuery(DingDept.class)
                .likeRight(StringUtils.isNotBlank(deptChainName), DingDept::getDeptChainName, deptChainName)
                .orderByDesc(DingDept::getDeptLevel)
                .last(" limit 1")
        );
        if (Objects.isNull(dept)) {
            return 0;
        }
        return dept.getDeptLevel();
    }

    @Override
    public List<DingDept> childList(String deptChainName, Integer companyId, Integer maxLevel) {
        return this.list(Wrappers.lambdaQuery(DingDept.class)
                .likeRight(DingDept::getDeptChainName, deptChainName)
                .le(DingDept::getDeptLevel, maxLevel)
                .ge(DingDept::getDeptLevel, 2)
        );
    }
}
