package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qmqb.oa.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 报销凭证表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_reimbursement_voucher")
public class ReimbursementVoucher extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 审批实例ID
     */
    private String processInstanceId;

    /**
     * 制单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date makeDate;

    /**
     * 凭证类别: 0-记账凭证
     */
    private Integer voucherType;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 科目编码
     */
    private String subjectCode;

    /**
     * 币种: 0-人民币
     */
    private Integer currency;

    /**
     * 借贷方向: 0-借方, 1-贷方
     */
    private Integer borrowDirection;

    /**
     * 本币
     */
    private BigDecimal domesticCurrency;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 项目编码
     */
    private String itemCode;

    /**
     * 租户: 0-全民, 1-合众
     */
    private Integer tenantId;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 制单开始日期
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String beginDate;

    /**
     * 制单结束日期
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endDate;

    /**
     * 贷方科目编码
     */
    @TableField(exist = false)
    private String loanSubjectCode;

    /**
     * 导出制表日期
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM")
    private String exportMakeDate;

    /**
     * 往来单位编码
     */
    private String companyCode;

    /**
     * 往来单位
     */
    private String companyName;

    /**
     * 是否使用专用发票(0=否, 1=是)
     */
    private Integer specialInvoice;
}
