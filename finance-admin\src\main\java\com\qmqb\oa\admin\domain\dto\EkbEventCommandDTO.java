package com.qmqb.oa.admin.domain.dto;

import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.qmqb.oa.system.domain.EkbMessage;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description ekb 事件命令请求实体
 * @Date 2024\10\12 0012 9:21
 * @Version 1.0
 */

@Data
public class EkbEventCommandDTO {
    private EkbMessage ekbMessage;
    private EkbDingTodoMessage ekbDingTodoMessage;
    /**
     * 提交人
     */
    private DingUser submitterUser;
    /**
     * 当前操作人
     */
    private DingUser dingUser;

    /**
     * ekb 接收着钉钉映射实体
     */
    private EkbDingUserMapping ekbReceiverUser;
}
