package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 易快报事件类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Getter
@AllArgsConstructor
public enum EkbEventType {

    /**
     * 易快报事件类型
     */
    DEPT_BATCH_CREATE("dept_batch_create", "批量创建部门"),
    DEPT_BATCH_MODIFY("dept_batch_modify", "批量修改部门"),
    DEPT_BATCH_DELETE("dept_batch_remove", "批量删除部门"),
    USER_BATCH_ADD("user_batch_add", "批量新增员工"),
    USER_BATCH_MODIFY("user_batch_modify", "批量修改员工"),
    USER_BATCH_DELETE("user_batch_remove", "批量停用员工"),
    ROLE_USER_UPDATE("role_user_update", "更新角色下员工信息"),

    EKB_EVENT_NOTICE("ekb_event_notice", "ekb 事件通知")
    ;

    private final String eventType;
    private final String eventDesc;
}
