package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.SysEvent;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统事件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface SysEventService extends IService<SysEvent> {

    /**
     * 保存系统事件表
     * @param companyId 0-全民；4-OA
     * @param eventSource 事件源：1-钉钉；2-易快报
     * @param eventType
     * @param reqData
     * @param eventTime
     * @param param
     * @param failReason
     * @return requestNo
     */
    String saveSysEvent(Integer companyId,Integer eventSource, String eventType, String reqData, Long eventTime, String param, String failReason);

    /**
     * 根据requestNo更新事件状态
     * @param requestNo
     * @param status
     * @param failReason 失败原因,成功时可传null
     */
    void updateEventStatusByRequestNo(String requestNo, Integer status,String failReason);

    /**
     * 更新失败原因
     *
     * @param sysEvent
     * @param failReason
     */
    void updateFailReasonById(SysEvent sysEvent, String failReason);

    /**
     * 更新事件，状态=处理成功
     * @param sysEvent
     */
    void update4SuccessById(SysEvent sysEvent);

    /**
     * 根据公司id 事件原
     * @param companyId
     * @param eventSource
     * @param eventType
     * @param reqParams
     * @return
     */
    SysEvent getSysEventByTypeAndParams(Integer companyId,Integer eventSource, String eventType, String reqParams);

}
