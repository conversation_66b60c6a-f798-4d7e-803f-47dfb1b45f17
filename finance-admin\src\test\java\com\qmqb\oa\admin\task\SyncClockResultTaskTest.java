package com.qmqb.oa.admin.task;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.service.hsfk.DingTalkEventPolicyPatternService;
import com.qmqb.oa.admin.service.hsfk.DingTalkUserService;
import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.system.domain.DingTalkClockResult;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.DingTalkClockResultService;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import shade.com.alibaba.fastjson2.JSONObject;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
@Slf4j
public class SyncClockResultTaskTest extends BaseTest {

    @Autowired
    SyncClockResultTask task;
    @Autowired
    DingTalkClockResultService dingTalkClockResultService;

    @Test
    public void execute() {
//        LocalDate begin = LocalDate.of(2024, 4, 18);
//        LocalDate end = begin.plusDays(7);
//        LocalDate now = LocalDate.now();
//        for (int i = 0; i < 20; i++) {
//            if (end.isAfter(now)) {
//                end = now;
//            }
//            begin = end.plusDays(1);
//            end = end.plusDays(8);
//        }
        task.execute(Tenant.QMQB.getValue(), "2024-04-18");

    }

    @Test
    public void update() {
        List<DingTalkClockResult> list = dingTalkClockResultService.list(Wrappers.lambdaQuery(DingTalkClockResult.class).eq(DingTalkClockResult::getCheckType, "OffDuty"));
        List<DingTalkClockResult> updateList = new ArrayList<>();
        list.forEach(clockResult -> {
            LocalDateTime userCheckTime = LocalDateTimeUtil.of(clockResult.getUserCheckTime());
//            LocalDateTime at20 = LocalDateTime.of(LocalDateTimeUtil.of(clockResult.getBaseCheckTime()).toLocalDate(), LocalTime.of(20, 0, 0));
//            // 20点后打卡下班
//            if (userCheckTime.isAfter(at20) || userCheckTime.isEqual(at20)) {
//                clockResult.setIsAfter20(BoolFlagEnum.YES.getStatus());
//            }
            // 隔天凌晨00：00~05:00打卡下班
            LocalDateTime at0 = LocalDateTime.of(LocalDateTimeUtil.of(clockResult.getBaseCheckTime()).toLocalDate(), LocalTime.of(0, 0, 0));
            LocalDateTime at5 = LocalDateTime.of(LocalDateTimeUtil.of(clockResult.getBaseCheckTime()).toLocalDate(), LocalTime.of(5, 0, 0));
            boolean after0 = userCheckTime.isAfter(at0) || userCheckTime.isEqual(at0);
            boolean before5 = userCheckTime.isBefore(at5) || userCheckTime.isEqual(at5);
            if (after0 && before5) {
                clockResult.setIsAfter20(BoolFlagEnum.YES.getStatus());
                updateList.add(clockResult);
            }
        });
        dingTalkClockResultService.updateBatchById(updateList);
    }
    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Test
    public void addUser() throws Exception {
        String bizData = "{\"timeStamp\":\"1722915095192\",\"eventId\":\"4649c7850eb145f7b369a316e78f712b\",\"optStaffId\":\"033244674439346805\",\"userId\":[\"02496634065226100670\"]}";
        JSONObject jsonObject = JSONObject.parseObject(bizData);
        System.out.println("result====="+jsonObject.getString("userId"));
        dingTalkUserService.deleteUser(jsonObject, 4);
    }

    @Autowired
    private List<DingTalkEventPolicyPatternService> eventPolicyPatternService;
    @Autowired
    private SysEventService sysEventService;
    @Test
    public void test() throws Exception {
        String bizData = "{\"timeStamp\":\"1722915095192\",\"eventId\":\"4649c7850eb145f7b369a316e78f712b\",\"optStaffId\":\"033244674439346805\",\"userId\":[\"02496634065226100670\"]}";
        JSONObject jsonObject = JSONObject.parseObject(bizData);
        try {
            DingTalkEventPolicyPatternService service = eventPolicyPatternService.stream().filter(l ->
                    l.isCeLueModel("user_modify_org")).findFirst().orElse(null);
        //    service.toHandleEvent(jsonObject, 0);
            SysEvent sysEvent = new SysEvent();
            sysEvent.setReqData(bizData);
            service.toExecuteEvent(sysEvent);
        } catch (Exception e) {
            log.error("【{}】异常!", DingTalkEventType.getEventTypeByType("user_modify_org").getEventDesc(), e);
            String errorMsg = dingTalkUserService.getErrorMsg(e);
            // 保存事件表
            sysEventService.saveSysEvent(0, 1,"user_modify_org",JSON.toJSONString(jsonObject),
                    null, null, errorMsg);
        }
    }



}
