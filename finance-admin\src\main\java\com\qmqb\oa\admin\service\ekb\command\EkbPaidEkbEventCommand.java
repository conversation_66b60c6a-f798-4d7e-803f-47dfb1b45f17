package com.qmqb.oa.admin.service.ekb.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hzed.structure.common.api.ResultCode;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.common.util.NumberUtil;
import com.hzed.structure.common.util.ObjectUtil;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.common.util.str.FaJsonUtil;
import com.hzed.structure.tool.api.ApiResponse;
import com.hzed.structure.tool.util.JacksonUtil;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.domain.dto.DingSendNotifyMsgDTO;
import com.qmqb.oa.admin.domain.dto.EkbEventCommandDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbDingTodoResultDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.admin.domain.vo.ekb.EkbWorkFlowDetailRsp;
import com.qmqb.oa.common.constant.StringPool;
import com.qmqb.oa.common.enums.EkbActionEnum;
import com.qmqb.oa.common.enums.EkbFlowStageNameEnums;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import com.qmqb.oa.system.domain.EkbMessage;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description ekb 已支付/审批完成 事件
 * @Date 2024\10\12 0012 10:50
 * @Version 1.0
 */

@Slf4j
@Service(value = "ekbPaidCommand")
public class EkbPaidEkbEventCommand extends AbstractEkbEventCommand<EkbReceiveEkbEventRequest, EkbEventCommandDTO> {


    @Override
    public boolean validation() {

        if (!super.checkBaseParams()) {
            return false;
        }

        if (!getParamObject().getAction().equals(EkbActionEnum.FLOW_PAID.getAction())) {
            return false;
        }

        return true;
    }

    @Override
    public ApiResponse<EkbEventCommandDTO> execute() {
        if (!validation()) {
            return ApiResponse.fail("已支付/审批完成事件参数校验失败");
        }

        if (eventHasExecute()) {
            return ApiResponse.success(ResultCode.SUCCESS, "ekb paid event deal success");
        }

        EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();

        List<String> msgActions = Lists.newArrayList(EkbActionEnum.BACKLOG_APPROVING.getAction(), EkbActionEnum.BACKLOG_PAYING.getAction());
        List<EkbDingTodoMessage> currentApprovingTodoMsgs = getCurrentDealTodoMsgList(request.getFlowId(), msgActions);
        log.info("messageId:{},已支付/审批完成事件，需要处理的待办消息列表");
        if (CollUtil.isEmpty(currentApprovingTodoMsgs)) {
            return ApiResponse.fail("已支付事件未找到对应的待审批数据列表");
        }

        /**
         * 一、已支付节点，需要把单据涉及到的所有的钉钉待办消息全部更新成已处理
         */
        //需要处理的钉钉待办任务列表
        List<EkbDingTodoMessage> necessaryDealEkbTasks = new ArrayList<>();

        this.filterMessages(currentApprovingTodoMsgs, necessaryDealEkbTasks);

        log.info("messageId{}已支付事件，需要处理的消息列表:{}",request.getMessageId(),JSON.toJSONString(necessaryDealEkbTasks));
        //更新易快报到钉钉待办消息表钉钉发送状态
        Map<Long, Boolean> batchUpdateResult = updateTodoDingMsg(request, necessaryDealEkbTasks);
        ApiResponse apiResponse = null;
        Long[] taskId = new Long[1];
        boolean isExistsExecuteFailure = false;

        //更新待办消息的remark字段，taskProcessedResult,DEV("开发", "开发", "1"),PROCESSED("完成", "完成", "2"),DELETE("已删除", "已删除", "3"),
        // NO_SUBMIT("尚未提交", "尚未提交", "4"),REJECT("拒绝", "拒绝", "5"), RETRACT("撤回", "撤回", "6"),
        for (Map.Entry<Long, Boolean> updateResult : batchUpdateResult.entrySet()) {
            if (updateResult.getValue()) {
                taskId[0] = updateResult.getKey();
                //更新待办消息结果处理状态
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build().
                        setTaskProcessedResult(EkbFlowStageNameEnums.PROCESSED.getDbCode())
                        .setMsgAction(EkbActionEnum.FLOW_PAID.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
            } else {
                //失败的记录下最后执行的action
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build()
                        .setMsgAction(EkbActionEnum.FLOW_PAID.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
                isExistsExecuteFailure = true;
            }
        }

        /**
         * 二、已支付节点，需要对所有参与单据流的人员发送通知消息
         */
        //是否需要通知
        boolean isNecessaryNotice = true;
        //防止重复发消息
        if (CollUtil.isNotEmpty(necessaryDealEkbTasks)){
            if (doBiz4PaidMessage(request, eventCommandDTO, currentApprovingTodoMsgs, isNecessaryNotice)){
                return null;
            }
        }


        //存在执行失败的任务
        if (isExistsExecuteFailure) {
            apiResponse = ApiResponse.fail();
        } else {
            apiResponse = ApiResponse.success(ResultCode.SUCCESS, "ekb paid event deal success");
        }

        //设置处理后的事件传输实体
        apiResponse.setData(getEkbEventCommandDTO());
        return apiResponse;
    }

    /**
     * 处理支付消息的业务逻辑
     *
     * @param request            消息请求对象，包含流程和提交信息
     * @param eventCommandDTO    事件命令对象，包含事件详情和提交者信息
     * @param currentApprovingTodoMsgs 当前待处理的消息列表
     * @param isNecessaryNotice  是否需要通知提交者的标志
     * @return                   返回一个布尔值，表示消息处理的结果
     */
    private boolean doBiz4PaidMessage(EkbReceiveEkbEventRequest request, EkbEventCommandDTO eventCommandDTO, List<EkbDingTodoMessage> currentApprovingTodoMsgs, boolean isNecessaryNotice) {
        //给所有人发消息
        EkbWorkFlowDetailRsp detailRsp = getSyncEkbService().getEkbFlowDetailDescByFlowId(request.getFlowId());
        //消息内容
        String title = detailRsp.getValue().getForm().getTitle();

        //支付完成事件通知任务提交者
        String msgTitle = StringUtil.concat(true,  request.getSubmitterId().getName(), "提交的", request.getFormSpecification().specificationName);
        String iconImgId = Tenant.findByValue(eventCommandDTO.getDingUser().getCompanyId()).getPaidImgId();
        //金额描述
        String amountDec = getAmountDesc(request);

        Map<String, String> msgDescMap = new LinkedHashMap<>(4);
        String legalPerson = ObjectUtil.isNotNull(request.get法人实体()) ? JacksonUtil.toJson(request.get法人实体().getName()) : "";

        Set<String> msgDescLinkHashSet = getMsgDescLinkHashSet(request, title, amountDec, legalPerson);

        int index = 1;
        for (String msg : msgDescLinkHashSet) {
            //[0] 作为key [1] 作为value
            String[] msgArray = msg.split(StringPool.AT);
            msgDescMap.put(StringUtil.concat(true, String.valueOf(index), "、", msgArray[0]), msgArray[1] + " ");
            index ++;
        }

        //消息实体拼接
        String msgDesc = getMsgDesc(request, msgDescMap);
        DingUser submitterUser = eventCommandDTO.getSubmitterUser();
        List<String> sendUserList = new ArrayList<>();
        //给所有参与人发消息
        for(EkbDingTodoMessage ekbDingTodoMessage : currentApprovingTodoMsgs) {

            if (eventCommandDTO.getSubmitterUser().getUnionId().equals(ekbDingTodoMessage.getDingUnionId())) {
                //是否需要通知提交人
                isNecessaryNotice = false;
            }
            //保证消息内容唯一
            msgTitle = msgTitle + StringPool.EMPTY + DateTimeUtil.format(LocalDateTime.now(), DateTimeUtil.PATTERN_DATETIME);
            //查找ekb 用户
            DingUser dingUser = getDingUserService().findOneByCompanyIdAndUnionId(ekbDingTodoMessage.getCompanyId(), ekbDingTodoMessage.getDingUnionId());
            if (ObjectUtil.isNull(dingUser) || ObjectUtil.isNull(submitterUser) || StringUtil.isAnyBlank(dingUser.getUnionId(), submitterUser.getUnionId()) ) {
                log.warn("####### 钉钉用户不存在,unionId={}, submitUserId={}", ekbDingTodoMessage.getDingUnionId(), submitterUser.getId());
                return true;
            }
            EkbMessage ekbMessage = getEkbMessageService().findEkbMessageByFlowIdAndMessageId(ekbDingTodoMessage.getEkbFlowId(), ekbDingTodoMessage.getEkbMessageId());

            //防止重复发消息给同一个人
            if (sendUserList.contains(String.valueOf(dingUser.getId()))) {
                continue;
            }
            //更新易快报消息通知记录表
            DingSendNotifyMsgDTO notifyMsgDTO = DingSendNotifyMsgDTO.builder().build()
                    .setTitle(msgTitle)
                    .setUserId(dingUser.getUserId())
                    .setCompanyId(ekbDingTodoMessage.getCompanyId())
                    .setEkbMessageId(ekbDingTodoMessage.getEkbMessageId())
                    .setEkbUserId(ekbMessage.getUserId())
                    .setEkbFlowId(request.getFlowId())
                    .setText(msgDesc)
                    .setIconImgId(iconImgId);
            //更新结果
            boolean sendMsgDingResult = false;
            try {
                sendMsgDingResult = getDingMsgService().sendNotifyMsg(notifyMsgDTO);
                if (!sendMsgDingResult) {
                    log.info("###### send notice failure ,unionId = {}", dingUser.getUnionId());
                }
                sendUserList.add(String.valueOf(dingUser.getId()));
            } catch (Exception ex) {
                log.info("###### ekb {} send notice error ,msg = {}", eventCommandDTO.getEkbMessage().getMsgAction(), ex.getMessage());
            }
        }

        //给提交人发消息
        if (isNecessaryNotice && !sendUserList.contains(String.valueOf(submitterUser.getId()))) {
            this.sendNotifyMessage4Submmiter(request, eventCommandDTO, msgTitle, iconImgId, msgDesc, submitterUser);
        }
        return false;
    }

    /**
     * 构建消息描述的有序集合
     *
     * 该方法根据传入的请求对象和相关字符串参数，构造一个有序集合，用于存储消息描述
     * 主要用于整理费用明细的相关信息，包括标题、法人实体信息（如果提供）、费用明细描述和合计金额
     *
     * @param request EkbReceiveEkbEventRequest对象，包含费用明细的请求数据
     * @param title String类型，标题信息
     * @param amountDec String类型，合计金额的描述
     * @param legalPerson String类型，法人实体名称，非必填
     * @return Set<String> 返回一个 LinkedHashSet 集合，包含构造的消息描述
     */
    @NotNull
    private Set<String> getMsgDescLinkHashSet(EkbReceiveEkbEventRequest request, String title, String amountDec, String legalPerson) {
        Set<String> msgDescLinkHashSet = new LinkedHashSet<>();
        //1.设置标题
        msgDescLinkHashSet.add(StringUtil.concat(true, "标题：", StringPool.AT, title));

        /**
         *
         * 费用明细详情下标排序位置，标题固定排在第一位
         */
        int feeDetailDescIndex = 2;

        if (StringUtil.isNotBlank(legalPerson)) {
            legalPerson = StringUtil.concat(true, "法人实体：", StringPool.AT, legalPerson);
            msgDescLinkHashSet.add(legalPerson);
            feeDetailDescIndex = 3;
        }

        //费用明细
        String feeDetailDesc = getFeeDetailDesc(request, feeDetailDescIndex, false);
        //有顺序要求
        if (StringUtil.isNotBlank(feeDetailDesc)) {
            msgDescLinkHashSet.add(feeDetailDesc);
        }
        if (StringUtil.isNotBlank(amountDec)) {
            amountDec = StringUtil.concat(true, "合计金额：", StringPool.AT, amountDec);
            msgDescLinkHashSet.add(amountDec);
        }
        return msgDescLinkHashSet;
    }

    /**
     * 根据请求和消息描述映射生成消息描述
     * 如果生成的消息描述为空，则使用请求中的动作名称作为默认描述
     *
     * @param request     Ekb接收事件请求对象，包含事件相关的信息
     * @param msgDescMap  消息描述映射，键为描述类型，值为描述内容
     * @return            生成的消息描述字符串
     */
    private String getMsgDesc(EkbReceiveEkbEventRequest request, Map<String, String> msgDescMap) {
        String msgDesc = msgDescMap.entrySet().stream()
                .map(entry -> entry.getKey() + entry.getValue())
                .collect(Collectors.joining(" "));
        if (StringUtil.isBlank(msgDesc)) {
            msgDesc = request.getActionName();
        }
        return msgDesc;
    }


    /**
     * 根据请求对象计算并获取总金额的描述
     * 该方法主要用于计算请求对象中所有费用详情的总金额，并以字符串形式返回
     * 如果请求对象中包含有效的费用详情列表，该方法会遍历每个费用详情，累加其金额，
     * 并与金额单位一起拼接成描述字符串返回
     *
     * @param request EkbReceiveEkbEventRequest对象，包含费用详情列表和其他相关信息
     * @return 返回总金额的描述字符串，格式为"总金额+金额单位"
     */
    private String getAmountDesc(EkbReceiveEkbEventRequest request) {
        String amountDec = "";
        BigDecimal sumAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(request.getDetails())) {
            for(EkbReceiveEkbEventRequest.FeeDetail feeDetail : request.getDetails()) {
                sumAmount = NumberUtil.add(sumAmount, NumberUtil.toBigDecimal(feeDetail.getFeeTypeForm().getAmount().getStandard()));
            }
            amountDec = StringUtil.concat(true, sumAmount.toPlainString(), request.getDetails().get(0).getFeeTypeForm().getAmount().getStandardUnit());
        }
        return amountDec;
    }

    /**
     * 向提交者发送通知消息
     *
     * @param request Ekb接收Ekb事件请求对象，包含提交者信息和流程ID
     * @param eventCommandDTO Ekb事件命令DTO，包含与钉钉消息相关的ID
     * @param msgTitle 消息标题
     * @param iconImgId 消息图标图片ID
     * @param msgDesc 消息描述
     * @param submitterUser 提交者用户信息
     */
    private void sendNotifyMessage4Submmiter(EkbReceiveEkbEventRequest request, EkbEventCommandDTO eventCommandDTO, String msgTitle, String iconImgId, String msgDesc, DingUser submitterUser) {
        //发送钉钉通知消息
        DingSendNotifyMsgDTO notifyMsgDTO = DingSendNotifyMsgDTO.builder().build()
                .setTitle(msgTitle)
                .setUserId(submitterUser.getUserId())
                .setCompanyId(submitterUser.getCompanyId())
                .setEkbMessageId(eventCommandDTO.getEkbDingTodoMessage().getEkbMessageId())
                .setEkbUserId(request.getSubmitterId().getId())
                .setEkbFlowId(request.getFlowId())
                .setText(msgDesc)
                .setIconImgId(iconImgId);
        //更新结果
        boolean sendMsgDingResult = false;
        try {
            sendMsgDingResult = getDingMsgService().sendNotifyMsg(notifyMsgDTO);
            if (!sendMsgDingResult) {
                log.info("###### send notice submitterUser failure ,unionId = {}", submitterUser.getUnionId());
            }
        } catch (Exception ex) {
            log.info("###### ekb {} notice submitterUser failure ,msg = {}", eventCommandDTO.getEkbMessage().getMsgAction(), ex.getMessage());
        }
    }


    /**
     * 筛选需要处理的Ekb钉钉待办消息
     *
     * 本方法的目的是从当前待审批的待办消息中筛选出需要处理的Ekb任务这些任务满足以下条件之一：
     * 1. 备注为空，表示任务尚未被处理或标记
     * 2. 任务处理结果为空，或者任务状态为“开发中”或“尚未提交”，表示任务正在进行中或未开始
     *
     * @param currentApprovingTodoMsgs 当前待审批的待办消息列表
     * @param necessaryDealEkbTasks 需要处理的Ekb任务列表，通过本方法筛选后填充
     */
    private void filterMessages(List<EkbDingTodoMessage> currentApprovingTodoMsgs, List<EkbDingTodoMessage> necessaryDealEkbTasks) {
        for (EkbDingTodoMessage flow : currentApprovingTodoMsgs) {
            if (StringUtil.isBlank(flow.getRemark())) {
                necessaryDealEkbTasks.add(flow);
            } else {
                EkbDingTodoResultDTO todoResultDTO = FaJsonUtil.toObject(flow.getRemark(), EkbDingTodoResultDTO.class);
                //先筛选出状态：开发中或尚未提交的任务
                if (StringUtil.isBlank(todoResultDTO.getTaskProcessedResult()) || (StringUtil.equalsAny(todoResultDTO.getTaskProcessedResult(), EkbFlowStageNameEnums.DEV.getDbCode(), EkbFlowStageNameEnums.NO_SUBMIT.getDbCode()))) {
                    necessaryDealEkbTasks.add(flow);
                }
            }
        }
    }
}
