package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.oa.common.annotation.Excel;
import com.qmqb.oa.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 员工主体对象 t_employee_subject
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_employee_subject")
public class EmployeeSubject extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 档案编号
     */
    @Excel(name = "档案编号")
    private String jobNumber;

    /**
     * 归属公司
     */
    @Excel(name = "归属公司")
    private String rawCompany;

    /**
     * 调整后归属公司
     */
    @Excel(name = "调整后归属公司", dictType = "finance_tenant_id")
    private Integer company;

    /**
     * 上班地点
     */
    @Excel(name = "上班地点")
    private String workPlace;

    /**
     * 中心
     */
    @Excel(name = "中心")
    private String center;

    /**
     * 部门
     */
    @Excel(name = "部门")
    private String dept;

    /**
     * 组
     */
    @Excel(name = "组")
    private String group;

    /**
     * 小组
     */
    @Excel(name = "小组")
    private String lowGroup;

    /**
     * 职位名称
     */
    @Excel(name = "职位名称")
    private String title;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
//    @Excel(name = "逻辑删除: 0-未删除, 1-删除")
    private Integer isDeleted;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
//    @Excel(name = "主体", dictType = "finance_tenant_id")
    private Integer tenantId;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setRawCompany(String rawCompany) {
        this.rawCompany = rawCompany;
    }

    public String getRawCompany() {
        return rawCompany;
    }

    public void setCompany(Integer company) {
        this.company = company;
    }

    public Integer getCompany() {
        return company;
    }

    public void setWorkPlace(String workPlace) {
        this.workPlace = workPlace;
    }

    public String getWorkPlace() {
        return workPlace;
    }

    public void setCenter(String center) {
        this.center = center;
    }

    public String getCenter() {
        return center;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getDept() {
        return dept;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getGroup() {
        return group;
    }

    public void setLowGroup(String lowGroup) {
        this.lowGroup = lowGroup;
    }

    public String getLowGroup() {
        return lowGroup;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("jobNumber", getJobNumber())
                .append("rawCompany", getRawCompany())
                .append("company", getCompany())
                .append("workPlace", getWorkPlace())
                .append("center", getCenter())
                .append("dept", getDept())
                .append("group", getGroup())
                .append("lowGroup", getLowGroup())
                .append("title", getTitle())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("isDeleted", getIsDeleted())
                .append("tenantId", getTenantId())
                .toString();
    }
}
