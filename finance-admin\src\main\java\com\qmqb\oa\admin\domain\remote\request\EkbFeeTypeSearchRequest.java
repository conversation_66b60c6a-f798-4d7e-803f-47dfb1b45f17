package com.qmqb.oa.admin.domain.remote.request;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Description ekb 费用类型查询实体
 * @Date 2024\11\6 0006 15:51
 * @Version 1.0
 */

@Data
@Accessors(chain = true)
@Builder
public class EkbFeeTypeSearchRequest {
    /**
     * 费用类型ID 和 codes 至少有一个必填，可以传入多个 ID
     */
    private List<String> ids;
    /**
     * 费用类型编码CODE  和 ids 至少有一个必填，可以传入多个 CODE
     */
    private List<String> codes;

}
