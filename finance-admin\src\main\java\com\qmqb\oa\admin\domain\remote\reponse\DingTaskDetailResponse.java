package com.qmqb.oa.admin.domain.remote.reponse;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description 钉钉待办任务详情
 * @Date 2024\10\25 0025 14:26
 * @Version 1.0
 */

@Data
public class DingTaskDetailResponse {
    private String nextToken;

    private TodoCard[] todoCards;


    @Data
    public static class TodoCard {
        private String taskId;
        private String subject;
        private long dueTime;
        private DetailUrl detailUrl;
        private int priority;
        private long createdTime;
        private long modifiedTime;
        private String creatorId;
        private String sourceId;
        private String bizTag;
        private boolean isDone;

    }

    @Data
    public static class DetailUrl {
        private String appUrl;
        private String pcUrl;
    }
}
