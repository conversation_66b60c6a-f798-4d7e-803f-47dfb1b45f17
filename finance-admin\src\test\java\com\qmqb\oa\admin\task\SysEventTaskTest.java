package com.qmqb.oa.admin.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.hzed.structure.common.util.ObjectUtil;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.domain.dto.DingDeleteTodoMsgDTO;
import com.qmqb.oa.admin.domain.dto.DingSendNotifyMsgDTO;
import com.qmqb.oa.admin.domain.dto.DingSubmitTodoMsgDTO;
import com.qmqb.oa.admin.domain.dto.DingUpdateTodoMsgDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbProvisionalAuthRequest;
import com.qmqb.oa.admin.service.hsfk.*;
import com.qmqb.oa.common.enums.SysEventEnum;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.DingUserService;
import com.qmqb.oa.system.service.EkbDingTodoMessageService;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SysEventTaskTest extends BaseTest {

    @Autowired
    SysEventTask task;
    @Autowired
    EkbSysEventHandler ekbSysEventHandler;
    @Autowired
    private SysEventService sysEventService;
    @Autowired
    private SysEventTask sysEventTask;
    @Autowired
    private EkbDingTodoMessageService ekbDingTodoMessageService;
    @Autowired
    private DingMsgService dingMsgService;
    @Autowired
    private HeSiFeiKongService heSiFeiKongService;

    @Autowired
    private DingUserService dingUserService;
    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Autowired
    private H5AppDingClientService h5AppDingClientService;
    @Autowired
    private DingAppConfService dingAppConfService;
    private final static int QMQB_COMPANY = 0;
    private final static int OA_COMPANY = 4;

    @Test
    public void execute() {
        SysEvent sysEvent = sysEventService.getById(24L);
//        ekbSysEventHandler.dealEvent4BatchUpdateUserList(sysEvent);
        sysEventTask.pullDingCallBackFailedResult();

    }

    @Test
    public void testDingSubmitTodoMsg() throws Exception{
        Map<String, String> msgDesc = new HashMap<>(2);
        msgDesc.put("1、法人实体", "广州市全民钱包科技有限公司");
        msgDesc.put("2、合计金额", "33.8元");
        DingSubmitTodoMsgDTO dto = DingSubmitTodoMsgDTO.builder()
                .messageId("test_08")
                .companyId(0)
                .sourceId("a8541aedc36ca29ea1c1e210bb0a1114")
                //生产: 林咏锶: AuwZhyiPuCWiiAAXVuK973GwiEiE 马代军: HJ9NnlSEUmIiE
                //咏锶 userId: 190168296826194014 unionId: 0r70koTh4QFkTBEDHsbRSgiEiE 映迁： D5I1K5iS6JhgiE 代军： 6liSpVM7kiS3YiE
                .recipientUnionId("AuwZhyiPuCWiiAAXVuK973GwiEiE")
                .submitUnionId("AuwZhyiPuCWiiAAXVuK973GwiEiE")
                .content("待办12-03001")
                .ekbFlowId("ID01EkB5yPqDi7")
                //生产： 林咏锶： ID01zNdgxCRa06:ID01BwCSPXQ459 代军：ID01zNdgxCRa06:ID01BwAynOhvSD
                //咏锶: ID01zNdgxCRa06:ID01BwCSPXQ459 代军 ID01zNdgxCRa06:ID01BwAynOhvSD 映迁: ID01zNdgxCRa06:ID01BwAynOgfin
                .ekbUserId("ID01zNdgxCRa06:ID01BwAynOhvSD")
                .desc(msgDesc)
                .build();

        String taskId = dingMsgService.sendTodoMsg(dto);
        System.out.println("taskId" + taskId);
    }


    @Test
    public void testDingUpdateTodoMsg() throws Exception{
        /**
         * {"companyId":5,"content":"已处理","messageId":"ID01DC5sqr8Mur","recipientUnionId":"0r70koTh4QFkTBEDHsbRSgiEiE","taskId":"task17435deb42040c7da4dc9108dbd0bd25"}
         */
        DingUpdateTodoMsgDTO dto = DingUpdateTodoMsgDTO.builder()
                .messageId("test_06")
                .companyId(5)
                .taskId("task4bc5b14439da9eb3a3695b520225fa4f")
                //.recipientUnionIds(Arrays.asList("0r70koTh4QFkTBEDHsbRSgiEiE"))
//                .recipientUnionId("0r70koTh4QFkTBEDHsbRSgiEiE")
                .content("更新待办测试")
                .unionId("0r70koTh4QFkTBEDHsbRSgiEiE")
                .isDone(true)
                .build();

        dingMsgService.updateTodoMsg(dto);
    }

    @Test
    public void testDingDeleteTodoMsg() throws Exception{
        DingDeleteTodoMsgDTO dto = DingDeleteTodoMsgDTO.builder()
                .messageId("test_05")
                .companyId(5)
                .taskId("task864e04dbeaf8d811fd253619407c5081")
                .recipientUnionId("m2Z2mFBiSI6UiE")
                .build();

        dingMsgService.deleteTodoMsg(dto);
    }


    @Test
    public void testDingSendNotifyMsg() throws Exception{
        DingSendNotifyMsgDTO dto = DingSendNotifyMsgDTO.builder()
                .ekbFlowId("ID01DIojbvW66P")
                .ekbUserId("ID01zNdgxCRa06:ID01BwAynOgfin")
                .ekbMessageId("111")
                .companyId(5)
                //测试主体 映迁
                .userId("manager400")
//                .userId("0368432424249537")
//                //代军
                .userId("1905526038637188")
//                .companyId(0)
//                .userId("ID01zNdgxCRa06:ID01BwAynOgfin")
                //全民主体 超杰 映迁
                .companyId(0)
//                .userId("146401060839139831")
                .userId("0368432424249537")
                .title("通知消息测试44")
                .text("内容444 描述")
                .msgUrl("https://app.ekuaibao.com/applet/thirdparty.html?accessToken=AMhDinFf7F43znp-c3gJ_0&ekbCorpId=ID01zNdgxCRa06&pageType=form&overdueTokenRedirect=&noPathCache=true&flowId=ID01DKrBgzngbd&backlogId=")
                .build();

        dingMsgService.sendNotifyMsg(dto);
    }

    @Test
    public void testEkbUrl() throws Exception{
        EkbProvisionalAuthRequest request = new EkbProvisionalAuthRequest();
        request.setCorpId("dingdc0a16e71772ca06bc961a6cb783455b");
        request.setEkbUserId("ID01zNdgxCRa06:ID01BwCSPXQ459");
        request.setClient(2);
        request.setFlowId("ID01DIojbvW66P");
        String url = heSiFeiKongService.getFormProvisionalauthurl(request);
        log.info("url: {}",url);

    }

    @Test
    public void testSysEvent(){
        String s = "{\"submitDate\":null,\"userInfo\":{\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSpFJ\",\"name\":\"邓树坚\",\"cellphone\":\"19849481848\",\"email\":\"\"},\"formSpecification\":{\"specificationId\":\"ID01B4mXUFS6LB:33ae8704ba89a4c230a21928d5c503ea37693842\",\"specificationName\":\"306-1.2渠道采量大额申请单\"},\"法人实体\":{\"id\":\"ID01zPEcMjsEs7\",\"code\":\"101\",\"name\":\"广州市全民钱包科技有限公司\",\"path\":\"广州市全民钱包科技有限公司\"},\"corporationId\":\"ID01zNdgxCRa06\",\"submitterId\":{\"name\":\"阳燕\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSCsv\"},\"action\":\"backlog.processed\",\"messageId\":\"ID01ExNmaN08uH\",\"state\":\"approving\",\"flowId\":\"ID01ExNChdrzHh\",\"nodeId\":\"FLOW:1635677479:1136039992\",\"currentApprovers\":[{\"name\":\"邓树坚\",\"id\":\"ID01zNdgxCRa06:ID01zZzVbYSpFJ\"}],\"preNodeApprovedTime\":1732241386967,\"actionName\":\"已处理\",\"details\":[{\"feeTypeId\":\"ID01zRklccHq8v\",\"feeTypeForm\":{\"amount\":{\"standard\":\"2000000\",\"standardUnit\":\"元\",\"standardScale\":2,\"standardSymbol\":\"¥\",\"standardNumCode\":\"156\",\"standardStrCode\":\"CNY\"},\"detailId\":\"ID01ExNChdrALl\",\"detailNo\":1,\"invoiceForm\":{\"type\":\"wait\",\"invoices\":null},\"u_合同台账\":\"ID01BhQe8atgjt\"},\"specificationId\":\"ID01zRklccHq8v:requisition:2c5f94a4a2d6d9b9251398b3e712e5bb901ee03e\"}]}";

        String json = JacksonUtil.toJson(s);
        long eventTime = DateTimeUtil.getUnixTimestamp(LocalDateTime.now());
        sysEventService.saveSysEvent(0,  SysEventEnum.EKB_EVENT_NOTICE.getEventSource(),
                SysEventEnum.EKB_EVENT_NOTICE.getEventType(), JacksonUtil.toJson(s), eventTime, "ID01ExNmaN08uH", null);
    }


}
