package com.qmqb.oa.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 流程类型：1-加班餐费/车费报销；2-团建费报销；3-付款申请；4-开票申请；5-入职审批
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Getter
@AllArgsConstructor
public enum ProcessType {
    /**
     * 加班餐费/车费报销
     */
    MEAL(1, "finance", "加班餐费/车费报销"),
    /**
     * 团建费报销
     */
    TB(2, "finance", "团建费报销"),
    /**
     * 付款申请
     */
    PAY_APPLY(3, "finance", "付款申请"),
    /**
     * 开票申请
     */
    BILLING_APPLY(4, "finance", "开票申请"),
    /**
     * 入职审批
     */
    ENTRY_APPROVAL(5, "personal", "入职审批"),
    ;
    /**
     * 值
     */
    private final Integer value;
    /**
     * 类别：管理部；运营/产品相关申请；行政；出勤休假；财务；人事；技术-研发类；技术-支持类；电商分期项目；其他
     */
    private final String type;
    private final String name;

    public static ProcessType indexOf(Integer value) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.value, value)).findFirst().orElse(null);
    }

    public static List<Integer> getValuesByTypes(List<String> types) {
        List<Integer> values = new ArrayList<>();
        for (String type : types) {
            for (ProcessType value : values()) {
                if (StrUtil.startWith(type, value.type)) {
                    values.add(value.value);
                }
            }
        }
        return values;
    }
}
