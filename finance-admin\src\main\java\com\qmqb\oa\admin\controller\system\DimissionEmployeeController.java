//package com.qmqb.oa.admin.controller.system;
//
//import java.util.List;
//
//import cn.hutool.core.date.DateUtil;
//import com.qmqb.oa.system.service.DimissionEmployeeService;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import com.qmqb.oa.common.annotation.Log;
//import com.qmqb.oa.common.core.controller.BaseController;
//import com.qmqb.oa.common.core.domain.AjaxResult;
//import com.qmqb.oa.common.enums.BusinessType;
//import com.qmqb.oa.system.domain.DimissionEmployee;
//import com.qmqb.oa.common.utils.poi.ExcelUtil;
//import com.qmqb.oa.common.core.page.TableDataInfo;
//
///**
// * 离职员工Controller
// *
// * <AUTHOR>
// * @date 2023-11-01
// */
//@RestController
//@RequestMapping("/system/employee")
//public class DimissionEmployeeController extends BaseController {
//    @Autowired
//    private DimissionEmployeeService dimissionEmployeeService;
//
///**
// * 查询离职员工列表
// */
//@GetMapping("/list")
//        public TableDataInfo list(DimissionEmployee dimissionEmployee) {
//        startPage();
//        List<DimissionEmployee> list = dimissionEmployeeService.selectDimissionEmployeeList(dimissionEmployee);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出离职员工列表
//     */
//    //@PreAuthorize("@ss.hasPermi('system:employee:export')")
//    @Log(title = "离职员工", businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//    public AjaxResult export(DimissionEmployee dimissionEmployee) {
//        List<DimissionEmployee> list = dimissionEmployeeService.selectDimissionEmployeeList(dimissionEmployee);
//        ExcelUtil<DimissionEmployee> util = new ExcelUtil<DimissionEmployee>(DimissionEmployee. class);
//        return util.exportExcel(list, "employee");
//    }
//
//    /**
//     * 获取离职员工详细信息
//     */
//    //@PreAuthorize("@ss.hasPermi('system:employee:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return AjaxResult.success(dimissionEmployeeService.selectDimissionEmployeeById(id));
//    }
//
//    /**
//     * 新增离职员工
//     */
//    //@PreAuthorize("@ss.hasPermi('system:employee:add')")
//    @Log(title = "离职员工", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DimissionEmployee dimissionEmployee) {
//        return toAjax(dimissionEmployeeService.insertDimissionEmployee(dimissionEmployee));
//    }
//
//    /**
//     * 修改离职员工
//     */
//    //@PreAuthorize("@ss.hasPermi('system:employee:edit')")
//    @Log(title = "离职员工", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody DimissionEmployee dimissionEmployee) {
//        return toAjax(dimissionEmployeeService.updateDimissionEmployee(dimissionEmployee));
//    }
//
//    /**
//     * 删除离职员工
//     */
//    //@PreAuthorize("@ss.hasPermi('system:employee:remove')")
//    @Log(title = "离职员工", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(dimissionEmployeeService.deleteDimissionEmployeeByIds(ids));
//    }
//}
