package com.qmqb.oa.admin.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 发票OCR识别
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
public class Invoice {

    /**
     * 发票代码
     */
    @JSONField(name = "发票代码")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @JSONField(name = "发票号码")
    private String invoiceNumber;

    /**
     * 开票日期
     */
    @JSONField(name = "开票日期")
    private String invoiceDate;

    /**
     * 校验码
     */
    @JSONField(name = "校验码")
    private String checkCode;

    /**
     * 发票金额
     */
    @JSONField(name = "发票金额")
    private BigDecimal invoiceAmount;

    /**
     * 大写金额
     */
    @JSONField(name = "大写金额")
    private String capitalAmount;

    /**
     * 发票税额
     */
    @JSONField(name = "发票税额")
    private BigDecimal invoiceTax;

    /**
     * 不含税金额
     */
    @JSONField(name = "不含税金额")
    private BigDecimal excludeTaxAmount;

    /**
     * 受票方名称
     */
    @JSONField(name = "受票方名称")
    private String buyerName;

    /**
     * 受票方税号
     */
    @JSONField(name = "受票方税号")
    private String buyerTaxNumber;

    /**
     * 受票方地址、电话
     */
    @JSONField(name = "受票方地址、电话")
    private String buyerAddrPhone;

    /**
     * 受票方开户行、账号
     */
    @JSONField(name = "受票方开户行、账号")
    private String buyerBankAccount;

    /**
     * 销售方名称
     */
    @JSONField(name = "销售方名称")
    private String sellName;

    /**
     * 销售方税号
     */
    @JSONField(name = "销售方税号")
    private String sellTaxNumber;

    /**
     * 销售方地址、电话
     */
    @JSONField(name = "销售方地址、电话")
    private String sellAddrPhone;

    /**
     * 销售方开户行、账号
     */
    @JSONField(name = "销售方开户行、账号")
    private String sellBankAccount;

    /**
     * 联次
     */
    @JSONField(name = "联次")
    private String sheet;

    /**
     * 发票类型
     */
    @JSONField(name = "发票类型")
    private String invoiceType;

    /**
     * 发票详单
     */
    @JSONField(name = "发票详单")
    private String invoiceDetail;

    /**
     * 发票代码解析
     */
    @JSONField(name = "发票代码解析")
    private String invoiceCodeParse;

    /**
     * 发票图片地址
     */
    @JSONField(serialize = false, deserialize = false)
    private String imageUrl;

    /**
     * 发票名称
     */
    @JSONField(serialize = false, deserialize = false)
    private String filename;

    /**
     * 发票名称
     */
    @JSONField(serialize = false, deserialize = false)
    private Date parseInvoiceDate;


}
