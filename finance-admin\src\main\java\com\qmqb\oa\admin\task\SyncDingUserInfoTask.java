package com.qmqb.oa.admin.task;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiUserListidRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiUserListidResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.collect.Lists;
import com.qmqb.oa.admin.service.hsfk.DingTalkUserService;
import com.qmqb.oa.admin.service.hsfk.H5AppDingClientService;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.DingDept;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.DingDeptService;
import com.qmqb.oa.system.service.DingUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import shade.com.alibaba.fastjson2.JSONObject;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/10
 */
@Slf4j
@Component("syncDingUserInfoTask")
@RequiredArgsConstructor
public class SyncDingUserInfoTask {
    @Resource
    private DingUserService dingUserService;
    @Resource
    private DingTalkUserService dingTalkUserService;
    @Resource
    private H5AppDingClientService h5AppDingClientService;
    @Resource
    private DingAppConfService dingAppConfService;
    @Resource
    private DingDeptService dingDeptService;

    private final static int QMQB_COMPANY = 0;
    private final static int OA_COMPANY = 4;

    /**
     * 同步用户在公司主体的unionId
     *
     * @throws Exception
     */
    public void syncUnionId() throws Exception {
        //查询主体全民钱包、OA公司下待处理的用户数据
        LambdaQueryWrapper<DingUser> userWrapper = Wrappers.lambdaQuery(DingUser.class)
                .in(DingUser::getCompanyId, QMQB_COMPANY, OA_COMPANY)
                .isNull(DingUser::getUnionId)
                .orderByDesc(DingUser::getCompanyId);

        List<DingUser> userList = dingUserService.list(userWrapper);
        //全民钱包的token
        DingAppConf qmConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, QMQB_COMPANY));
        String qmToken = h5AppDingClientService.getAccessToken(qmConf.getDingAppKey(), qmConf.getDingAppSecret());
        //OA的token
        DingAppConf oaConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, OA_COMPANY));
        String oaToken = h5AppDingClientService.getAccessToken(oaConf.getDingAppKey(), oaConf.getDingAppSecret());

        userList.forEach(s -> {
                    String token = qmToken;
                    if (s.getCompanyId() == OA_COMPANY) {
                        token = oaToken;
                    }
                    try {
                        OapiV2UserGetResponse.UserGetResponse userDetail = dingTalkUserService.getUserDetail(s.getUserId(), token);
                        DingUser update = new DingUser();
                        update.setId(s.getId());
                        update.setUnionId(userDetail.getUnionid());
                        update.setUpdateTime(LocalDateTime.now());
                        dingUserService.updateById(update);
                    } catch (Exception e) {
                        log.error("主体{}，userId:{}，unionId处理异常", s.getCompanyId(), s.getUserId());
                    }

                }
        );
    }

    /**
     * 以钉钉通讯录为基础
     * 按部门逐级同步钉钉的用户到本地，本地没有的用户新增，有改变的修改
     * 最后用户同步到易快报
     *
     * @throws Exception
     */
    public void syncDingUser(Integer companyIdParam, Integer maxLevel) throws Exception {
        log.info("同步钉钉的用户定时任务开启，请求参数:{},{}", companyIdParam, maxLevel);
        LambdaQueryWrapper<DingAppConf> confWrapper = Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getAppSwitch, 1);
        if (ObjectUtil.isNotNull(companyIdParam)) {
            //把字符串companyIdList转成int集合
            confWrapper.eq(DingAppConf::getCompanyId, companyIdParam);
        }

        List<DingAppConf> appConfList = dingAppConfService.list(confWrapper);
        for (DingAppConf dingAppConf : appConfList) {
            log.info("同步钉钉的用户,主体:{}", dingAppConf.getCompanyName());
            Integer companyId = dingAppConf.getCompanyId();
            //查询当前主体下的所有子部门
            String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
            //查询当前主体下的最大部门层级
            maxLevel = maxLevel == null ? 6 : maxLevel;
            for (int level = 2; level <= maxLevel; level++) {

                log.info("同步钉钉的用户,同步主体:{}，部门层级:{}", dingAppConf.getCompanyName(), level);
                //找到同一级的部门
                List<DingDept> deptList = dingDeptService.list(Wrappers.lambdaQuery(DingDept.class).eq(DingDept::getCompanyId, companyId).eq(DingDept::getDeptLevel, level));
                //处理同一级部门的用户
                deptList.forEach(dept -> {
                    try {
                        log.info("同步钉钉的用户,同步主体:{}，部门:{}", dingAppConf.getCompanyName(), dept.getDeptChainName());
                        //通过钉钉接口查询当前部门下的用户
                        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/listid");
                        OapiUserListidRequest req = new OapiUserListidRequest();
                        req.setDeptId(dept.getDeptId());
                        OapiUserListidResponse rsp = client.execute(req, accessToken);
                        List<String> addUserIdList = new ArrayList<>();
                        List<String> modifyUserIdList = new ArrayList<>();
                        List<DingUser> modifyUserList = new ArrayList<>();
                        if (rsp.isSuccess() && !rsp.getResult().getUseridList().isEmpty()) {
                            //对比本地数据库，不存在则新增
                            for (String u : rsp.getResult().getUseridList()) {
                                try {
                                    DingUser userData = dingUserService.getOne(Wrappers.lambdaQuery(DingUser.class)
                                            .eq(DingUser::getUserId, u)
                                            .eq(DingUser::getCompanyId, companyId));
                                    log.info("同步钉钉的用户，主体:{}，部门:{}，钉钉用户:{}", dingAppConf.getCompanyName(), dept.getDeptChainName(), u);
                                    if (userData == null) {
                                        addUserIdList.add(u);
                                    } else {
                                        //存在时，需要对比数据库的用户的部门，如果不一样，需要更新
                                        //"dept_id_list":"[2,3,4]",
                                        OapiV2UserGetResponse.UserGetResponse userDetail = dingTalkUserService.getUserDetail(u, accessToken);
                                        if (ObjectUtil.isNull(userDetail)) {
                                            continue;
                                        }
                                        boolean match = userDetail.getDeptIdList().stream().anyMatch(deptId -> !userData.getDeptIdList().contains(deptId + ""));
                                        if (match) {
                                            modifyUserIdList.add(u);
                                            modifyUserList.add(userData);
                                        }

                                    }
                                } catch (Exception e) {
                                    log.error("主体{}，userId:{}，同步钉钉用户处理异常", companyId, u);
                                }
                            }

                        }
                        if (!addUserIdList.isEmpty()) {
                            JSONObject bizData = new JSONObject();
                            bizData.put("userId", addUserIdList);
                            dingTalkUserService.addUser(bizData, companyId);
                            log.info("部门:{},新增用户的列表:{}", dept.getDeptChainName(), JSON.toJSONString(addUserIdList));
                        }
                        if (!modifyUserIdList.isEmpty()) {
                            JSONObject bizData = new JSONObject();
                            bizData.put("userId", modifyUserIdList);
                            dingTalkUserService.modifyUser(bizData, companyId);
                            log.info("部门:{},修改用户的列表:{}", dept.getDeptChainName(), JSON.toJSONString(modifyUserList));
                        }

                    } catch (Exception e) {
                        log.error("所属部门：{}，同步用户失败", dept.getDeptChainName(), e);
                    }

                });
            }
        }
    }

    /**
     * 以本地用户为基础
     * 本地已离职的用户同步到易快报
     *
     * @throws Exception
     */
    public void syncLeaveUsersToEkb() throws Exception {
        List<DingAppConf> appConfList = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getAppSwitch, 1));
        for (DingAppConf dingAppConf : appConfList) {
            Integer companyId = dingAppConf.getCompanyId();
            String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
            List<String> userIdLeaveList = Lists.newArrayList();
            List<DingUser> userLeaveList = Lists.newArrayList();
            List<DingUser> list = dingUserService.list(Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getCompanyId, companyId));
            for (DingUser dingUser : list) {
                String userId = dingUser.getUserId();
                try {
                    DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
                    OapiV2UserGetRequest req = new OapiV2UserGetRequest();
                    req.setUserid(userId);
                    OapiV2UserGetResponse resp = client.execute(req, accessToken);
                    if (!resp.isSuccess() && resp.getErrmsg().contains("找不到该用户")) {
                        log.error("获取用户详情失败，用户id：{},属于新增用户", userId);
                        userIdLeaveList.add(dingUser.getUserId());
                        userLeaveList.add(dingUser);
                    }
                } catch (Exception e) {
                    log.error("userId{},同步离职用户时异常", userId, e);
                }

            }
            if (!userIdLeaveList.isEmpty()) {
                JSONObject bizData = new JSONObject();
                bizData.put("userId", userIdLeaveList);
                log.info("离职的人员列表:{}", JSON.toJSONString(userLeaveList));
                dingTalkUserService.deleteUser(bizData, companyId);
            }

        }

    }

    public static void main(String[] args) {
        String s = "1";
        List<Integer> collect = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
        System.out.println(JSON.toJSONString(collect));

    }

}
