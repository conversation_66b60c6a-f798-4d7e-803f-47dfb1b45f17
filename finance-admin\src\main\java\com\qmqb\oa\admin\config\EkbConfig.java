package com.qmqb.oa.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * 易快报配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ekb")
public class EkbConfig {


        /**
         * 域名
         */
        private String domain;
        /**
         * 获取授权码接口url
         */
        private String accessTokenUrl;
        /**
         * 访问临时授权接口url
         */
        private String provisionalAuthUrl;

        /**
         * 批量新增部门url
         */
        private String batchAddDeptUrl;

        /**
         * 批量修改员工url
         */
        private String batchUpdateUserUrl;

        /**
         * 修改部门信息接口url
         */
        private String updateDepartmentUrl;
        /**
         * 停启用部门接口url
         */
        private String disableOrEnableDepartmentUrl;
        
        /**
         * 获取部门列表(包含停用部门)
         */
        private String departmentsUrl;

        /**
         * 批量新增员工url
         */
        private String batchAddUserUrl;
        /**
         * 更新角色下员工信息
         */
        private String updateRoleUserInfoUrl;

        /**
         * 员工列表
         */
        private String staffListUrl;

        /**
         * 停启用员工接口url
         */
        private String disableOrEnableStaffUrl;

        /**
         * ekb 审批状态接口url
         */
        private String approveStatesUrl;

        /**
         * ekb 审批状态接口url
         */
        private String ekbFlowDetailUrl;

        /**
         * 消息通知类型图片
         */
        private String ekbMsgNoticeImgUrl;

        /**
         * 根据ids获取ekb 票据类型url
         */
        private String ekbGetFeeTypeByIdUrl;

}
