CREATE TABLE `t_sys_event` (
                             `id` BIGINT(21) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                             `request_no` VARCHAR(32) NOT NULL COMMENT '流水号',
                             `event_code` VARCHAR(50) NOT NULL COMMENT '事件编码',
                             `req_data` VARCHAR(1000) NOT NULL COMMENT '请求报文',
                             `status` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '状态：0-处理中；1-处理成功；2-处理失败',
                             `fail_count` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '失败次数',
                             `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `uk_request_no` (`request_no`),
                             KEY `idx_create_time` (`create_time`)
) COMMENT='系统事件表';

CREATE TABLE `t_ding_app_conf` (
                                 `id` int(4) NOT NULL COMMENT '主键id',
                                 `ding_h5_app_name` varchar(20) DEFAULT NULL COMMENT 'H5微应用名称',
                                 `company_name` varchar(50) DEFAULT NULL COMMENT '公司名称',
                                 `company_id` tinyint(1) NOT NULL COMMENT '公司主键id: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台',
                                 `ding_corp_id` varchar(50) NOT NULL COMMENT '公司corpId',
                                 `ding_app_key` varchar(50) NOT NULL COMMENT '钉钉H5微应用appKey',
                                 `ding_app_secret` varchar(100) NOT NULL COMMENT '钉钉H5微应用appSecret',
                                 `partner_type` int(4) NOT NULL COMMENT '合作方类型:1-易快报',
                                 `partner_app_key` varchar(100) NOT NULL COMMENT '合作方appKey',
                                 `partner_app_secret` varchar(100) NOT NULL COMMENT '合作方appSecret',
                                 `remark` varchar(200) DEFAULT NULL COMMENT '备注',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 PRIMARY KEY (`id`)
) COMMENT='钉钉app应用配置表';

CREATE TABLE `t_ding_company` (
                                `id` tinyint(1) NOT NULL COMMENT '租户Id',
                                `company_name` varchar(20) NOT NULL COMMENT '公司主体名称',
                                `corp_id` varchar(50) NOT NULL COMMENT '钉钉主体的corpId',
                                PRIMARY KEY (`id`)
) COMMENT='钉钉主体公司表';

CREATE TABLE `t_ding_dept` (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
                             `parent_id` bigint(20) DEFAULT NULL COMMENT '父部门ID',
                             `dept_name` varchar(30) DEFAULT NULL COMMENT '部门名称',
                             `dept_level` int(2) DEFAULT NULL COMMENT '级别',
                             `dept_code` varchar(100) DEFAULT NULL COMMENT '部门编码：全民主体QM-,OA主体OA-',
                             `dept_chain_name` varchar(200) DEFAULT NULL COMMENT '部门及上级部门完整链路名称',
                             `company_id` tinyint(1) NOT NULL COMMENT '主体公司：0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台',
                             `remark` varchar(256) DEFAULT NULL COMMENT '备注',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                             PRIMARY KEY (`id`),
                             KEY `idx_dept_id` (`dept_id`)
) COMMENT='钉钉部门表';

CREATE TABLE `t_ding_user` (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `user_id` varchar(64) NOT NULL COMMENT '用户的userId',
                             `user_name` varchar(64) NOT NULL COMMENT '名字',
                             `mobile` varchar(16) NOT NULL COMMENT '手机号码',
                             `dept_id_list` varchar(64) NOT NULL COMMENT '所属部门id列表',
                             `company_id` tinyint(1) NOT NULL COMMENT '主体公司: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台',
                             `remark` varchar(256) DEFAULT NULL COMMENT '备注',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                             PRIMARY KEY (`id`),
                             KEY `idx_user_id` (`user_id`),
                             KEY `idx_mobile` (`mobile`)
) COMMENT='钉钉员工表';

CREATE TABLE `t_ekb_ding_dept_mapping` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `dept_code` varchar(20) DEFAULT NULL COMMENT '部门编码',
                                         `ding_dept_id` bigint(20) NOT NULL COMMENT '钉钉部门id',
                                         `dept_name` varchar(30) NOT NULL COMMENT '部门名称',
                                         `ding_dept_level` int(11) NOT NULL COMMENT '部门级别',
                                         `ding_parent_id` bigint(20) DEFAULT NULL COMMENT '父级部门id',
                                         `company_id` tinyint(1) NOT NULL COMMENT '公司主键id: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台',
                                         `ekb_dept_id` varchar(30) DEFAULT NULL COMMENT '易快报部门id',
                                         `ekb_dept_name` varchar(30) DEFAULT NULL COMMENT '易快报部门名称',
                                         `ekb_parent_id` varchar(30) DEFAULT NULL COMMENT '易快报父级部门id',
                                         `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_ding_dept_id` (`ding_dept_id`),
                                         KEY `idx_ekb_dept_id` (`ekb_dept_id`)
) COMMENT='钉钉与易快报部门映射表';

CREATE TABLE `t_ekb_ding_user_mapping` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `ding_user_id` varchar(64) NOT NULL COMMENT '钉钉user_id',
                                         `ding_user_name` varchar(64) NOT NULL COMMENT '钉钉员工名称',
                                         `ding_dept_id_list` varchar(128) NOT NULL COMMENT '钉钉部门列表,部门id用,分隔',
                                         `mobile` varchar(16) NOT NULL COMMENT '手机号',
                                         `ekb_user_id` varchar(64) DEFAULT NULL COMMENT '易快报user_id',
                                         `ekb_user_name` varchar(64) DEFAULT NULL COMMENT '易快报用户姓名',
                                         `ekb_default_dept_id` varchar(32) DEFAULT NULL COMMENT '易快报默认部门ID',
                                         `ekb_dept_id_list` varchar(300) DEFAULT NULL COMMENT '易快报部门列表',
                                         `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`,`ding_user_id`),
                                         KEY `idx_mobile` (`mobile`),
                                         KEY `idx_ekb_user_id` (`ekb_user_id`)
) COMMENT='钉钉与易快报员工映射表';



