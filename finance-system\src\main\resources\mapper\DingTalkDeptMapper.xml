<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.DingTalkDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.DingTalkDept">
        <id column="id" property="id" />
        <result column="dept_id" property="deptId" />
        <result column="parent_id" property="parentId" />
        <result column="dept_name" property="deptName" />
        <result column="order_num" property="orderNum" />
        <result column="dept_level" property="deptLevel" />
        <result column="dept_manager_userid_list" property="deptManagerUseridList" />
        <result column="source_identifier" property="sourceIdentifier" />
        <result column="is_create_dept_group" property="createDeptGroup" />
        <result column="is_auto_add_user" property="autoAddUser" />
        <result column="is_auto_approve_apply" property="autoApproveApply" />
        <result column="is_from_union_org" property="fromUnionOrg" />
        <result column="tags" property="tags" />
        <result column="dept_group_chat_id" property="deptGroupChatId" />
        <result column="is_group_contain_sub_dept" property="groupContainSubDept" />
        <result column="org_dept_owner" property="orgDeptOwner" />
        <result column="is_outer_dept" property="outerDept" />
        <result column="outer_permit_depts" property="outerPermitDepts" />
        <result column="outer_permit_users" property="outerPermitUsers" />
        <result column="is_hide_dept" property="hideDept" />
        <result column="user_permits" property="userPermits" />
        <result column="dept_permits" property="deptPermits" />
        <result column="dept_status" property="deptStatus" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dept_id, parent_id, dept_name, order_num, dept_level, dept_manager_userid_list, source_identifier, is_create_dept_group, is_auto_add_user, is_auto_approve_apply, is_from_union_org, tags, dept_group_chat_id, is_group_contain_sub_dept, org_dept_owner, is_outer_dept, outer_permit_depts, outer_permit_users, is_hide_dept, user_permits, dept_permits, dept_status, remark, create_by, create_time, update_by, update_time, is_deleted, tenant_id
    </sql>

</mapper>
