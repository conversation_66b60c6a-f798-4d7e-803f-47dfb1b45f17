package com.qmqb.oa.admin.config;

import com.qmqb.oa.common.enums.Tenant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

/**
 * <p>
 * 模板code
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "invoice")
public class InvoiceConfig {

    /**
     * 全民钱包
     */
    @NestedConfigurationProperty
    private Prop qmqb;
    /**
     * 深圳合众
     */
    @NestedConfigurationProperty
    private Prop szhz;
    /**
     * 佛山百益来信息咨询有限公司
     */
    @NestedConfigurationProperty
    private Prop fsbyl;

    @Data
    public static class Prop {
        /**
         * 受票方名称
         */
        private String company;
        /**
         * 受票方税号
         */
        private String code;
        /**
         * 受票方地址
         */
        private String addrPhone;
        /**
         * 受票方开户行、账号
         */
        private String account;

    }

    public String getCompany(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.company;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.company;
        } else {
            return this.qmqb.company;
        }
    }

    public String getCode(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.code;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.code;
        } else {
            return this.qmqb.code;
        }
    }

    public String getAddrPhone(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.addrPhone;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.addrPhone;
        } else {
            return this.qmqb.addrPhone;
        }
    }

    public String getAccount(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.account;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.account;
        } else {
            return this.qmqb.account;
        }
    }
}
