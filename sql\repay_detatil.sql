create table t_repay_detail
(
    id           bigint auto_increment
        primary key,
    program_id   int          null comment '程序编号',
    batch_no     varchar(255) null comment '批次号',
    start_date   datetime     null comment '开始日期',
    end_date     datetime     null comment '结束日期',
    file_path    varchar(512) null comment '文件路径',
    creator      varchar(25)  null comment '创建人',
    updater      varchar(25)  null comment '更新人',
    db_create_dt datetime     null comment '记录创建时间',
    db_update_dt datetime     null comment '记录更新时间',
    data_type    int          null comment '数据类型:1-资金还款明细, 2-资金资产还款明细',
    constraint t_batch_no_index
        unique (batch_no)
)
    comment '对账明细表';

create index t_start_date_index
    on t_repay_detail (start_date);


INSERT INTO `sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '对账明细-数据类型', 'repay_detail_data_type', '0', 'admin', '2025-03-20 20:32:27', '', NULL, NULL);

INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '资金还款明细', '1', 'repay_detail_data_type', NULL, NULL, 'N', '0', 'admin', '2025-03-20 20:38:58', 'admin', '2025-03-20 20:40:08', NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '资金资产还款明细', '2', 'repay_detail_data_type', NULL, NULL, 'N', '0', 'admin', '2025-03-20 20:39:57', '', NULL, NULL);


INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1121, '对账明细', 1096, 2, 'reconciliationDetail', 'finance/reconciliationDetail/index', 1, 0, 'C', '0', '0', '', 'list', 'admin', '2025-03-20 11:11:59', 'admin', '2025-03-20 11:14:16', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1122, '下载', 1121, 1, '', NULL, 1, 0, 'F', '0', '0', 'finance:file:download', '#', 'admin', '2025-03-20 17:31:49', 'admin', '2025-03-20 20:27:26', '');
