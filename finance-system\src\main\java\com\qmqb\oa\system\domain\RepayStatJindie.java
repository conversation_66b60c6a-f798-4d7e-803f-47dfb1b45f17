package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.oa.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_repay_stat_jindie")
public class RepayStatJindie implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Excel(name = "批次号")
    private String batchNo;

    @Excel(name = "单据头(序号)")
    private String fbillhead;

    @Excel(name = "(单据头)账簿#编码")
    private String faccountbookid;

    @Excel(name = "(单据头)账簿#名称")
    private String faccountbookidName;

    @Excel(name = "(单据头)日期")
    private String fdate;

    @Excel(name = "(单据头)凭证字#编码")
    private String fvouchergroupid;

    @Excel(name = "(单据头)凭证字#名称")
    private String fvouchergroupidName;

    @Excel(name = "(单据头)凭证号")
    private String fvouchergroupno;

    @Excel(name = "(单据头)核算组织#编码")
    private String faccbookorgid;

    @Excel(name = "(单据头)核算组织#名称")
    private String faccbookorgidName;

    @Excel(name = "间隔列")
    private String split;

    @Excel(name = "分录(序号)")
    private String fentity;

    @Excel(name = "(分录)摘要")
    private String fexplanation;

    @Excel(name = "(分录)科目编码#编码")
    private String faccountid;

    @Excel(name = "(分录)科目编码#名称")
    private String faccountidName;

    @Excel(name = "(分录)科目全名")
    private String facctfullname;

    @Excel(name = "(分录)银行#编码")
    private String fdetailidFflex14;

    @Excel(name = "(分录)银行#名称")
    private String fdetailidFflex14Name;

    @Excel(name = "(分录)银行账号#编码")
    private String fdetailidFflex15;

    @Excel(name = "(分录)银行账号#名称")
    private String fdetailidFflex15Name;

    @Excel(name = "(分录)物料分组#编码")
    private String fdetailidFflex12;

    @Excel(name = "(分录)物料分组#名称")
    private String fdetailidFflex12Name;

    @Excel(name = "(分录)客户分组#编码")
    private String fdetailidFflex13;

    @Excel(name = "(分录)客户分组#名称")
    private String fdetailidFflex13Name;

    @Excel(name = "产品#编码")
    private String fdetailidFf100003;

    @Excel(name = "(分录)产品#名称")
    private String fdetailidFf100003Name;

    @Excel(name = "(分录)信托包#编码")
    private String fdetailidFf100004;

    @Excel(name = "(分录)信托包#名称")
    private String fdetailidFf100004Name;

    @Excel(name = "(分录)其他往来单位#编码")
    private String fdetailidFflex16;

    @Excel(name = "(分录)他往来单位#名称")
    private String fdetailidFflex16Name;

    @Excel(name = "(分录)项目#编码")
    private String fdetailidFf100002;

    @Excel(name = "(分录)项目#名称")
    private String fdetailidFf100002Name;

    @Excel(name = "(分录)组织机构#编码")
    private String fdetailidFflex11;

    @Excel(name = "(分录)组织机构#名称")
    private String fdetailidFflex11Name;

    @Excel(name = "(分录)部门#编码")
    private String fdetailidFflex5;

    @Excel(name = "(分录)部门#名称")
    private String fdetailidFflex5Name;

    @Excel(name = "(分录)客户#编码")
    private String fdetailidFflex6;

    @Excel(name = "(分录)客户#名称")
    private String fdetailidFflex6Name;

    @Excel(name = "(分录)往来单位#编码")
    private String fdetailidFflex4;

    @Excel(name = "(分录)往来单位#名称")
    private String fdetailidFflex4Name;

    @Excel(name = "(分录)费用项目#编码")
    private String fdetailidFflex9;

    @Excel(name = "(分录)费用项目#名称")
    private String fdetailidFflex9Name;

    @Excel(name = "(分录)资产类别#编码")
    private String fdetailidFflex10;

    @Excel(name = "(分录)资产类别#名称")
    private String fdetailidFflex10Name;

    @Excel(name = "(分录)员工#编码")
    private String fdetailidFflex7;

    @Excel(name = "(分录)员工#名称")
    private String fdetailidFflex7Name;

    @Excel(name = "(分录)物料#编码")
    private String fdetailidFflex8;

    @Excel(name = "(分录)物料#名称")
    private String fdetailidFflex8Name;

    @Excel(name = "(分录)币别#编码")
    private String fcurrencyid;

    @Excel(name = "(分录)币别#名称")
    private String fcurrencyidName;

    @Excel(name = "(分录)汇率类型#编码")
    private String fexchangeratetype;

    @Excel(name = "(分录)汇率类型#名称")
    private String fexchangeratetypeName;

    @Excel(name = "(分录)借方金额")
    private String fdebit;

    @Excel(name = "(分录)贷方金额")
    private String fcredit;


}
