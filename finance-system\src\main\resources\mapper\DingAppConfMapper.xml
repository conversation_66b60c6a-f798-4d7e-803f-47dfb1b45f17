<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.DingAppConfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.DingAppConf">
        <id column="id" property="id" />
        <result column="ding_h5_app_name" property="dingH5AppName" />
        <result column="company_name" property="companyName" />
        <result column="company_id" property="companyId" />
        <result column="ding_corp_id" property="dingCorpId" />
        <result column="ding_app_key" property="dingAppKey" />
        <result column="ding_app_secret" property="dingAppSecret" />
        <result column="partner_app_key" property="partnerAppKey" />
        <result column="partner_app_secret" property="partnerAppSecret" />
        <result column="app_switch" property="appSwitch" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ding_h5_app_name, company_name, company_id, ding_corp_id, ding_app_key, ding_app_secret,partner_app_key,partner_app_secret,app_switch,remark, create_time, update_time
    </sql>

</mapper>
