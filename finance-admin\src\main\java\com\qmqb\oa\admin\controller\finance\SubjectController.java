package com.qmqb.oa.admin.controller.finance;

import com.hzed.structure.common.util.CollUtil;
import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.common.utils.SecurityUtils;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.system.domain.Subject;
import com.qmqb.oa.system.domain.SubjectDept;
import com.qmqb.oa.system.service.SubjectDeptService;
import com.qmqb.oa.system.service.SubjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 科目Controller
 *
 * <AUTHOR>
 * @date 2021-05-24
 */
@RestController
@RequestMapping("/finance/subject")
public class SubjectController extends BaseController {

    @Autowired
    private SubjectService subjectService;
    @Autowired
    private SubjectDeptService subjectDeptService;

    /**
     * 查询科目列表
     */
    @PreAuthorize("@ss.hasPermi('finance:subject:list')")
    @GetMapping("/list")
    public AjaxResult list(Subject subject) {
        List<Subject> list = subjectService.selectSubjectList(subject);
        return AjaxResult.success(list);
    }

    /**
     * 导出科目列表
     */
    @PreAuthorize("@ss.hasPermi('finance:subject:export')")
    @Log(title = "科目", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Subject subject) {
        List<Subject> list = subjectService.selectSubjectList(subject);
        ExcelUtil<Subject> util = new ExcelUtil<Subject>(Subject.class);
        return util.exportExcel(list, "科目");
    }

    /**
     * 获取科目详细信息
     */
    @PreAuthorize("@ss.hasPermi('finance:subject:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(subjectService.selectSubjectById(id));
    }

    /**
     * 新增科目
     */
    @PreAuthorize("@ss.hasPermi('finance:subject:add')")
    @Log(title = "科目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Subject subject) {
        if (subject.getSubjectCode().equals(subject.getParentSubjectCode())) {
            return AjaxResult.error("新增科目失败,科目编码与父级科目编码不能相同");
        }
        SecurityUtils.setCreateInfo(subject);
        boolean success = subjectService.save(subject);
        List<Long> deptIdList = subject.getDeptIds();
        if (CollUtil.isNotEmpty(deptIdList)) {
            List<SubjectDept> list = new ArrayList<>();
            deptIdList.forEach(deptId -> {
                SubjectDept subjectDept = new SubjectDept();
                subjectDept.setSubjectId(subject.getId());
                subjectDept.setDeptId(deptId);
                list.add(subjectDept);
            });
            success = subjectDeptService.saveBatch(list);
        }
        return toAjax(success);
    }

    /**
     * 修改科目
     */
    @PreAuthorize("@ss.hasPermi('finance:subject:edit')")
    @Log(title = "科目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Subject subject) {
        SecurityUtils.setUpdateInfo(subject);
        boolean success = subjectService.updateById(subject);
        List<Long> deptIdList = subject.getDeptIds();
        if (CollUtil.isNotEmpty(deptIdList)) {
            if(subjectDeptService.checkSubjectDeptBySubjectIds(new Long[]{subject.getId()})) {
                // 删除旧关系
                success = subjectDeptService.deleteSubjectDeptBySubjectIds(new Long[]{subject.getId()});
                if (!success) {
                    return AjaxResult.error();
                }
            }
            List<SubjectDept> list = new ArrayList<>();
            deptIdList.forEach(deptId -> {
                SubjectDept subjectDept = new SubjectDept();
                subjectDept.setSubjectId(subject.getId());
                subjectDept.setDeptId(deptId);
                list.add(subjectDept);
            });
            // 保存新关系
            success = subjectDeptService.saveBatch(list);
        }
        return toAjax(success);
    }

    /**
     * 删除科目
     */
    @PreAuthorize("@ss.hasPermi('finance:subject:remove')")
    @Log(title = "科目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        boolean success = subjectService.deleteSubjectByIds(ids);
        if (!success) {
            return AjaxResult.error("存在下级科目,不允许删除");
        }
        return AjaxResult.success();
    }
}
