<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.BusinessNoticeMapper">
    <resultMap type="com.qmqb.oa.system.domain.BusinessNotice" id="BusinessNoticeResult">
        <result property="id" column="id"/>
        <result property="noticeObject" column="notice_object"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="noticeTime" column="notice_time"/>
        <result property="noticeCount" column="notice_count"/>
        <result property="noticeStatus" column="notice_status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectBusinessNoticeVo">
        select id,
               notice_object,
               serial_number,
               notice_content,
               notice_time,
               notice_count,
               notice_status,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               is_deleted,
               tenant_id
        from t_business_notice
    </sql>

    <select id="selectBusinessNoticeList" parameterType="com.qmqb.oa.system.domain.BusinessNotice"
            resultMap="BusinessNoticeResult">
        <include refid="selectBusinessNoticeVo"/>
        <where>
            <if test="noticeObject != null  and noticeObject != ''">
                and notice_object = #{noticeObject}
            </if>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="noticeContent != null  and noticeContent != ''">
                and notice_content = #{noticeContent}
            </if>
            <if test="noticeTime != null ">
                and notice_time = #{noticeTime}
            </if>
            <if test="noticeCount != null ">
                and notice_count = #{noticeCount}
            </if>
            <if test="noticeStatus != null ">
                and notice_status = #{noticeStatus}
            </if>
            <if test="isDeleted != null ">
                and is_deleted = #{isDeleted}
            </if>
            <if test="tenantId != null ">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="selectBusinessNoticeById" parameterType="Long"
            resultMap="BusinessNoticeResult">
        <include refid="selectBusinessNoticeVo"/>
        where id = #{id}
    </select>

    <insert id="insertBusinessNotice" parameterType="com.qmqb.oa.system.domain.BusinessNotice"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into t_business_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeObject != null and noticeObject != ''">
                notice_object,
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                serial_number,
            </if>
            <if test="noticeContent != null and noticeContent != ''">
                notice_content,
            </if>
            <if test="noticeTime != null">
                notice_time,
            </if>
            <if test="noticeCount != null">
                notice_count,
            </if>
            <if test="noticeStatus != null">
                notice_status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeObject != null and noticeObject != ''">
                #{noticeObject},
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                #{serialNumber},
            </if>
            <if test="noticeContent != null and noticeContent != ''">
                #{noticeContent},
            </if>
            <if test="noticeTime != null">
                #{noticeTime},
            </if>
            <if test="noticeCount != null">
                #{noticeCount},
            </if>
            <if test="noticeStatus != null">
                #{noticeStatus},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <update id="updateBusinessNotice" parameterType="com.qmqb.oa.system.domain.BusinessNotice">
        update t_business_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeObject != null and noticeObject != ''">
                notice_object =
                #{noticeObject},
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                serial_number =
                #{serialNumber},
            </if>
            <if test="noticeContent != null and noticeContent != ''">
                notice_content =
                #{noticeContent},
            </if>
            <if test="noticeTime != null">
                notice_time =
                #{noticeTime},
            </if>
            <if test="noticeCount != null">
                notice_count =
                #{noticeCount},
            </if>
            <if test="noticeStatus != null">
                notice_status =
                #{noticeStatus},
            </if>
            <if test="remark != null">
                remark =
                #{remark},
            </if>
            <if test="createBy != null">
                create_by =
                #{createBy},
            </if>
            <if test="createTime != null">
                create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">
                update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time =
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted =
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                tenant_id =
                #{tenantId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessNoticeById" parameterType="Long">
        delete
        from t_business_notice
        where id = #{id}
    </delete>

    <delete id="deleteBusinessNoticeByIds" parameterType="String">
        delete from t_business_notice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
