package com.qmqb.oa.system.mapper;

import com.qmqb.oa.common.core.domain.dto.RepayGenConditionDto;
import com.qmqb.oa.system.domain.RepayGenRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 还款信息生成记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface RepayGenRecordMapper extends BaseMapper<RepayGenRecord> {
    /**
     * 通过id获取批次号
     * @param id
     * @return
     */
    String getBatchNoById(Long id);

    /**
     * 通过条件筛选
     * @param record
     * @return
     */
    List<RepayGenRecord> listByCondition(@Param("condition") RepayGenConditionDto record);
}
