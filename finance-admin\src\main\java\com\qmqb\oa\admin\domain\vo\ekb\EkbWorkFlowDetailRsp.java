package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 易快报-工作流任务详情rsp
 * <AUTHOR>
 * @date 2024/7/31
 */
@Data
public class EkbWorkFlowDetailRsp {

    private ExpenseForm value;
    @Data
    public class ExpenseForm {
        private int pipeline;
        private String grayver;
        private int version;
        private boolean active;
        private long createTime;
        private long updateTime;
        private String corporationId;
        private Object sourceCorporationId;
        private Object dataCorporationId;

        private Form form;

        private String ownerId;
        private String ownerDefaultDepartment;
        private String state;
        private String flowType;
        private String formType;
        private Object logs;

        private Map<String, List<String>> actions;
        private boolean invoiceRemind;
        private String id;

       
    }


    @Data
    public static class Form {
        private String code;
        private String title;
        private List<Detail> details;
        private List<String> expenseLinks;
        // private String expenseLink; // Uncomment if needed  
        private String payeeId;
        private PayMoney payMoney;
        private String voucherNo;
        private String printCount;
        private String printState;
        private long submitDate;
        private String description;
        private long expenseDate;
        private String submitterId;
        private PayMoney expenseMoney;
        private String voucherStatus;
        private PayMoney companyRealPay;
        private String paymentChannel;
        private String paymentAccountId;
        private String specificationId;
        private PayMoney writtenOffMoney;
        private String expenseDepartment;
        private long voucherCreateTime;
        private List<WrittenOffRecord> writtenOffRecords;

    }

    @Data
    public static class Detail {
        private String feeTypeId;
        private Object feeTypeForm;
        private String specificationId;
        private FeeType feeType;
    }

    @Data
    public static class FeeType {
        private String id;
        private String name;
        private String parentId;
        private boolean active;
        private String code;
    }

    @Data
    public static class PayMoney {
        private String standard;
        private String standardUnit;
        private String standardScale;
        private String standardSymbol;
        private String standardNumCode;
        private String standardStrCode;
    }

    @Data
    public static class WrittenOffRecord {
        private String id;
        private String amount;
        private String loanId;
    }

}
