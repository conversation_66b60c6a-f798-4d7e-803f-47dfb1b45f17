package com.qmqb.oa.framework.filter;

import com.hzed.structure.common.enums.TraceTypeEnum;
import com.qmqb.oa.common.utils.MdcUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 日志打印过滤器
 * </p>
 *
 * <AUTHOR>
 * @date 2021-03-08
 */
@Component
public class RequestLogFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            MdcUtil.setWebTraceMsg(TraceTypeEnum.TRACE_16, request);
            filterChain.doFilter(request, response);
        } finally {
            MdcUtil.clear();
        }
    }

}
