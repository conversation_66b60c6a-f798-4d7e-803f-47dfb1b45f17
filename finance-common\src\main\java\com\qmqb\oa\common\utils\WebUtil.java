package com.qmqb.oa.common.utils;


import com.hzed.structure.common.constant.StringPool;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class WebUtil extends com.hzed.structure.tool.util.WebUtil {


    public static String getMethod(HttpServletRequest request) {
        if (Objects.isNull(request)) {
            return StringPool.EMPTY;
        }
        return request.getMethod();
    }

    public static String getSpanHeader(HttpServletRequest request) {
        if (Objects.isNull(request)) {
            return StringPool.EMPTY;
        }
        return request.getHeader(StringPool.TRACE_SPAN_ID);
    }

}
