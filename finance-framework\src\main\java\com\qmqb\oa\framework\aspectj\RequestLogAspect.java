package com.qmqb.oa.framework.aspectj;

import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.hzed.structure.common.constant.LogConstant;
import com.hzed.structure.tool.util.JacksonUtil;
import com.hzed.structure.tool.util.WebUtil;
import com.qmqb.oa.common.constant.StringPool;
import com.qmqb.oa.framework.config.RequestLogConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 请求日志切面
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RequestLogAspect {

    private final RequestLogConfig config;

    /**
     * 请求日志织入点
     */
    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void requestLogPointCut() {
    }

    /**
     * 请求日志切面
     *
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around(value = "requestLogPointCut()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        HttpServletRequest request = WebUtil.getRequest();
        if (Objects.isNull(request)) {
            return proceedingJoinPoint.proceed();
        }
        String path = request.getServletPath();
        if (isExcludePath(path)) {
            return proceedingJoinPoint.proceed();
        }
        handleRequestLog(proceedingJoinPoint);
        long now = SystemClock.now();
        Object obj = proceedingJoinPoint.proceed();
        handleResponseLog(now, obj);
        return obj;
    }

    /**
     * 请求日志
     *
     * @param proceedingJoinPoint
     */
    private void handleRequestLog(ProceedingJoinPoint proceedingJoinPoint) {
        try {
            MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
            String[] parameterNames = signature.getParameterNames();
            List<String> params = new ArrayList<>();
            Object[] args = proceedingJoinPoint.getArgs();
            if (ArrayUtil.isNotEmpty(args)) {
                for (int i = 0; i < args.length; i++) {
                    Object arg = args[i];
                    if (isExcludeArg(arg)) {
                        continue;
                    }
                    String parameterName = parameterNames[i];
                    String argName = parameterName + StringPool.COLON;
                    String argValue = JacksonUtil.toJson(arg);
                    params.add(argName + argValue);
                }
            }
            log.info("请求参数: {}", params);
        } catch (Exception e) {
            log.error("打印请求参数异常", e);
        }
    }

    /**
     * 响应日志
     *
     * @param now
     * @param obj
     */
    private void handleResponseLog(long now, Object obj) {
        try {
            long costTime = SystemClock.now() - now;
            log.info("响应参数: {} ,响应时间: {} 毫秒", JacksonUtil.toJson(obj), costTime);
        } catch (Exception e) {
            log.warn("打印响应参数异常", e);
        }
    }

    /**
     * 判断是否为排除的参数
     *
     * @param arg
     * @return
     */
    private boolean isExcludeArg(Object arg) {
        if (Objects.isNull(arg)) {
            return true;
        }
        //默认排除 不排除的话 像HttpResponseServlet等输出会出错
        if (arg instanceof HttpServletRequest || arg instanceof HttpServletResponse || arg instanceof MultipartFile || arg instanceof InputStream || arg instanceof BindingResult) {
            return true;
        }
        //默认排除spring/javax/io/本项目的包名 ->不排除输出日志会异常
        Class<?> clz = arg.getClass();
        if (Objects.isNull(clz)) {
            return false;
        }
        Package clzPackage = clz.getPackage();
        if (Objects.isNull(clzPackage)) {
            return false;
        }
        String packageName = clzPackage.getName();
        return packageName.startsWith(LogConstant.SPRING_PACKAGE_PREFIX) || packageName.startsWith(LogConstant.JAVAX_PACKAGE_PREFIX) || packageName.startsWith(LogConstant.JAVA_IO_PACKAGE_PREFIX) || packageName.startsWith(LogConstant.LOG_PACKAGE_PREFIX);
    }

    /**
     * 判断是否为排除的路径
     *
     * @param path 需判定的路径
     */
    private boolean isExcludePath(String path) {
        if (StrUtil.isEmpty(path)) {
            return false;
        }
        String[] excludePatterns = config.getExcludePatterns();
        if (ArrayUtil.isEmpty(excludePatterns)) {
            return false;
        }
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        for (String pattern : excludePatterns) {
            if (antPathMatcher.match(pattern.trim(), path)) {
                return true;
            }
        }
        return false;
    }

}
