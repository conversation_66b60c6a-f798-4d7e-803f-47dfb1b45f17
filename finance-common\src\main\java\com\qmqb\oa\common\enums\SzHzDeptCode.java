package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 深圳合众部门编码
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SzHzDeptCode {
    /**
     * 部门编码, 拼音便于区分
     */
    CAI_WU("001", "财务中心"),
    CHE_DAI("002", "车贷事业部"),
    FA_WU("003", "法务中心"),
    FENG_KONG("004", "风控中心"),
    GUAN_LI("005", "管理部"),
    HE_GUI("006", "合规中心"),
    JI_GOU_HE_ZUO("007", "机构合作部"),
    JI_SHU("008", "技术中心"),
    QI_YE_FA_ZHAN("009", "企业发展中心"),
    REN_LI_XING_ZHENG("010", "人力行政中心"),
    BIAN_WAI("011", "编外"),
    TOU_RONG_ZI("012", "投融资事业部"),
    YUN_YING("013", "运营中心"),
    ZHI_FU_JIAO_YI("014", "支付与交易项目组"),
    ZI_CHAN_GUAN_LI("015", "资产管理中心"),
    ZI_CHAN_SHI_YE("016", "资产事业中心"),
    ZONG_JING_BAN("017", "总经办"),
    JIN_RONG_CHAN_PIN("018", "金融产品中心"),
    HAI_WAI_SHI_YE("019", "海外事业部"),
    SZ_YUN_YING("020", "深圳运营中心"),
    GZ_YUN_YING("021", "广州运营中心"),
    CAI_FU_SHI_YE("022", "财富事业部"),
    XING_ZHENG("023", "行政部"),
    REN_LI_ZI_YUAN("024", "人力资源部"),
    TOU_ZI_ZHE_GUAN_XI("025", "投资者关系部"),
    DIAN_SHANG_SHI_YE("026", "电商事业部"),
    CUN_LIANG_YE_WU("027", "存量业务组"),
    DAI_HOU_ZHONG_CAI("028", "贷后仲裁组"),
    ;

    private final String code;
    private final String name;

}
