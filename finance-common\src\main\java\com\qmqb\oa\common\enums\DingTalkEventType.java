package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 钉钉事件类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Getter
@AllArgsConstructor
public enum DingTalkEventType {

    /**
     * 钉钉事件类型
     */
    DEPT_CREATE("org_dept_create", "通讯录企业部门创建"),
    DEPT_MODIFY("org_dept_modify", "通讯录企业部门修改"),
    DEPT_DELETE("org_dept_remove", "通讯录企业部门删除"),
    USER_ADD_ORG("user_add_org", "通讯录企业增加员工事件"),
    USER_MODIFY_ORG("user_modify_org", "通讯录企业修改员工事件"),
    USER_LEAVE_ORG("user_leave_org", "通讯录用户离职"),

    ;

    /**
     * 根据事件类型获取事件
     * @param eventType
     * @return
     */
    public static DingTalkEventType getEventTypeByType(String eventType) {
        for (DingTalkEventType value : DingTalkEventType.values()) {
            if ( value.getEventType().equals(eventType) ) {
                return value;
            }
        }
        return null;
    }

    private final String eventType;
    private final String eventDesc;

}
