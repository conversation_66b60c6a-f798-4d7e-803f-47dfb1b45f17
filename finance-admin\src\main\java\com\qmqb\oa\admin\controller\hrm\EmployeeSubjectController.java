package com.qmqb.oa.admin.controller.hrm;

import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.system.domain.EmployeeSubject;
import com.qmqb.oa.system.service.EmployeeSubjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工主体Controller
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@RestController
@RequestMapping("/hrm/subject")
public class EmployeeSubjectController extends BaseController {
    @Autowired
    private EmployeeSubjectService employeeSubjectService;

    /**
     * 查询员工主体列表
     */
    @PreAuthorize("@ss.hasPermi('hrm:subject:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmployeeSubject employeeSubject) {
        startPage();
        List<EmployeeSubject> list = employeeSubjectService.selectEmployeeSubjectList(employeeSubject);
        return getDataTable(list);
    }

    /**
     * 导出员工主体列表
     */
    @PreAuthorize("@ss.hasPermi('hrm:subject:export')")
    @Log(title = "员工主体", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EmployeeSubject employeeSubject) {
        List<EmployeeSubject> list = employeeSubjectService.selectEmployeeSubjectList(employeeSubject);
        ExcelUtil<EmployeeSubject> util = new ExcelUtil<EmployeeSubject>(EmployeeSubject.class);
        return util.exportExcel(list, "subject");
    }

    /**
     * 获取员工主体详细信息
     */
    @PreAuthorize("@ss.hasPermi('hrm:subject:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(employeeSubjectService.selectEmployeeSubjectById(id));
    }

    /**
     * 新增员工主体
     */
    @PreAuthorize("@ss.hasPermi('hrm:subject:add')")
    @Log(title = "员工主体", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmployeeSubject employeeSubject) {
        return toAjax(employeeSubjectService.insertEmployeeSubject(employeeSubject));
    }

    /**
     * 修改员工主体
     */
    @PreAuthorize("@ss.hasPermi('hrm:subject:edit')")
    @Log(title = "员工主体", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmployeeSubject employeeSubject) {
        return toAjax(employeeSubjectService.updateEmployeeSubject(employeeSubject));
    }

    /**
     * 删除员工主体
     */
    @PreAuthorize("@ss.hasPermi('hrm:subject:remove')")
    @Log(title = "员工主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(employeeSubjectService.deleteEmployeeSubjectByIds(ids));
    }


}
