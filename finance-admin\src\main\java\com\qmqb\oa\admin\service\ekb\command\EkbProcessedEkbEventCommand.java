package com.qmqb.oa.admin.service.ekb.command;

import com.google.common.collect.Lists;
import com.hzed.structure.common.api.ResultCode;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.tool.api.ApiResponse;
import com.qmqb.oa.admin.domain.dto.EkbEventCommandDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbDingTodoResultDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.common.enums.EkbActionEnum;
import com.qmqb.oa.common.enums.EkbFlowStageNameEnums;
import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import com.qmqb.oa.system.service.EkbDingTodoMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Description ekb 已处理事件
 * @Date 2024\10\12 0012 10:50
 * @Version 1.0
 */

@Slf4j
@Service(value = "ekbProcessedCommand")
public class EkbProcessedEkbEventCommand extends AbstractEkbEventCommand<EkbReceiveEkbEventRequest, EkbEventCommandDTO> {
    @Resource
    private EkbDingTodoMessageService ekbDingTodoMessageService;
    @Override
    public boolean validation() {
        if (!super.checkBaseParams()) {
            return false;
        }

        if (!getParamObject().getAction().equals(EkbActionEnum.BACKLOG_PROCESSED.getAction())) {
            return false;
        }

        return true;
    }

    @Override
    public ApiResponse<EkbEventCommandDTO> execute() {
        if (!validation()) {
            return ApiResponse.fail("已处理事件参数校验失败");
        }

        if (eventHasExecute()) {
            return ApiResponse.success(ResultCode.SUCCESS, "ekb processed event deal success");
        }

        EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();

        List<String> msgActions = Lists.newArrayList(EkbActionEnum.BACKLOG_APPROVING.getAction(), EkbActionEnum.BACKLOG_PAYING.getAction());
        List<EkbDingTodoMessage> currentApprovingTodoMsgs = getCurrentDealTodoMsgList(request.getFlowId(), msgActions);

        if (CollUtil.isEmpty(currentApprovingTodoMsgs)) {
            return ApiResponse.fail("已处理事件未找到对应的待审批数据列表");
        }
        //查询出当前审批节点的所有审批人的未处理的待办任务列表
        List<EkbDingTodoMessage> necessaryDealEkbTasks = getUnHandlerTodoListOnSameNode(request, eventCommandDTO);
        //更新易快报到钉钉待办消息表钉钉发送状态
        Map<Long, Boolean> batchUpdateResult = updateTodoDingMsg(request, necessaryDealEkbTasks);
        ApiResponse apiResponse = null;
        Long[] taskId = new Long[1];
        boolean isExistsExecuteFailure = CollUtil.isNotEmpty(batchUpdateResult);
        for (Map.Entry<Long, Boolean> updateResult : batchUpdateResult.entrySet()) {
            if (updateResult.getValue()) {
                taskId[0] = updateResult.getKey();
                //更新待办消息结果处理状态
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build()
                        .setTaskProcessedResult(EkbFlowStageNameEnums.PROCESSED.getDbCode())
                        .setMsgAction(EkbActionEnum.BACKLOG_PROCESSED.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
            } else {
                //失败的记录下最后执行的action
                EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build()
                        .setMsgAction(EkbActionEnum.BACKLOG_PROCESSED.getAction());
                updateApprovingMsgRemark(ekbDingTodoResultDTO, taskId);
                isExistsExecuteFailure = true;
            }
        }

        //存在执行失败的任务
        if (isExistsExecuteFailure) {
            apiResponse = ApiResponse.fail();
        } else {
            apiResponse = ApiResponse.success(ResultCode.SUCCESS, "ekb processed event deal success");
        }

        //设置处理后的事件传输实体
        apiResponse.setData(getEkbEventCommandDTO());
        return apiResponse;
    }


}
