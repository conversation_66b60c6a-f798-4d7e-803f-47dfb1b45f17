package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.ReimbursementVoucher;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报销凭证表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
public interface ReimbursementVoucherService extends IService<ReimbursementVoucher> {

    /**
     * 根据processInstanceId查询报销凭证信息
     *
     * @return
     */
    List<ReimbursementVoucher> getByProcessInstanceId(String processInstanceId);

    /**
     * 根据制单日期查询最新一条报销凭证信息
     *
     * @return
     */
    ReimbursementVoucher getLatestByMakeDate(Date beginDate, Date endDate);


    /**
     * 查询报销凭证列表
     *
     * @param reimbursementVoucher 报销凭证
     * @return 报销凭证集合
     */
    List<ReimbursementVoucher> selectReimbursementVoucherList(ReimbursementVoucher reimbursementVoucher);

}
