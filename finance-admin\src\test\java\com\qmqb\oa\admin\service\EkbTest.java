package com.qmqb.oa.admin.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.response.*;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchUpdateRoleUserInfoReq;
import com.qmqb.oa.admin.service.hsfk.DingTalkDeptService;
import com.qmqb.oa.admin.service.hsfk.DingTalkUserService;
import com.qmqb.oa.admin.service.hsfk.*;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.admin.task.SyncDingDeptTask;
import com.qmqb.oa.admin.task.SyncDingUserInfoTask;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.*;
import com.qmqb.oa.system.service.*;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import shade.com.alibaba.fastjson2.JSONArray;
import shade.com.alibaba.fastjson2.JSONObject;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 易快报业务测试类
 *
 * <AUTHOR>
 * @date 2024/7/17
 */
@Slf4j
@ActiveProfiles("dev")
public class EkbTest extends BaseTest {
    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private SyncEkbService syncEkbService;
    @Autowired
    private DingTalkOApiClient dingTalkApiClient;
    @Autowired
    private DingDeptService dingDeptService;
    @Autowired
    private EkbDeptService ekbDeptService;
    @Autowired
    private EkbDingDeptMappingService ekbDingDeptMappingService;
    @Autowired
    private EkbDingUserMappingService ekbDingUserMappingService;
    @Autowired
    private EkbUserService ekbUserService;
    @Autowired
    private SyncDingUserInfoTask syncDingUserInfoTask;
    @Autowired
    private DingTalkDeptService dingTalkDeptService;
    private final int COMPANY_LEVEL = 1;
    private final int ROOT_DEPT_LEVEL = 2;
    private final int MAX_DEPT_LEVEL = 6;
    private final int NUM_7 = 7;
    private final int NUM_10 = 10;
    @Test
    public void testModifyDept() throws Exception{
        String bizData = "{\"data\":{\"timeStamp\":\"1734510909957\",\"eventId\":\"ceac55488ece4c229677d9e1e5cf3366\",\"deptId\":[*********]},\"eventBornTime\":1734510909958,\"eventCorpId\":\"dingdc0a16e71772ca06bc961a6cb783455b\",\"eventId\":\"ceac55488ece4c229677d9e1e5cf3366\",\"eventType\":\"org_dept_modify\",\"eventUnifiedAppId\":\"02cd27c7-a770-4f05-b0c7-dbd7756ddd6c\"}";
        dingTalkDeptService.modifyDept4DingAndEkb(JSONObject.parseObject(bizData).getJSONObject("data"),5);
    }

    @Test
    public void pullDeptFromEkbList(){
        int count = 100;
        for(int start = 0;start<ROOT_DEPT_LEVEL;start++ ){

            syncEkbService.pullDeptFromEkbList(start*count, count);
        }
    }

    @Test
    public void pushDepts2Ekb(){
        int level = COMPANY_LEVEL;
        syncEkbService.pushDepts2Ekb(level);
    }

    @Test
    public void testUpdate(){

        LambdaUpdateWrapper<DingAppConf> up = Wrappers.lambdaUpdate(DingAppConf.class)
                .set(DingAppConf::getUpdateTime, LocalDateTime.now())
                .eq(DingAppConf::getCompanyId, 0);
        dingAppConfService.update(up);
    }

    @Test
    public void deptCodeByInterface(){
        Integer tenantId = Tenant.QMQB.getValue();
        TenantContextHolder.setTenantId(tenantId);
        MdcUtil.setTrace();
        MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "部门列表");
        for (int i = 1; i <=MAX_DEPT_LEVEL ; i++) {
            LambdaQueryWrapper<DingDept> wrapper = Wrappers.lambdaQuery(DingDept.class)
                    .eq(DingDept::getCompanyId, tenantId).eq(DingDept::getDeptLevel, i);
            List<DingDept> deptList = dingDeptService.list(wrapper);
            if(deptList.isEmpty()){
                continue;
            }
            int order = 1;
            for (DingDept p : deptList) {
                log.info("查询{}的子部门",p.getDeptName());
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> childList = dingTalkApiClient.listDepartment(p.getDeptId());
                log.info(JSON.toJSONString(childList));
                for (OapiV2DepartmentListsubResponse.DeptBaseResponse c : childList) {
                    DingDept dingDept = new DingDept();
                    dingDept.setDeptId(c.getDeptId());
                    dingDept.setParentId(c.getParentId());
                    dingDept.setDeptName(c.getName());
                    dingDept.setDeptLevel(i+1);
                    dingDept.setDeptCode(getDeptCode(p.getDeptCode(),order));
                    dingDept.setDeptChainName(p.getDeptChainName() + "-" + c.getName());
                    dingDept.setCompanyId(tenantId);
                    order ++;
                    dingDeptService.save(dingDept);
                }

            }

        }


    }

    public String getDeptCode(String parentCode,Integer order){
        String pre = parentCode;
        String end = order.toString();
        if(order < NUM_10){
            end = "0" + order;
        }
        return pre + end;
    }

    @Test
    public void testUpdateRoleUserInfo() {
        List<EkbDingUserMapping> dingUserMappingList = ekbDingUserMappingService.list();
        EkbBatchUpdateRoleUserInfoReq ekbBatchUpdateRoleUserInfoReq = new EkbBatchUpdateRoleUserInfoReq();
        List<EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq> ekbUpdateRoleUserInfoReqs = new ArrayList<>();
        HashMap<String, List<String>> map = new HashMap<>(8);
        for (EkbDingUserMapping ekbDingUserMapping : dingUserMappingList) {
            if (ekbDingUserMapping.getDingDeptIdList().contains(",")) {
                String[] split = ekbDingUserMapping.getDingDeptIdList().split(",");
                for (String ekbDeptId : split) {
                    map.computeIfAbsent(ekbDeptId, k -> new ArrayList<>()).add(ekbDingUserMapping.getDingUserName());
                }
            } else {
                map.computeIfAbsent(ekbDingUserMapping.getDingDeptIdList(), k -> new ArrayList<>()).add(ekbDingUserMapping.getDingUserName());
            }
        }
        map.entrySet().stream().forEach((entry) -> {
            System.out.println("Key = " + entry.getKey() + ", Value = " + entry.getValue());
            EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq ekbUpdateRoleUserInfoReq = new EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq();
            ekbUpdateRoleUserInfoReq.setPathType("id");
            List<String> pathList = new ArrayList<>();
            pathList.add(entry.getKey());
            ekbUpdateRoleUserInfoReq.setPath(pathList);
            ekbUpdateRoleUserInfoReq.setStaffs(entry.getValue());
            ekbUpdateRoleUserInfoReqs.add(ekbUpdateRoleUserInfoReq);
        });
        ekbBatchUpdateRoleUserInfoReq.setContents(ekbUpdateRoleUserInfoReqs);
        System.out.println(ekbBatchUpdateRoleUserInfoReq);
    }

    @Test
    public void deptCodeByCal(){
        Integer tenantId = Tenant.QMQB.getValue();
        TenantContextHolder.setTenantId(tenantId);
        MdcUtil.setTrace();
        MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "部门列表");
        for (int i = 1; i <=MAX_DEPT_LEVEL ; i++) {
            LambdaQueryWrapper<DingDept> wrapper = Wrappers.lambdaQuery(DingDept.class)
                    .eq(DingDept::getCompanyId, tenantId).eq(DingDept::getDeptLevel, i);
            List<DingDept> deptList = dingDeptService.list(wrapper);
            if(deptList.isEmpty()){
                continue;
            }

            for (DingDept p : deptList) {
                log.info("查询{}的子部门",p.getDeptName());
                LambdaQueryWrapper<DingDept> cWrapper = Wrappers.lambdaQuery(DingDept.class)
                        .eq(DingDept::getParentId,p.getDeptId())
                        .eq(DingDept::getCompanyId, tenantId)
                        .eq(DingDept::getDeptLevel, i+1);
                List<DingDept> cDeptList = dingDeptService.list(cWrapper);
                int order = 1;
                for (DingDept c : cDeptList) {
                    DingDept update = new DingDept();
                    update.setId(c.getId());
                    update.setDeptCode(getDeptCode(p.getDeptCode(),order));
                    update.setDeptChainName(p.getDeptChainName() + "-" + c.getDeptName());
                    dingDeptService.updateById(update);
                    order++;
                }
            }
        }
    }


    @Test
    public void pullUsersFromEkb4UpdateEkbUserId(){
        int count = 100;
        for (int i = 1; i <= NUM_7; i++) {
            int start = i * count;
            syncEkbService.pullUsersFromEkb4UpdateEkbUserId(start, count);
        }
    }

    @Test
    public void pushUserToEkb4BatchUpdate() throws Exception{
        int count = 1;
        for (int i = 1; i <= 1; i++) {
            int start = i * count;
            syncEkbService.pushUserToEkb4BatchUpdate(start, count);
        }
    }

    @Test
    public void pushUserToEkb4BatchAdd(){
        int count = 1;
        for (int i = 1; i <= 1; i++) {
            int start = i * count;
            try {
                syncEkbService.pushUserToEkb4BatchAdd(start, count);
            } catch (Exception e){
                log.error("出现异常",e);
            }

        }
    }

    @Test
    public void updateRoleUserInfo(){
        ekbUserService.updateRoleUserInfo();
    }

    @Autowired
    private H5AppDingClientService h5AppDingClientService;

    @Autowired
    private EkbLoginService ekbLoginService;
    @Test
    public void getAccessToken() throws Exception {
        // 获取accessToken
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, "0"));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(),dingAppConf.getDingAppSecret());
        log.info("token:{}",accessToken);
        OapiV2DepartmentGetResponse.DeptGetResponse deptDetail = getDeptDetail(121705044L, accessToken);


    }
    public OapiV2DepartmentGetResponse.DeptGetResponse getDeptDetail(Long deptId, String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(deptId);
        req.setLanguage("zh_CN");
        OapiV2DepartmentGetResponse resp = client.execute(req, accessToken);
        if (!resp.isSuccess()) {
            log.error("获取部门详情失败，部门id：{},响应code：{},响应msg:{}", deptId, resp.getErrcode(), resp.getErrmsg());
        }
        return resp.getResult();

    }

    @Autowired
    private DingUserService dingUserService;
    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Autowired
    private SyncDingDeptTask syncDingDeptTask;

    @Test
    public void testCreateDept() throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(93214609);
        jsonObject.put("deptId", jsonArray);
        dingTalkDeptService.createDept4DingAndEkb(jsonObject, 0);
    }

    @Test
    public void pullFromDing() throws Exception{

        List<OapiV2UserListResponse.ListUserResponse> responseList = dingTalkApiClient.listUser(94511409L, null);
        JSONObject obj = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (OapiV2UserListResponse.ListUserResponse userResponse : responseList) {
            jsonArray.add(userResponse.getUserid());
        }
        obj.put("userId", jsonArray);
        dingTalkUserService.modifyUser(obj,0);

    }

    @Test
    public void userLeaveList() throws Exception{
        //查询资管中心部门的所有子部门
        LambdaQueryWrapper<DingDept> wrapper = Wrappers.lambdaQuery(DingDept.class)
                .gt(DingDept::getDeptLevel, 1)
                .like(DingDept::getDeptChainName, "广州市全民钱包科技有限公司");
        List<DingDept> list = dingDeptService.list(wrapper);
        System.out.println(JSON.toJSONString(list));
        JSONObject obj = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        HashSet<String> set = new HashSet<>();
        List<String> leaveList = new ArrayList<>();
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, 0));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        for (DingDept dept : list) {
            //查询部门下的员工
            LambdaQueryWrapper<DingUser> userWrapper = Wrappers.lambdaQuery(DingUser.class)
                    .eq(DingUser::getCompanyId,0)
                    .like(DingUser::getDeptIdList,dept.getDeptId());
            List<DingUser> userList = dingUserService.list(userWrapper);
            log.info("{}部门，{},用户列表：{}",dept.getDeptName(),userList.size(),JSON.toJSONString(userList));
            for (DingUser user : userList) {
                set.add(user.getUserId());
            }


        }
        log.info("员工总数：{},去重后员工总数：{}",jsonArray.size(),set.size());
        for (String s : set) {
            OapiV2UserGetResponse.UserGetResponse userDetail = dingTalkUserService.getUserDetail(s, accessToken);
            if( userDetail == null){
                leaveList.add(s);
            } else{
                jsonArray.add(s);
            }
        }
        obj.put("userId", jsonArray);
        log.info("{}离职的用户：{}",leaveList.size(),JSON.toJSONString(leaveList));
        log.info("{}有效的用户：{}",jsonArray.size(),JSON.toJSONString(jsonArray));
//        dingTalkUserService.modifyUser(obj,0);
    }

    @Test
    public void  syncDingUserFromZiGuan() throws Exception{
        //查询资管中心部门的所有子部门
        LambdaQueryWrapper<DingDept> wrapper = Wrappers.lambdaQuery(DingDept.class)
                .gt(DingDept::getDeptLevel, 2)
                .like(DingDept::getDeptChainName, "广州市全民钱包科技有限公司-风控中心-审批部-审批五组");
        List<DingDept> list = dingDeptService.list(wrapper);
        log.info("部门列表{}",JSON.toJSONString(list));

        System.out.println(JSON.toJSONString(list));
        JSONObject obj = new JSONObject();
        JSONArray jsonArray = new JSONArray();

        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, 0));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        List<String> addList = new ArrayList<>();
        HashSet<String> dingSet = new HashSet<>();
        HashSet<DingUser> addSet = new HashSet<>();
        for (DingDept dept : list) {
            //查询部门下的员工
            OapiUserListidResponse.ListUserByDeptResponse byDeptResponse = dingTalkUserService.listDingUserByDeptId(dept.getDeptId(), accessToken);
            if(byDeptResponse == null){
                continue;
            }
            List<String> useridList = byDeptResponse.getUseridList();
            log.info("{}部门，{},用户列表：{}",dept.getDeptName(),byDeptResponse.getUseridList().size(),JSON.toJSONString(useridList));

            for (String s : useridList) {
                    dingSet.add(s);
            }
        }

        for (String s : dingSet) {
            List<DingUser> userDataList = dingUserService.list(Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getUserId, s));
            if(userDataList.isEmpty()){
                OapiV2UserGetResponse.UserGetResponse userDetail = dingTalkUserService.getUserDetail(s, accessToken);
                DingUser user = new DingUser();
                user.setUserId(s);
                user.setUserName(userDetail.getName());
                user.setCompanyId(0);
                // 部门id处理 user.setDeptIdList(dingTalkUserService.handleDeptIList(userDetail.getDeptIdList()))
                user.setMobile(userDetail.getMobile());
                user.setRemark("钉钉新增用户");
                dingUserService.save(user);
                EkbDingUserMapping userMapping = new EkbDingUserMapping();
                userMapping.setDingUserId(user.getUserId());
                userMapping.setDingUserName(user.getUserName());
                userMapping.setDingDeptIdList(user.getDeptIdList());
                userMapping.setMobile(user.getMobile());
                userMapping.setRemark("钉钉新增用户");
                ekbDingUserMappingService.save(userMapping);
                addSet.add(user);
            }

            jsonArray.add(s);
        }
        log.info("{}去重的用户：{}",dingSet.size(),JSON.toJSONString(dingSet));

        obj.put("userId", jsonArray);
        dingTalkUserService.modifyUser(obj,0);

    }

    @Test
    public void testDeptUsers() throws Exception{
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, 0));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        String userId =  "123";
        OapiV2UserGetResponse.UserGetResponse userDetail = dingTalkUserService.getUserDetail(userId, accessToken);

        log.info("新增用户列表——{}",JSON.toJSONString(userDetail));
    }

    @Test
    public void testLeave() throws Exception {
        String[] s = {"095202391537854722","01084638231627692557","083901121340285914","274908072121270473",
                "105243164521370516","202061332032830294","142941345426233331","02460834571621995262",
                "01433460433531985278","213169475724144670","02454716595430717283","02231849366637892608",
                "400556366532604344","01144263416020387444","156714653523252308","145959653436501861",
                "02214425325740013403","221829231320355286","230859246536327251","050910511537668927",
                "01696238472239890568","590629562131557286","030653395936902102","01103652661337900334",
                "115415230520991167","02266316052332346711","693962555724052462","051147063621642990",
                "072552244524694342","020253664437736797","01403353034539953442","02455218471040042577",
                "321817146026789908","096652621220974289","011132454161934498","01276402542726387368",
                "02481916252624056322","266847552126091315","242650553826628395","01395964466626116244",
                "0148374244441034753","270941612820932260","02470050094336601582","256956210037758767",
                "01540443111537734243","01090134693536881147","01400500663221370203","2131610667677431",
                "034249016021419940"};
        JSONObject obj = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (String s1 : s) {
            jsonArray.add(s1);
        }
        obj.put("userId", jsonArray);
        dingTalkUserService.deleteUser(obj,0);
    }

    @Test
    public void testModifyUser() throws Exception {
        String[] s = {
                "593901355326477952",
                "01072767412923514919",
                "475157673326627928",
                "593842260526652646",
                "02234103543238191180",
                "02676549633637753280",
                "513617436128436499",
                "01284510022720810941",
                "02676553012321507880"};
        JSONObject obj = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (String s1 : s) {
            jsonArray.add(s1);
        }
        obj.put("userId", jsonArray);
        dingTalkUserService.modifyUser(obj,3);
    }

    @Test
    public void updateRole(){
        ekbUserService.updateRoleUserInfo();
    }


    @Test
    public void testSyncDingDept() throws Exception{
        syncDingDeptTask.syncAddDingDept(0);
    }
    @Test
    public void testSyncDingUser() throws Exception{
        syncDingUserInfoTask.syncDingUser(3,6);
    }

    @Test
    public void testSyncLeaveUser() throws Exception{
        syncDingUserInfoTask.syncLeaveUsersToEkb();
    }
    @Test
    public void test(){
        String deptId = "1";
        String companyIdList = "3,5";
        LambdaQueryWrapper<EkbDingDeptMapping> ekbDingDeptMappingWrapper = Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                .in(EkbDingDeptMapping::getCompanyId, Arrays.stream(companyIdList.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()))
                ;
        List<EkbDingDeptMapping> list = ekbDingDeptMappingService.list(ekbDingDeptMappingWrapper);
        System.out.println(JSON.toJSONString(list));
    }
}
