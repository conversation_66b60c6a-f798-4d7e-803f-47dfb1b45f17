package com.qmqb.oa.system.domain;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qmqb.oa.common.annotation.Excel;
import com.qmqb.oa.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 钉钉打卡结果对象 t_dingtalk_clock_result
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_dingtalk_clock_result")
public class DingTalkClockResult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识ID
     */
    private Long id;

    /**
     * 数据来源：ATM：考勤机打卡（指纹/人脸打卡）BEACON：IBeaconDING_ATM：钉钉考勤机（考勤机蓝牙打卡）USER：用户打卡BOSS：老板改签APPROVE：审批系统SYSTEM：考勤系统AUTO_CHECK：自动打卡
     */
    private String sourceType;

    /**
     * 计算迟到和早退，基准时间。
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @Excel(name = "计算迟到和早退，基准时间。", width = 30, dateFormat = "yyyy-MM-dd")
    private Date baseCheckTime;

    /**
     * 实际打卡时间, 用户打卡时间的毫秒数。
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @Excel(name = "实际打卡时间, 用户打卡时间的毫秒数。", width = 30, dateFormat = "yyyy-MM-dd")
    private Date userCheckTime;

    /**
     * 关联的审批ID，当该字段非空时，表示打卡记录与请假、加班等审批有关。
     */
    @Excel(name = "关联的审批ID，当该字段非空时，表示打卡记录与请假、加班等审批有关。")
    private String procInstId;

    /**
     * 位置结果：Normal：范围内Outside：范围外NotSigned：未打卡
     */
    @Excel(name = "位置结果：Normal：范围内Outside：范围外NotSigned：未打卡")
    private String locationResult;

    /**
     * 打卡结果：Normal：正常Early：早退Late：迟到SeriousLate：严重迟到Absenteeism：旷工迟到NotSigned：未打卡
     */
    @Excel(name = "打卡结果：Normal：正常Early：早退Late：迟到SeriousLate：严重迟到Absenteeism：旷工迟到NotSigned：未打卡")
    private String timeResult;

    /**
     * 考勤类型：OnDuty：上班OffDuty：下班
     */
    @Excel(name = "考勤类型：OnDuty：上班OffDuty：下班")
    private String checkType;

    /**
     * 打卡人的UserID。
     */
    @Excel(name = "打卡人的UserID。")
    private String userId;

    /**
     * 打卡人名
     */
    @Excel(name = "打卡人名")
    private String userName;

    /**
     * 工作日。
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @Excel(name = "工作日。", width = 30, dateFormat = "yyyy-MM-dd")
    private Date workDate;

    /**
     * 打卡记录ID。
     */
    private Long recordId;

    /**
     * 排班ID。
     */
    private Long planId;

    /**
     * 考勤组ID。
     */
    private Long groupId;

    /**
     * 下班打卡时间是否“20:00”后：0-否，1-是
     */
    @TableField(value = "is_after_20")
    @Excel(name = "下班打卡时间是否“20:00”后：0-否，1-是")
    private Integer isAfter20;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    private Integer isDeleted;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    @Excel(name = "主体", dictType = "finance_tenant_id")
    private Integer tenantId;

    /**
     * 工作日
     */
    @TableField(exist = false)
    private String[] workDateRange;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("sourceType", getSourceType())
                .append("baseCheckTime", getBaseCheckTime())
                .append("userCheckTime", getUserCheckTime())
                .append("procInstId", getProcInstId())
                .append("locationResult", getLocationResult())
                .append("timeResult", getTimeResult())
                .append("checkType", getCheckType())
                .append("userId", getUserId())
                .append("workDate", getWorkDate())
                .append("recordId", getRecordId())
                .append("planId", getPlanId())
                .append("groupId", getGroupId())
                .append("isAfter20", getIsAfter20())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("isDeleted", getIsDeleted())
                .append("tenantId", getTenantId())
                .toString();
    }
}
