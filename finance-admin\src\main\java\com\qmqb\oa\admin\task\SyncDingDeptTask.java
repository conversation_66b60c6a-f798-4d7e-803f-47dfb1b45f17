package com.qmqb.oa.admin.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.qmqb.oa.admin.service.hsfk.DingTalkDeptService;
import com.qmqb.oa.admin.service.hsfk.H5AppDingClientService;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.DingDept;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.DingDeptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import shade.com.alibaba.fastjson2.JSONObject;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步钉钉部门
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Slf4j
@Component("syncDingDeptTask")
@RequiredArgsConstructor
public class SyncDingDeptTask {
    @Resource
    private DingAppConfService dingAppConfService;
    @Resource
    private H5AppDingClientService h5AppDingClientService;
    @Resource
    private DingDeptService dingDeptService;
    @Resource
    private DingTalkDeptService dingTalkDeptService;

    /**
     * 同步新增钉钉部门
     *
     * @throws Exception
     */
    public void syncAddDingDept(Integer companyIdParam) throws Exception {
        List<DingAppConf> appConfList = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class)
                .eq(DingAppConf::getAppSwitch, 1)
                .in(DingAppConf::getCompanyId, companyIdParam)
        );
        for (DingAppConf dingAppConf : appConfList) {
            Integer companyId = dingAppConf.getCompanyId();
            //查询当前主体下的所有子部门
            String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
            //查询当前主体下的最大部门层级 dingDeptService.getMaxChildDeptLevel(null, companyId)
            Integer maxLevel = 6;
            for (int level = 1; level <= maxLevel; level++) {
                List<DingDept> deptList = dingDeptService.list(Wrappers.lambdaQuery(DingDept.class).eq(DingDept::getCompanyId, companyId).eq(DingDept::getDeptLevel, level));
                deptList.forEach(dept -> dealChildDept(dept, companyId, accessToken));
            }
        }

    }
    /**
     * 处理子部门信息
     * 该方法用于递归处理钉钉中的子部门，将其与系统中的部门进行对比，并在系统中不存在时添加
     *
     * @param parent 钉钉部门对象，代表当前处理的父部门
     * @param companyId 公司ID，用于在系统中区分不同的公司
     * @param accessToken 钉钉API访问令牌，用于调用钉钉API
     */
    private void dealChildDept(DingDept parent, Integer companyId, String accessToken) {
        Long parentId = parent.getDeptId();
        String deptChainName = parent.getDeptChainName();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setLanguage("zh_CN");
        req.setDeptId(parentId);
        try {
            OapiV2DepartmentListsubResponse rsp = client.execute(req, accessToken);
            log.info("部门:{},子部门列表{}", deptChainName, JSON.toJSONString(rsp));
            if (rsp.isSuccess() && !rsp.getResult().isEmpty()) {
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> result = rsp.getResult();
                JSONObject bizData = new JSONObject();
                List<Long> deptIdList = new ArrayList<>();
                List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptList = new ArrayList<>();
                for (OapiV2DepartmentListsubResponse.DeptBaseResponse r : result) {
                    DingDept dt = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class).eq(DingDept::getCompanyId, companyId).eq(DingDept::getDeptId, r.getDeptId()));
                    if (dt == null) {
                        log.info("父部门:{},本部门:{},做新增处理", deptChainName, JSON.toJSONString(r));
                        deptIdList.add(r.getDeptId());
                        deptList.add(r);
                    }
                }
                if (!deptIdList.isEmpty()) {
                    bizData.put("deptId", deptIdList);
                    log.info("父部门:{},新增子部门列表:{}", deptChainName, JSON.toJSONString(deptList));

                    dingTalkDeptService.createDept4DingAndEkb(bizData, companyId);
                }
            }

        } catch (Exception e) {
            log.error("父部门:{},同步子部门出现异常", deptChainName, e);
        }

    }
}
