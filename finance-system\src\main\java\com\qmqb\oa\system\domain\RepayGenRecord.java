package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 还款信息生成记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_repay_gen_record")
public class RepayGenRecord implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 主键id
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 程序编号
     */
      private Integer programId;

      /**
     * 批次号
     */
      private String batchNo;

      /**
     * 开始日期
     */
      private Date startDate;

      /**
     * 结束日期
     */
      private Date endDate;

      /**
     * 状态0待执行，1已执行，2已生成
     */
      private Integer status;

      /**
     * 创建人
     */
      private String creator;

      /**
     * 更新人
     */
      private String updater;

      /**
     * 完成时间
     */
      private Date finishTime;

      /**
     * 记录创建时间
     */
      @TableField("dbCreateDt")
    private Date dbcreatedt;

      /**
     * 记录更新时间
     */
      @TableField("dbUpdateDt")
    private Date dbupdatedt;

    /**
     * 数据类型:1-资金资产还款, 2-履约保证金退款
     */
    private Integer dataType;


}
