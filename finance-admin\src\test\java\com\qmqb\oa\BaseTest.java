package com.qmqb.oa;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.qmqb.oa.admin.domain.OvertimeExcel;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@SpringBootTest(classes = AdminApplication.class)
@RunWith(SpringRunner.class)
public class BaseTest {

    public static void main(String[] args) throws Exception {
//        ArrayList<OvertimeExcel> objects = new ArrayList<>();
//        EasyExcel.read("C:\\Users\\<USER>\\Desktop\\2024年3月技术中心全民公司报销收集.xlsx", new ReadListener<OvertimeExcel>() {
//
//            /**
//             * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
//             * entire read will terminate.
//             *
//             * @param exception
//             * @param context
//             * @throws Exception
//             */
//            @Override
//            public void onException(Exception exception, AnalysisContext context) throws Exception {
//
//            }
//
//            /**
//             * When analysis one head row trigger invoke function.
//             *
//             * @param headMap
//             * @param context
//             */
//            @Override
//            public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
//
//            }
//
//            /**
//             * When analysis one row trigger invoke function.
//             *
//             * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
//             * @param context analysis context
//             */
//            @Override
//            public void invoke(OvertimeExcel data, AnalysisContext context) {
//                objects.add(data);
//            }
//
//            /**
//             * The current method is called when extra information is returned
//             *
//             * @param extra   extra information
//             * @param context analysis context
//             */
//            @Override
//            public void extra(CellExtra extra, AnalysisContext context) {
//
//            }
//
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext context) {
//                saveData();
//            }
//
//            /**
//             * Verify that there is another piece of data.You can stop the read by returning false
//             *
//             * @param context
//             * @return
//             */
//            @Override
//            public boolean hasNext(AnalysisContext context) {
//                return true;
//            }
//
//            /**
//             * 加上存储数据库
//             */
//            private void saveData() {
//
//            }
//        }).sheet(0).headRowNumber(2).doRead();
////        read.sheet(0);
////        read.headRowNumber(2);
////        read.doReadAll();
////        List<Object> objects = read.doReadAllSync();
//        System.out.println("objects = " + objects);
//        ExcelUtil<OvertimeExcel> util = new ExcelUtil<>(OvertimeExcel.class);

        List<OvertimeExcel> overtimeExcels =  EasyExcel.read(FileUtil.getInputStream("C:\\Users\\<USER>\\Desktop\\2024年3月技术中心全民公司报销收集.xlsx"))
                .head(OvertimeExcel.class)
                .sheet(0)
                .headRowNumber(3)
                .doReadSync();
        System.out.println("overtimeExcels = " + overtimeExcels);

    }
}
