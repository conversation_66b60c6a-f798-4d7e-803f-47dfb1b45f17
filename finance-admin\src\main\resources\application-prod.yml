# 项目相关配置
ruoyi:
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /www/wwwfiles/finance-admin
  # 获取ip地址开关
  addressEnabled: false

# 生产环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9701

# Spring配置
spring:
  # redis 配置
  redis:
    # 地址
    host: hezhong88.redis.rds.aliyuncs.com
    # 端口，默认为6379
    port: 4467
    # 数据库索引
    database: 32
    # 密码
    password: p5SjpHRk8RdDGpkT
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池的最大数据库连接数
        max-active: 20
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 10000ms
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ******************************************************************************************************************************************************************************
        username: u_finance_auto
        password: Mskeu8aoIpjshga27hzbrpw9
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      mysql:
        usePingMethod: false

#财务自动化系统域名
system:
  domain: https://finance.qmqb.top

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false

# pub.oss
pub:
  oss:
    ssl: false
    enabled: true
    bucket-name: qm-finance
    access-key: LTAI5tB9Da8fkkA3eWexxJA3
    secret-key: ******************************
    endpoint: oss-cn-shenzhen-internal.aliyuncs.com
    access-endpoint: oss-cn-shenzhen.aliyuncs.com
    base-dir: prod

sftp:
  host: sftp-s2.qmqb.top
  port: 22
  username: finance-auto
  password: as2dfRdf78Q3FO

#接入监控中心
qmwallet:
  monitor:
    center:
      url: https://monitor.qmqb.top/monitor-api