package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.config.EkbConfig;
import com.qmqb.oa.admin.constant.EkbConstants;
import com.qmqb.oa.admin.domain.request.internal.EkbProvisionalAuthRequest;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAccessTokenRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbProvisionalAuthReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbProvisionalAuthRsp;
import com.qmqb.oa.common.exception.ServiceException;
import com.qmqb.oa.common.utils.http.HttpRestService;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.service.DingAppConfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Objects;

/**
 * 易快报登录相关接口
 * <AUTHOR>
 * @date 2024/7/27
 */
@Slf4j
@Service
public class EkbLoginService {
    @Autowired
    private EkbConfig ekbConfig;
    @Autowired
    private HttpRestService httpRestService;
    @Autowired
    private DingAppConfService dingAppConfService;

    @Autowired
    private EkbLoginService ekbLoginService;

    /**
     * 获取授权
     *
     * @param appKey
     * @param appSecurity
     * @return
     */
    public EkbAccessTokenRsp getAccessToken(String appKey, String appSecurity) {
        JSONObject param = new JSONObject();
        param.put("appKey", appKey);
        param.put("appSecurity", appSecurity);
        HashMap<String, JSONObject> result = httpRestService.post(ekbConfig.getAccessTokenUrl(),param.toJSONString(),  HashMap.class, "【获取易快报授权】");
        JSONObject data = result.get("value");
        if (Objects.nonNull(data.get("errorCode"))) {
            throw new ServiceException(data.get("errorMessage").toString());
        }
        return data.toJavaObject(EkbAccessTokenRsp.class);
    }

    /**
     * 访问临时授权
     * @param authRequest
     * @return
     */
    public EkbProvisionalAuthRsp getProvisionalAuth(EkbProvisionalAuthRequest authRequest) throws Exception {

        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getDingCorpId, authRequest.getCorpId()));

        EkbAccessTokenRsp ekbAccessTokenRsp = ekbLoginService.getAccessToken(dingAppConf.getPartnerAppKey(), dingAppConf.getPartnerAppSecret());
        log.info("易快报accessToken:{}", ekbAccessTokenRsp);


        String url = ekbConfig.getProvisionalAuthUrl() + ekbAccessTokenRsp.getAccessToken();
        EkbProvisionalAuthReq req = new EkbProvisionalAuthReq();
        req.setUid(authRequest.getEkbUserId());
        req.setPageType(authRequest.getPageType());
        req.setExpireDate(EkbConstants.EXPIRE_DATE);
        if(1 == authRequest.getClient()){
            req.setIsApplet(true);
        }
        if (StringUtil.equals(EkbConstants.PAGE_TYPE_FORM, authRequest.getPageType()) && StringUtil.isBlank(authRequest.getFlowId())) {
            throw new ServiceException("form 流程id不能为空");
        }
        if (StringUtil.isNotBlank(authRequest.getFlowId())) {
            req.setFlowId(authRequest.getFlowId());
        }
        String result = httpRestService.post(url, JSON.toJSONString(req), String.class, "【访问临时授权】");
        if (!result.contains(result)) {
            throw new ServiceException(result);
        }
        EkbProvisionalAuthRsp rsp = JSON.parseObject(result).getObject("value", EkbProvisionalAuthRsp.class);

        return rsp;
    }
}
