package com.qmqb.oa.admin.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <p>
 * 发票代码解析
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Data
public class InvoiceCodeParse {
    @JSONField(name = "批次号")
    private String batchNumber;
    @JSONField(name = "年份")
    private String year;
    @JSONField(name = "税务局代码")
    private String taxCode;
    @JSONField(name = "发票行业代码")
    private String invoiceIndustryCode;
    @JSONField(name = "金额版")
    private String amountVersion;
    @JSONField(name = "行政区划代码")
    private String areaCode;
    @JSONField(name = "发票类别代码")
    private String invoiceCategoryCode;
}
