package com.qmqb.oa.admin.controller.hsfk;

import com.alibaba.fastjson.JSON;
import com.qmqb.oa.admin.domain.request.internal.EkbProvisionalAuthRequest;
import com.qmqb.oa.admin.service.hsfk.HeSiFeiKongService;
import com.qmqb.oa.common.core.controller.BaseController;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Slf4j
@Controller
@RequestMapping("/ding")
public class DingUrlController extends BaseController {
    @Autowired
    private HeSiFeiKongService heSiFeiKongService;

    @ApiOperation("重定向跳转易快报的url")
    @GetMapping("/redirectEkbUrl")
    public String redirectEkbUrl(String flowId,String ekbUserId,String dingCorpId) throws Exception{
        EkbProvisionalAuthRequest request = new EkbProvisionalAuthRequest();
        request.setCorpId(dingCorpId);
        request.setEkbUserId(ekbUserId);
        //只用app
        request.setClient(1);
        request.setFlowId(flowId);
        request.setPageType("form");
        log.info("redirectEkbUrl request:{}", JSON.toJSONString(request));
        String url = heSiFeiKongService.getFormProvisionalauthurl(request);

        return redirect(url);
    }
}
