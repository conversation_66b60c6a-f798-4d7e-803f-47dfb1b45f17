package com.qmqb.oa.admin.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 还款生成记录VO
 */
@Data
public class RepayGenRecordVo {
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    String batchNo;
    /**
     * 批次号生成时间
     */
    @ApiModelProperty("批次号生成时间")
    String batchNoCreateDt;
    /**
     * 启示日期
     */
    @ApiModelProperty("启示日期")
    String startDate;
    /**
     * 截止日期
     */
    @ApiModelProperty("截止日期")
    String endDate;
    /**
     * 状态 @see com\qmqb\finance\common\enums\RepaymentGenStatusEnums.java
     */
    @ApiModelProperty("状态")
    Integer status;
    /**
     * 数据类型
     */
    @ApiModelProperty("数据类型")
    Integer dataType;
}
