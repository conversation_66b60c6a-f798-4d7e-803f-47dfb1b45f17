package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钉钉与易快报员工映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_ekb_ding_user_mapping")
public class EkbDingUserMapping implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 主键id
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 钉钉user_id
     */
      private String dingUserId;

      /**
     * 钉钉员工名称
     */
      private String dingUserName;

      /**
     * 钉钉部门列表,部门id用,分隔
     */
      private String dingDeptIdList;

      /**
     * 手机号
     */
      private String mobile;

      /**
     * 易快报user_id
     */
      private String ekbUserId;

      /**
     * 易快报用户姓名
     */
      private String ekbUserName;

      /**
     * 易快报默认部门ID
     */
      private String ekbDefaultDeptId;

      /**
     * 易快报部门列表
     */
      private String ekbDeptIdList;

      /**
     * 备注
     */
      private String remark;

      /**
     * 创建时间
     */
      private LocalDateTime createTime;

      /**
     * 更新时间
     */
      private LocalDateTime updateTime;


}
