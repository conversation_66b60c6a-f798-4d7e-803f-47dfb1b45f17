package com.qmqb.oa.admin.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.system.domain.DingTalkDept;
import com.qmqb.oa.system.service.DingTalkDeptService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

public class SyncDingTalkAddressBookTaskTest extends BaseTest {

    @Autowired
    SyncDingTalkAddressBookTask task;
    @Autowired
    DingTalkDeptService dingTalkDeptService;

    @Test
    public void syncRole() {
        task.syncRole();
    }

    @Test
    public void syncDept() {
        task.syncDept();
    }

    @Test
    public void deptTree() {
        List<DingTalkDept> depts = dingTalkDeptService.list(Wrappers.lambdaQuery(DingTalkDept.class)
                .eq(DingTalkDept::getTenantId, 0));
        List<TreeNode<Long>> nodes = depts.stream().map(dept -> {
            TreeNode<Long> node = new TreeNode<>();
            node.setId(dept.getDeptId());
            node.setParentId(dept.getParentId());
            node.setName(dept.getDeptName());
            node.setWeight(dept.getDeptLevel());
            node.setExtra(BeanUtil.beanToMap(dept));
            return node;
        }).collect(Collectors.toList());
        List<Tree<Long>> build = TreeUtil.build(nodes, 1L, (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setParentId(treeNode.getParentId());
            tree.setWeight(0);
            tree.setName(treeNode.getName());
        });
        System.out.println(JacksonUtil.toJson(build));
    }

    @Test
    public void syncUser() {
        task.syncUser();
    }
}
