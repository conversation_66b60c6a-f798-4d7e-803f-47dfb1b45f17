package com.qmqb.oa.admin.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.dingtalkcontact_1_0.models.ListEmpLeaveRecordsResponseBody;
import com.aliyun.dingtalkhrm_1_0.models.QueryHrmEmployeeDismissionInfoResponseBody;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.qmqb.oa.admin.api.client.DingTalkApiClient;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.config.DingTalkConfig;
import com.qmqb.oa.admin.service.ApproveTimeService;
import com.qmqb.oa.admin.support.FinancialRobotWatch;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.constant.*;
import com.qmqb.oa.common.core.domain.entity.SysDictData;
import com.qmqb.oa.common.core.redis.RedisCache;
import com.qmqb.oa.common.enums.DictType;
import com.qmqb.oa.common.enums.ProcessType;
import com.qmqb.oa.common.enums.RedisCacheKey;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.DimissionEmployee;
import com.qmqb.oa.system.domain.EmployeeSubject;
import com.qmqb.oa.system.domain.ProcessRecord;
import com.qmqb.oa.system.service.DimissionEmployeeService;
import com.qmqb.oa.system.service.EmployeeSubjectService;
import com.qmqb.oa.system.service.ProcessRecordService;
import com.qmqb.oa.system.service.SysDictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 入职审批
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Slf4j
@Component("entryApprovalTask")
@RequiredArgsConstructor
public class EntryApprovalTask {

    private final DingTalkConfig dingTalkConfig;
    private final DingTalkApiClient dingTalkApiClient;
    private final DingTalkOApiClient dingTalkOApiClient;
    private final DimissionEmployeeService dimissionEmployeeService;
    private final FinancialRobotWatch financialRobotWatch;
    private final ApproveTimeService approveTimeService;
    private final RedisCache redisCache;
    private final ProcessRecordService processRecordService;
    private final SysDictTypeService sysDictTypeService;
    private final EmployeeSubjectService employeeSubjectService;


    /**
     * 机器人审批
     */
    public void entry(Integer tenantId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "入职审批");
            // 查询审批实例ID列表
            Map<String, Long> time = approveTimeService.approveListTime();
            Long beginTime = time.get(ApproveTimeService.BEGIN_TIME);
            Long endTime = time.get(ApproveTimeService.END_TIME);
            String key = RedisCacheKey.ENTRY_APPROVAL_LAST_QUERY_TIME.getKey(tenantId);
            Long lastQueryTime = redisCache.getCacheObject(key);
            if (Objects.nonNull(lastQueryTime)) {
                beginTime = lastQueryTime;
            }
            log.info("查询的开始时间：{}, 结束时间：{}", beginTime, endTime);
            List<String> processInstanceIds = dingTalkOApiClient.listProcessInstanceIds(dingTalkConfig.getEntryApproval(tenantId), null, beginTime, endTime);
            if (CollectionUtil.isEmpty(processInstanceIds)) {
                redisCache.setCacheObject(key, endTime);
                log.info("获取入职审批实例ID列表为空,不执行此次任务");
                return;
            }
            log.info("本次查询入职审批个数:{}", processInstanceIds.size());
            // 执行审批实例操作
            processInstanceIds.forEach(processInstanceId -> {
                try {
                    executeProcess(processInstanceId);
                } catch (Exception e) {
                    // 钉钉预警
                    financialRobotWatch.warn(processInstanceId, e);
                    log.error("执行审批实例操作异常", e);
                }
            });
            redisCache.setCacheObject(key, endTime);
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }

    /**
     * 审批实例列表操作
     *
     * @param processInstanceId
     */
    public void executeProcess(String processInstanceId) {
        // 遍历实例列表
        MdcUtil.putProcess(processInstanceId);
        ProcessRecord processRecord = processRecordService.getByProcessInstanceId(processInstanceId);
        if (Objects.nonNull(processRecord)) {
            log.info("该流程已处理过");
            return;
        }
        // 查询审批实例详情
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = dingTalkOApiClient.getProcessInstance(processInstanceId);
        // 审批实例业务编号
        String businessId = processInstance.getBusinessId();
        MdcUtil.putBizId(businessId);
        // 查询当前任务id
        Optional<OapiProcessinstanceGetResponse.TaskTopVo> taskOptional = processInstance.getTasks().stream()
                .filter(task -> ProcessStatus.RUNNING.equals(task.getTaskStatus()) && Approver.ROBOT_IDS.contains(task.getUserid())).findFirst();
        if (!taskOptional.isPresent()) {
            log.info("当前流程非机器人处理中状态");
            return;
        }

        log.info("开始处理审批流程,审批实例ID:{},审批编号:{}", processInstanceId, businessId);

        Long taskId = Long.valueOf(taskOptional.get().getTaskid());
        String userId = taskOptional.get().getUserid();

        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();
        String entryName = formComponentValues.stream()
                .filter(form -> TableFieldId.TEXT_FIELD.equals(form.getComponentType()))
                .filter(form -> "待入职姓名".equals(form.getName()))
                .map(OapiProcessinstanceGetResponse.FormComponentValueVo::getValue)
                .findFirst().orElse(null);
        if (StrUtil.isBlank(entryName)) {
            log.info("获取待入职姓名为空");
            commonRefuse(processInstanceId, userId, taskId, "获取待入职姓名为空，请核实");
            saveProcessRecord(processInstance, processInstanceId, "获取待入职姓名为空，请核实", BoolFlagEnum.NO);
            return;
        }
        String entryCompany = formComponentValues.stream()
//                .filter(form -> TableFieldId.TABLE_SELECT_FIELD.equals(form.getComponentType()))
                .filter(form -> "待入职公司主体".equals(form.getName()))
                .map(OapiProcessinstanceGetResponse.FormComponentValueVo::getValue)
                .findFirst().orElse(null);
        if (StrUtil.isBlank(entryCompany)) {
            log.info("待入职公司主体为空");
            commonRefuse(processInstanceId, userId, taskId, "待入职公司主体为空，请核实");
            saveProcessRecord(processInstance, processInstanceId, "待入职公司主体为空，请核实", BoolFlagEnum.NO);
            return;
        }
        // 2、现阶段公司暂不招收姓氏含有“敬”、“苟”的入职人员。
        boolean rejectBySurname = false;
        String surname = surname();
        for (String name : StrUtil.split(surname, StringPool.COMMA)) {
            if (StrUtil.startWith(entryName, name)) {
                rejectBySurname = true;
                break;
            }
        }
        if (rejectBySurname) {
            log.info("包含暂不招收姓氏：{}==>{}", entryName, surname);
            commonRefuse(processInstanceId, userId, taskId, "入职人员姓名包含暂不招收姓氏【" + surname + "】");
            saveProcessRecord(processInstance, processInstanceId, "入职人员姓名包含暂不招收姓氏【" + surname + "】", BoolFlagEnum.NO);
            return;
        }
        // 1、现阶段公司不允许离职员工再次入职。
        List<DimissionEmployee> employees = dimissionEmployeeService.list(Wrappers.lambdaQuery(DimissionEmployee.class)
                .like(DimissionEmployee::getName, entryName));
        if (CollUtil.isNotEmpty(employees)) {
            List<String> names = employees.stream().map(DimissionEmployee::getName).collect(Collectors.toList());
            boolean match = false;
            for (String name : names) {
                String afterFilter = ReUtil.replaceAll(name, PatternPool.NUMBERS, StringPool.EMPTY);
                if (StrUtil.equals(entryName, afterFilter)) {
                    match = true;
                    break;
                }
            }
            if (match) {
                String namesStr = StrUtil.join(StringPool.COMMA, names);
                log.info("存在重名：{}==>{}", entryName, names);
                commonRefuse(processInstanceId, userId, taskId, "入职人员姓名与已离职人员名录比对，存在重名【" + namesStr + "】，请核实");
                saveProcessRecord(processInstance, processInstanceId, "入职人员姓名与已离职人员名录比对，存在重名【" + namesStr + "】，请核实", BoolFlagEnum.NO);
                return;
            }
        }
        commonAgree(processInstanceId, userId, taskId, "校验通过");
        saveProcessRecord(processInstance, processInstanceId, "校验通过", BoolFlagEnum.YES);

        EmployeeSubject subject = new EmployeeSubject();
        subject.setName(entryName);
        subject.setRawCompany(entryCompany);
        subject.setCompany(Tenant.findByShortName(entryCompany).getValue());
        employeeSubjectService.save(subject);
    }

    /**
     * 保存流程处理记录
     *
     * @param processInstance
     * @param processInstanceId
     * @param commentResult
     * @param isPass
     */
    private boolean saveProcessRecord(OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance,
                                      String processInstanceId,
                                      String commentResult,
                                      BoolFlagEnum isPass) {
        ProcessRecord processRecord = BeanUtil.copyProperties(processInstance, ProcessRecord.class);
        processRecord.setProcessInstanceId(processInstanceId);
        OapiV2UserGetResponse.UserGetResponse user = dingTalkOApiClient.getUser(processInstance.getOriginatorUserid());
        processRecord.setOriginatorUserId(processInstance.getOriginatorUserid());
        processRecord.setOriginatorUserName(Objects.nonNull(user) ? user.getName() : StringPool.EMPTY);
        processRecord.setIsPass(isPass.getStatus());
        processRecord.setResult(commentResult);
        processRecord.setProcessType(ProcessType.ENTRY_APPROVAL.getValue());
        processRecord.setTenantId(TenantContextHolder.getTenantId());
        return processRecordService.save(processRecord);
    }


    /**
     * 姓氏
     *
     * @return
     */
    private String surname() {
        return sysDictTypeService.selectDictDataByType(DictType.SURNAME.getType())
                .stream()
                .map(SysDictData::getDictValue)
                .findFirst()
                .orElse("敬,苟");
    }


    /**
     * 审批拒绝
     *
     * @param processInstanceId
     * @param taskId
     * @param remark
     */
    private void commonRefuse(String processInstanceId, String userId, Long taskId, String remark) {
        Integer tenantId = TenantContextHolder.getTenantId();
        if (Objects.equals(tenantId, Tenant.FHTX.getValue()) || Objects.equals(tenantId, Tenant.ZJ.getValue())) {
            dingTalkApiClient.executeProcessInstance(processInstanceId, userId, null, null, remark, ApprovalOpt.REFUSE, taskId);
            return;
        }
        dingTalkOApiClient.executeTask(processInstanceId, userId, taskId, remark, ApprovalOpt.REFUSE, null);
    }

    /**
     * 审批通过
     *
     * @param processInstanceId
     * @param taskId
     * @param remark
     */
    private void commonAgree(String processInstanceId, String userId, Long taskId, String remark) {
        Integer tenantId = TenantContextHolder.getTenantId();
        if (Objects.equals(tenantId, Tenant.FHTX.getValue()) || Objects.equals(tenantId, Tenant.ZJ.getValue())) {
            dingTalkApiClient.executeProcessInstance(processInstanceId, userId, null, null, remark, ApprovalOpt.AGREE, taskId);
            return;
        }
        dingTalkOApiClient.executeTask(processInstanceId, userId, taskId, remark, ApprovalOpt.AGREE, null);
    }


    /**
     * 同步离职员工
     */
    public void syncDimissionEmployee() {
        for (Integer tenantId : Arrays.asList(Tenant.QMQB.getValue(), Tenant.FSBYL.getValue(), Tenant.OAOP.getValue(), Tenant.FHTX.getValue())) {
            syncAndSave(tenantId);
        }
    }

    private void syncAndSave(Integer tenantId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "同步离职员工");
            DateTime now = DateTime.now();
            DateTime start = DateUtil.beginOfMonth(now);
            DateTime end = DateUtil.endOfMonth(now);
            List<ListEmpLeaveRecordsResponseBody.ListEmpLeaveRecordsResponseBodyRecords> records = dingTalkApiClient.listEmpLeaveRecordsWithOptions(start, end);
            if (CollUtil.isEmpty(records)) {
                return;
            }
            List<String> userIds = records.stream().map(ListEmpLeaveRecordsResponseBody.ListEmpLeaveRecordsResponseBodyRecords::getUserId).collect(Collectors.toList());
            List<QueryHrmEmployeeDismissionInfoResponseBody.QueryHrmEmployeeDismissionInfoResponseBodyResult> results = dingTalkApiClient.queryHrmEmployeeDismissionInfoWithOptions(userIds);
            List<DimissionEmployee> employees = new ArrayList<>();
            for (ListEmpLeaveRecordsResponseBody.ListEmpLeaveRecordsResponseBodyRecords record : records) {
                int count = dimissionEmployeeService.countByName(record.name);
                if (SqlHelper.retBool(count)) {
                    log.info("存在同名记录，name:{}", record.name);
                    continue;
                }
                DimissionEmployee employee = new DimissionEmployee();
                employee.setUserId(record.userId);
                employee.setName(record.name);
                employee.setStateCode(record.stateCode);
                employee.setMobile(record.mobile);
                employee.setLeaveTime(DateUtil.parse(record.leaveTime, DatePattern.UTC_PATTERN));
                employee.setLeaveReason(record.leaveReason);
                Optional<QueryHrmEmployeeDismissionInfoResponseBody.QueryHrmEmployeeDismissionInfoResponseBodyResult> optional = results.stream().filter(e -> StrUtil.equals(e.userId, record.userId)).findFirst();
                if (optional.isPresent()) {
                    QueryHrmEmployeeDismissionInfoResponseBody.QueryHrmEmployeeDismissionInfoResponseBodyResult result = optional.get();
                    if (Objects.nonNull(result.lastWorkDay)) {
                        Date lastWorkDay = new Date(result.lastWorkDay);
                        employee.setLastWorkDay(lastWorkDay);
                    }
                    employee.setReasonMemo(result.reasonMemo);
                    employee.setPreStatus(result.preStatus);
                    employee.setHandoverUserId(result.handoverUserId);
                    employee.setStatus(result.status);
                    employee.setMainDeptName(result.mainDeptName);
                    employee.setMainDeptId(result.mainDeptId);
                    if (CollUtil.isNotEmpty(result.voluntaryReason)) {
                        employee.setVoluntaryReason(StrUtil.join(StringPool.COMMA, result.voluntaryReason));
                    }
                    if (CollUtil.isNotEmpty(result.passiveReason)) {
                        employee.setPassiveReason(StrUtil.join(StringPool.COMMA, result.passiveReason));
                    }
                }
                employee.setTenantId(TenantContextHolder.getTenantId());
                employees.add(employee);
            }
            if (CollUtil.isNotEmpty(employees)) {
                dimissionEmployeeService.saveBatch(employees);
            }
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }

}
