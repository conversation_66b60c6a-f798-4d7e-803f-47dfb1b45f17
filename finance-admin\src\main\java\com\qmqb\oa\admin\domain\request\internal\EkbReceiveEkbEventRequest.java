package com.qmqb.oa.admin.domain.request.internal;


import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024\10\10 0010 11:57
 * @Version 1.0
 */

@Data
public class EkbReceiveEkbEventRequest {
    /**
     * 提交日期不能为空
     */
    private Long submitDate;

    /**
     * 用户信息
     */
    @NotNull(message = "用户信息不能为空")
    private UserInfo userInfo;

    /**
     * 表单规格
     */
    private FormSpecification formSpecification;

    /**
     * 法人实体
     */
    private Legal 法人实体;
    /**
     * 企业ID
     */
    private String corporationId;

    /**
     * 提交者信息
     */
    private SubmitterId submitterId;

    /**
     * 动作
     */
    @NotEmpty(message = "action不能为空")
    private String action;

    /**
     * 消息ID
     */
    @NotEmpty(message = "消息ID不能为空")
    private String messageId;

    /**
     * 状态
     */
    private String state;

    /**
     * 流程ID
     */
    @NotEmpty(message = "flowId 不能为空")
    private String flowId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 当前审批人
     */
    private List<CurrentApprover> currentApprovers;

    /**
     * 前置节点审批时间
     */
    private long preNodeApprovedTime;

    /**
     * 动作名称
     */
    private String actionName;

    /**
     * 费用详情
     */
    private List<FeeDetail> details;

    /**
     * 用户信息类
     */
    @Data
    public static class UserInfo {
        public String id;
        public String name;
        public String cellphone;
        public String email;
    }

    /**
     * 表单规格类
     */
    @Data
    public static class FormSpecification {
        public String specificationId;
        public String specificationName;

    }

    /**
     * 提交者信息类
     */
    @Data
    public static class SubmitterId {
        public String name;
        public String id;
    }

    @Data
    /**
     *     当前审批人类
     */
    public static class CurrentApprover {
        public String name;
        public String id;
    }


    @Data
    public static class FeeDetail {
        private String feeTypeId;
        private FeeTypeForm feeTypeForm;
        private String specificationId;
    }

    @Data
    public static class FeeTypeForm {
        private Amount amount;
        private String detailId;
        private int detailNo;
        private InvoiceForm invoiceForm;
        private String u_合同台账;
    }
    @Data
    public static class Amount {
        private String standard;
        private String standardUnit;
        private int standardScale;
        private String standardSymbol;
        private String standardNumCode;
        private String standardStrCode;
    }

    @Data
    public static class InvoiceForm {
        private String type;
        private List<Invoice> invoices;
    }

    @Data
    public static class Invoice {
        private List<String> itemIds;
        private double taxRate;
        private String invoiceId;
        private Amount taxAmount;
    }

    @Data
    public static class Legal {
        private String id;
        private String code;
        private String name;
        private String path;
    }
}
