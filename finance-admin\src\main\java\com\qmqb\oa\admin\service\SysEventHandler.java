package com.qmqb.oa.admin.service;

import com.qmqb.oa.admin.service.hsfk.DingTalkSysEventHandler;
import com.qmqb.oa.admin.service.hsfk.EkbSysEventHandler;
import com.qmqb.oa.system.domain.SysEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 系统事件处理类
 * <AUTHOR>
 * @date 2024/8/29
 */
@Slf4j
@Component
public class SysEventHandler {
    @Autowired
    private EkbSysEventHandler ekbSysEventHandler;
    @Autowired
    private DingTalkSysEventHandler dingTalkSysEventHandler;

    /**
     * 处理失败的事件
     * @param r
     */
    public void dealFailedEvent(SysEvent r) {
        if (r.getEventSource() == 1) {
            //钉钉事件
            dingTalkSysEventHandler.dealFailedEvent(r);
        } else {
            //易快报事件
            ekbSysEventHandler.dealFailedEvent(r);
        }
    }
}
