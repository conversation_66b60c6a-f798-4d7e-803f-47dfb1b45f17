package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.vo.RoleUserInfoVo;

import java.util.List;

/**
 * <p>
 * 钉钉与易快报员工映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface EkbDingUserMappingService extends IService<EkbDingUserMapping> {

    /**
     * 查询易快报所有用户Id和主体部门id
     * 更新角色下员工信息接口使用
     * @return
     */
    List<RoleUserInfoVo> selectUserIdAndCompanyDeptId();

    /**
     * 根据ekbUserId 查找ekb 用户
     * @param ekbUserId
     * @return
     */
    EkbDingUserMapping findOneByEkbUserId(String ekbUserId);

    /**
     * 根据ekbUserIds 查找ekb 用户列表
     * @param ekbUserIdList
     * @return
     */
    List<EkbDingUserMapping> findListByEkbUserIds(List<String> ekbUserIdList);

    /**
     *
     * @param dingUserId
     * @return
     */
    EkbDingUserMapping findOneByDingUserId(String dingUserId);
    
}
