package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.EkbMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 易快报消息通知记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
public interface EkbMessageService extends IService<EkbMessage> {

    /**
     * 根据流程和消息id查找用户唯一的消息记录
     * @param flowId
     * @param messageId
     * @param userId
     * @return
     */
    EkbMessage findOneByFlowIdAndMessageId(String flowId, String messageId, String userId);


    /**
     * 根据流程id 及消息ids 获取流程列表
     * @param flowId
     * @param messageIds
     * @return
     */
    List<EkbMessage> listByFlowIdAndMessageIds(String flowId, List<String> messageIds);

    /**
     * 根据流程和消息id查找用户唯一的消息记录
     * @param flowId
     * @param messageId
     * @return
     */
    EkbMessage findEkbMessageByFlowIdAndMessageId(String flowId, String messageId);
}
