<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.RepayStatJindieMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.RepayStatJindie">
        <id column="id" property="id" />
        <result column="batch_no" property="batchNo" />
        <result column="fbillhead" property="fbillhead" />
        <result column="faccountbookid" property="faccountbookid" />
        <result column="faccountbookid_name" property="faccountbookidName" />
        <result column="fdate" property="fdate" />
        <result column="fvouchergroupid" property="fvouchergroupid" />
        <result column="fvouchergroupid_name" property="fvouchergroupidName" />
        <result column="fvouchergroupno" property="fvouchergroupno" />
        <result column="faccbookorgid" property="faccbookorgid" />
        <result column="faccbookorgid_name" property="faccbookorgidName" />
        <result column="split" property="split" />
        <result column="fentity" property="fentity" />
        <result column="fexplanation" property="fexplanation" />
        <result column="faccountid" property="faccountid" />
        <result column="faccountid_name" property="faccountidName" />
        <result column="facctfullname" property="facctfullname" />
        <result column="fdetailid_fflex14" property="fdetailidFflex14" />
        <result column="fdetailid_fflex14_name" property="fdetailidFflex14Name" />
        <result column="fdetailid_fflex15" property="fdetailidFflex15" />
        <result column="fdetailid_fflex15_name" property="fdetailidFflex15Name" />
        <result column="fdetailid_fflex12" property="fdetailidFflex12" />
        <result column="fdetailid_fflex12_name" property="fdetailidFflex12Name" />
        <result column="fdetailid_fflex13" property="fdetailidFflex13" />
        <result column="fdetailid_fflex13_name" property="fdetailidFflex13Name" />
        <result column="fdetailid_ff100003" property="fdetailidFf100003" />
        <result column="fdetailid_ff100003_name" property="fdetailidFf100003Name" />
        <result column="fdetailid_ff100004" property="fdetailidFf100004" />
        <result column="fdetailid_ff100004_name" property="fdetailidFf100004Name" />
        <result column="fdetailid_fflex16" property="fdetailidFflex16" />
        <result column="fdetailid_fflex16_name" property="fdetailidFflex16Name" />
        <result column="fdetailid_ff100002" property="fdetailidFf100002" />
        <result column="fdetailid_ff100002_name" property="fdetailidFf100002Name" />
        <result column="fdetailid_fflex11" property="fdetailidFflex11" />
        <result column="fdetailid_fflex11_name" property="fdetailidFflex11Name" />
        <result column="fdetailid_fflex5" property="fdetailidFflex5" />
        <result column="fdetailid_fflex5_name" property="fdetailidFflex5Name" />
        <result column="fdetailid_fflex6" property="fdetailidFflex6" />
        <result column="fdetailid_fflex6_name" property="fdetailidFflex6Name" />
        <result column="fdetailid_fflex4" property="fdetailidFflex4" />
        <result column="fdetailid_fflex4_name" property="fdetailidFflex4Name" />
        <result column="fdetailid_fflex9" property="fdetailidFflex9" />
        <result column="fdetailid_fflex9_name" property="fdetailidFflex9Name" />
        <result column="fdetailid_fflex10" property="fdetailidFflex10" />
        <result column="fdetailid_fflex10_name" property="fdetailidFflex10Name" />
        <result column="fdetailid_fflex7" property="fdetailidFflex7" />
        <result column="fdetailid_fflex7_name" property="fdetailidFflex7Name" />
        <result column="fdetailid_fflex8" property="fdetailidFflex8" />
        <result column="fdetailid_fflex8_name" property="fdetailidFflex8Name" />
        <result column="fcurrencyid" property="fcurrencyid" />
        <result column="fcurrencyid_name" property="fcurrencyidName" />
        <result column="fexchangeratetype" property="fexchangeratetype" />
        <result column="fexchangeratetype_name" property="fexchangeratetypeName" />
        <result column="fdebit" property="fdebit" />
        <result column="fcredit" property="fcredit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_no, fbillhead, faccountbookid, faccountbookid_name, fdate, fvouchergroupid, fvouchergroupid_name, fvouchergroupno, faccbookorgid, faccbookorgid_name, split, fentity, fexplanation, faccountid, faccountid_name, facctfullname, fdetailid_fflex14, fdetailid_fflex14_name, fdetailid_fflex15, fdetailid_fflex15_name, fdetailid_fflex12, fdetailid_fflex12_name, fdetailid_fflex13, fdetailid_fflex13_name, fdetailid_ff100003, fdetailid_ff100003_name, fdetailid_ff100004, fdetailid_ff100004_name, fdetailid_fflex16, fdetailid_fflex16_name, fdetailid_ff100002, fdetailid_ff100002_name, fdetailid_fflex11, fdetailid_fflex11_name, fdetailid_fflex5, fdetailid_fflex5_name, fdetailid_fflex6, fdetailid_fflex6_name, fdetailid_fflex4, fdetailid_fflex4_name, fdetailid_fflex9, fdetailid_fflex9_name, fdetailid_fflex10, fdetailid_fflex10_name, fdetailid_fflex7, fdetailid_fflex7_name, fdetailid_fflex8, fdetailid_fflex8_name, fcurrencyid, fcurrencyid_name, fexchangeratetype, fexchangeratetype_name, fdebit, fcredit
    </sql>

</mapper>
