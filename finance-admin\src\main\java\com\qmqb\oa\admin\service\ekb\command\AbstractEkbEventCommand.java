package com.qmqb.oa.admin.service.ekb.command;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.common.util.NumberUtil;
import com.hzed.structure.common.util.ObjectUtil;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.common.util.str.FaJsonUtil;
import com.hzed.structure.lock.annotation.Lock;
import com.hzed.structure.tool.api.ApiResponse;
import com.hzed.structure.tool.util.JacksonUtil;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.config.CommonConfig;
import com.qmqb.oa.admin.constant.EkbConstants;
import com.qmqb.oa.admin.domain.dto.*;
import com.qmqb.oa.admin.domain.remote.reponse.EkbFeeTypeSearchResponse;
import com.qmqb.oa.admin.domain.request.internal.EkbDingTodoResultDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbProvisionalAuthRequest;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.admin.domain.vo.ekb.EkbWorkFlowDetailRsp;
import com.qmqb.oa.admin.domain.vo.ekb.EkbWorkFlowItemsRsp;
import com.qmqb.oa.admin.service.hsfk.DingMsgService;
import com.qmqb.oa.admin.service.hsfk.HeSiFeiKongService;
import com.qmqb.oa.admin.service.hsfk.SyncEkbService;
import com.qmqb.oa.admin.util.ekb.EkbResourceUtil;
import com.qmqb.oa.common.constant.StringPool;
import com.qmqb.oa.common.enums.*;
import com.qmqb.oa.system.domain.*;
import com.qmqb.oa.system.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 命令模式基类
 * T 参数对象泛型 V 响应业务结果对象泛型
 *
 * <AUTHOR>
 * @Description ekb 抽象事件基类
 * @Date 2024\10\12 0012 9:15
 * @Version 1.0
 */

@Slf4j
@Component
public abstract class AbstractEkbEventCommand<T, V> {

    @Autowired
    private DingUserService dingUserService;
    @Autowired
    private EkbDingTodoMessageService ekbDingTodoMessageService;

    @Autowired
    private EkbMessageService ekbMessageService;

    @Autowired
    private DingMsgService dingMsgService;

    @Autowired
    private HeSiFeiKongService heSiFeiKongService;

    @Autowired
    private SysEventService sysEventService;

    @Autowired
    private DingCompanyService dingCompanyService;

    @Autowired
    private EkbDingUserMappingService ekbDingUserMappingService;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private SyncEkbService syncEkbService;

    @Resource
    private DingAppConfService dingAppConfService;

    /**
     * 机器人ebot
     */
    private final String EBOT = "ebot";

    /**
     * 参数对象
     */
    private ThreadLocal<T> paramObjectLocal = new ThreadLocal<T>();
    /**
     * 事件处理的传输对象
     */
    private ThreadLocal<EkbEventCommandDTO> currentEkbEventCommandDTO = new ThreadLocal<EkbEventCommandDTO>();

    /**
     * 关联当前补发的job 事件，执行该事件之前，存在关联的补发job 才有值
     */
    private ThreadLocal<SysEvent> currentReissueSysEvent = new ThreadLocal<SysEvent>();
    /**
     * 当前事件是否需要发消息
     */
    private ThreadLocal<EkbDingTodoMessage> currentApprovingTodoMsg = new ThreadLocal<EkbDingTodoMessage>();
    /**
     * 执行者的unionId列表，最大数量1000。
     */
    private ThreadLocal<List<String>> executorIds = new ThreadLocal<List<String>>();

    /***
     * 执行命令前进行参数校验
     * @return 校验结果
     */
    public abstract boolean validation();

    /***
     * 命令执行结果
     * @return 执行结果
     */
    public abstract ApiResponse<V> execute();

    /**
     * 销毁本地线程
     */
    public void destroyThreadLocal() {
        paramObjectLocal.remove();
        currentEkbEventCommandDTO.remove();
        currentReissueSysEvent.remove();
        currentApprovingTodoMsg.remove();
        executorIds.remove();
    }

    /**
     * 核对基础数据有效性
     * 初始化t_ekb_message 与 t_ekb_todo_message的数据
     * 判断t_sys_event事件是否存在，存在则不新增事件
     *
     * @return
     */
    public boolean checkBaseParams() {
        if (ObjectUtil.isNull(getParamObject())) {
            return false;
        }

        if (getParamObject() instanceof EkbReceiveEkbEventRequest) {
            EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
            if (ObjectUtil.isNull(request.getUserInfo()) || StringUtil.isAnyBlank(request.getAction(), request.getMessageId(), request.getFlowId())) {
                return false;
            }

            //初始化事件数据，保存ekb_message 与 ekb_todo_msg
            EkbEventCommandDTO eventCommandDTO = receiveEkbEventInitData(request);
            if (ObjectUtil.isNull(eventCommandDTO)) {
                return false;
            }
            //判断该事件是否已经存储
            SysEvent sysEvent = sysEventService.getSysEventByTypeAndParams(eventCommandDTO.getEkbDingTodoMessage().getCompanyId(), SysEventEnum.EKB_EVENT_NOTICE.getEventSource(),
                    SysEventEnum.EKB_EVENT_NOTICE.getEventType(), eventCommandDTO.getEkbMessage().getMessageId());
            if(sysEvent != null){
                this.currentReissueSysEvent.set(sysEvent);
            }
            currentEkbEventCommandDTO.set(eventCommandDTO);
        }
        return true;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Lock(keys = {"#ekbEventRequest.flowId", "#ekbEventRequest.action"}, waitTime = 20)
    protected EkbEventCommandDTO receiveEkbEventInitData(EkbReceiveEkbEventRequest ekbEventRequest) {
        // 查找ekb 用户
        EkbDingUserMapping ekbReceiverUser = ekbDingUserMappingService.findOneByEkbUserId(ekbEventRequest.getUserInfo().getId());
        EkbDingUserMapping ekbSubmitterUser = ekbDingUserMappingService.findOneByEkbUserId(ekbEventRequest.getSubmitterId().getId());

        if (ObjectUtil.isNull(ekbReceiverUser) || ObjectUtil.isNull(ekbSubmitterUser)) {
            return null;
        }

        // 接收人和提交人
        List<DingUser> dingUsers = dingUserService.listByMobile(ekbReceiverUser.getMobile());
        List<DingUser> submitterUsers = dingUserService.listByMobile(ekbSubmitterUser.getMobile());

        if (CollUtil.isEmpty(dingUsers) || CollUtil.isEmpty(submitterUsers)) {
            return null;
        }

        // 获取同主体用户列表
        List<DingUser> receiverAndSubmitterList = getSameCompanyDingUsers(dingUsers, submitterUsers);

        if (CollUtil.isEmpty(receiverAndSubmitterList)) {
            return null;
        }

        DingUser dingUser = receiverAndSubmitterList.get(0);
        DingUser submitterUser = receiverAndSubmitterList.get(1);

        if (ObjectUtil.isNull(dingUser) || ObjectUtil.isNull(submitterUser) || StringUtil.isAnyBlank(dingUser.getUnionId(), submitterUser.getUnionId())) {
            log.warn("####### 钉钉用户不存在,receiptUserId={}, submitUserId={}", ekbEventRequest.getUserInfo().getId(), ekbEventRequest.getSubmitterId().getId());
            return null;
        }


        EkbMessage ekbMessage;
        EkbDingTodoMessage ekbDingTodoMessage = null;

        //存储普通消息
        ekbMessage = new EkbMessage();
        ekbMessage.setMessageId(ekbEventRequest.getMessageId());
        ekbMessage.setFlowId(ekbEventRequest.getFlowId());
        ekbMessage.setUserName(ekbEventRequest.getUserInfo().getName());
        ekbMessage.setUserId(ekbEventRequest.getUserInfo().getId());
        ekbMessage.setState(ekbEventRequest.getState());
        ekbMessage.setCreateTime(LocalDateTime.now());
        ekbMessage.setActionName(ekbEventRequest.getActionName());
        ekbMessage.setRemark(JacksonUtil.toJson(ekbEventRequest));
        ekbMessage.setMsgAction(ekbEventRequest.getAction());

        //搜索
        EkbMessage searchEkbMessage = ekbMessageService.findOneByFlowIdAndMessageId(ekbEventRequest.getFlowId(), ekbEventRequest.getMessageId(), ekbEventRequest.getUserInfo().getId());

        EkbDingTodoMessage searchEkbDingTodoMessage = ekbDingTodoMessageService.findOneByFlowIdAndMessageId(ekbEventRequest.getFlowId(), ekbEventRequest.getMessageId(), dingUser.getUnionId());

        ekbDingTodoMessage = new EkbDingTodoMessage();
        ekbDingTodoMessage.setEkbMessageId(ekbEventRequest.getMessageId());
        ekbDingTodoMessage.setEkbFlowId(ekbEventRequest.getFlowId());
        ekbDingTodoMessage.setDingSourceId(IdUtil.getUUID());
        ekbDingTodoMessage.setCompanyId(dingUser.getCompanyId());
        ekbDingTodoMessage.setDingUnionId(dingUser.getUnionId());
        ekbDingTodoMessage.setActionName(ekbEventRequest.getActionName());
        ekbDingTodoMessage.setCreateTime(LocalDateTime.now());
        ekbDingTodoMessage.setEkbMsgAction(ekbEventRequest.getAction());

        //防重
        if (ObjectUtil.isNull(searchEkbMessage) && ObjectUtil.isNull(searchEkbDingTodoMessage)) {
            ekbMessageService.save(ekbMessage);
            ekbDingTodoMessageService.save(ekbDingTodoMessage);
        } else {
            //存在的才进行设置
            ekbMessage = searchEkbMessage;
            //存在进行设置
            ekbDingTodoMessage = searchEkbDingTodoMessage;
        }

        /**
         * flow.rejected=被驳回;freeflow.retract=单据撤回;freeflow.delete=单据删除;backlog.sending=待寄送;
         * flow.paid=已支付/审批完成;freeflow.mention=被@;backlog.paying=待支付;
         * freeflow.comment=评论;backlog.approving=待审批;freeflow.carbonCopy=抄送
         */


        EkbEventCommandDTO eventCommandDTO = new EkbEventCommandDTO();
        eventCommandDTO.setEkbMessage(ekbMessage);
        eventCommandDTO.setEkbDingTodoMessage(ekbDingTodoMessage);
        eventCommandDTO.setEkbReceiverUser(ekbReceiverUser);
        //设置提交人
        eventCommandDTO.setSubmitterUser(submitterUser);
        eventCommandDTO.setDingUser(dingUser);
        return eventCommandDTO;
    }

    public void setParamObject(T paramObject) {
        this.paramObjectLocal.set(paramObject);
    }

    public T getParamObject() {
        return this.paramObjectLocal.get();
    }

    public EkbEventCommandDTO getEkbEventCommandDTO() {
        return this.currentEkbEventCommandDTO.get();
    }

    public DingMsgService getDingMsgService() {
        return this.dingMsgService;
    }

    public EkbDingTodoMessage getApprovingEkbMsg() {
        return this.currentApprovingTodoMsg.get();
    }


    /**
     * 获取当前节点所有人员的未处理的待办任务列表
     * @param request
     * @param eventCommandDTO
     * @return
     */
    public List<EkbDingTodoMessage> getUnHandlerTodoListOnSameNode(EkbReceiveEkbEventRequest request, EkbEventCommandDTO eventCommandDTO) {
        //需要处理的待办任务的用户集合
        Set<String> userIdSet = new HashSet<>();
        userIdSet.add(request.getUserInfo().getId());

        //找出同节点的会签/或签审批人列表
        JSONObject jsonObject = JSON.parseObject(eventCommandDTO.getEkbMessage().getRemark());
        JSONArray currentApprovers = jsonObject.getJSONArray("currentApprovers");
        log.info("当前节点的审批人列表:{}",JSON.toJSONString(currentApprovers));
        //如果当前节点审批人中出现ebot，则代表当前节点只有操作人自己
        if(!currentApprovers.contains(EBOT)){
            //如果没有出现ebot，则有同节点的其他审批人
            for (Object obj : currentApprovers) {
                EkbCurrentApprove currentApprove = JSONObject.parseObject(JSON.toJSONString(obj),EkbCurrentApprove.class);
                userIdSet.add(currentApprove.getId());
            }
        }

        List<EkbDingTodoMessage> unHandleMessageList = ekbDingTodoMessageService.todoListByFlowIdAndUserIdSet(request.getFlowId(),userIdSet);
        log.info("待办任务列表:{}",JSON.toJSONString(unHandleMessageList));
        //需要处理的钉钉待办任务列表
        List<EkbDingTodoMessage> necessaryDealEkbTasks = new ArrayList<>();

        //找出这些用户在同单据的待办消息
        for (EkbDingTodoMessage flow : unHandleMessageList) {
            if (StringUtil.isBlank(flow.getRemark())) {
                necessaryDealEkbTasks.add(flow);
            } else {
                EkbDingTodoResultDTO todoResultDTO = FaJsonUtil.toObject(flow.getRemark(), EkbDingTodoResultDTO.class);
                //开发中或尚未提交的任务进行待办任务
                if (StringUtil.isBlank(todoResultDTO.getTaskProcessedResult()) || (StringUtil.equalsAny(todoResultDTO.getTaskProcessedResult(), EkbFlowStageNameEnums.DEV.getDbCode(), EkbFlowStageNameEnums.NO_SUBMIT.getDbCode()))) {
                    necessaryDealEkbTasks.add(flow);
                }
            }

        }

        log.info("待处理的待办任务列表:{}",JSON.toJSONString(unHandleMessageList));
        return necessaryDealEkbTasks;
    }

    /**
     * 发送todo钉钉消息
     *
     * @return
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Lock(keys = {"#ekbEventRequest.flowId", "#ekbEventRequest.action"}, waitTime = 20)
    protected boolean sendToDoDingMsg(EkbReceiveEkbEventRequest ekbEventRequest) {
        //ekb 事件处理传输实体
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();
        EkbWorkFlowDetailRsp detailRsp = syncEkbService.getEkbFlowDetailDescByFlowId(ekbEventRequest.getFlowId());
        //消息内容
        String title = detailRsp.getValue().getForm().getTitle();
        String msgTitle = StringUtil.concat(true, ekbEventRequest.getSubmitterId().getName(), "提交的", ekbEventRequest.getFormSpecification().specificationName);
        //金额描述
        String amountDec = "";
        BigDecimal sumAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(ekbEventRequest.getDetails())) {
            for (EkbReceiveEkbEventRequest.FeeDetail feeDetail : ekbEventRequest.getDetails()) {
                sumAmount = NumberUtil.add(sumAmount, NumberUtil.toBigDecimal(feeDetail.getFeeTypeForm().getAmount().getStandard()));
            }
            amountDec = StringUtil.concat(true, sumAmount.toPlainString(), ekbEventRequest.getDetails().get(0).getFeeTypeForm().getAmount().getStandardUnit());
        }
        Map<String, String> msgDesc = new LinkedHashMap<>(4);
        String legalPerson = ObjectUtil.isNotNull(ekbEventRequest.get法人实体()) ? JacksonUtil.toJson(ekbEventRequest.get法人实体().getName()) : "";


        Set<String> msgDescLinkHashSet = new LinkedHashSet<>();
        //1.设置标题
        msgDescLinkHashSet.add(StringUtil.concat(true, "标题 ", StringPool.AT, title));

        /**
         *
         * 费用明细详情下标排序位置，标题固定排在第一位
         */
        int feeDetailDescIndex = 2;
        if (StringUtil.isNotBlank(legalPerson)) {
            legalPerson = StringUtil.concat(true, "法人实体 ", StringPool.AT, legalPerson);
            msgDescLinkHashSet.add(legalPerson);
            feeDetailDescIndex = 3;
        }

        //费用明细
        String feeDetailDesc = getFeeDetailDesc(ekbEventRequest, feeDetailDescIndex, true);
        //设置费用明细
        if (StringUtil.isNotBlank(feeDetailDesc)) {
            msgDescLinkHashSet.add(feeDetailDesc);
        }

        if (StringUtil.isNotBlank(amountDec)) {
            amountDec = StringUtil.concat(true, "合计金额 ", StringPool.AT, amountDec);
            msgDescLinkHashSet.add(amountDec);
        }

        int index = 1;
        for (String msg : msgDescLinkHashSet) {
            //[0] 作为key [1] 作为value
            String[] msgArray = msg.split(StringPool.AT);
            msgDesc.put(StringUtil.concat(true, String.valueOf(index), "、", msgArray[0]), msgArray[1]);
            index++;
        }

        DingSubmitTodoMsgDTO todoMsgDTO = DingSubmitTodoMsgDTO.builder().build()
                .setMessageId(eventCommandDTO.getEkbDingTodoMessage().getEkbMessageId())
                .setSourceId(eventCommandDTO.getEkbDingTodoMessage().getDingSourceId())
                .setContent(msgTitle)
                .setCompanyId(eventCommandDTO.getEkbDingTodoMessage().getCompanyId())
                .setRecipientUnionId(eventCommandDTO.getEkbDingTodoMessage().getDingUnionId())
                .setSubmitUnionId(eventCommandDTO.getSubmitterUser().getUnionId())
                .setEkbFlowId(ekbEventRequest.getFlowId())
                .setEkbUserId(eventCommandDTO.getEkbReceiverUser().getEkbUserId())
                .setDesc(msgDesc);
        String taskId = null;
        try {
            //获取url 详情
            taskId = getDingMsgService().sendTodoMsg(todoMsgDTO);
        } catch (Exception ex) {
            log.info("###### ekb {} event error ,msg = {}", eventCommandDTO.getEkbMessage().getMsgAction(), ex.getMessage());
        }

        boolean updateResult = updateSendTodoMessage(ekbEventRequest, eventCommandDTO, taskId);
        return updateResult;
    }


    /**
     * 更新易快报到钉钉待办消息表状态和易快报消息通知记录状态
     * 根据处理结果更新EkbDingTodoMessage和EkbMessage对象，并处理相关业务逻辑
     *
     * @param ekbEventRequest 易快报事件请求对象，包含事件相关信息
     * @param eventCommandDTO 事件命令数据传输对象，包含待更新的消息信息
     * @param taskId 任务ID，用于更新钉钉任务状态
     * @return 返回更新结果，true表示更新成功，false表示更新失败
     */
    private boolean updateSendTodoMessage(EkbReceiveEkbEventRequest ekbEventRequest, EkbEventCommandDTO eventCommandDTO, String taskId) {
        //更新易快报到钉钉待办消息表状态
        EkbDingTodoMessage updateTodoMessage = new EkbDingTodoMessage();
        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());

        //更新易快报消息通知记录状态
        EkbMessage updateMessage = new EkbMessage();
        updateMessage.setId(eventCommandDTO.getEkbMessage().getId());

        if (StringUtil.isBlank(eventCommandDTO.getEkbDingTodoMessage().getRemark())) {
            //设置json 默认值否则午饭set 属性值
            EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build().setMsgAction(ekbEventRequest.getAction());
            updateTodoMessage.setRemark(JacksonUtil.toJson(ekbDingTodoResultDTO));
        }
        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());
        if (StringUtil.isNotBlank(taskId)) {
            updateTodoMessage.setDingTaskId(taskId);
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
        } else {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            //判断该事件是否已经存储
            if (ObjectUtil.isNull(this.currentReissueSysEvent.get())) {
                String errorMsg = StringUtil.concat(true, eventCommandDTO.getEkbMessage().getMsgAction(), " event send to ding msg failure");
                EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
                Long eventTime = ObjectUtil.isNotNull(request.getSubmitDate()) ? request.getSubmitDate() : DateTimeUtil.getUnixTimestamp(LocalDateTime.now());
                sysEventService.saveSysEvent(eventCommandDTO.getEkbDingTodoMessage().getCompanyId(), SysEventEnum.EKB_EVENT_NOTICE.getEventSource(),
                        SysEventEnum.EKB_EVENT_NOTICE.getEventType(), JacksonUtil.toJson(getParamObject()), eventTime, eventCommandDTO.getEkbMessage().getMessageId(), errorMsg);
            }
        }


        //更新易快报到钉钉待办消息表钉钉发送状态
        boolean updateResult = updateEkbMsgAndToDingMsgStatus(updateMessage, updateTodoMessage);

        //设置处理结果
        if (updateResult) {
            eventCommandDTO.getEkbMessage().setState(updateMessage.getState());
            eventCommandDTO.getEkbDingTodoMessage().setDingTaskId(updateTodoMessage.getDingTaskId());
            eventCommandDTO.getEkbDingTodoMessage().setDingTaskStatus(updateTodoMessage.getDingTaskStatus());
            //重新更新到本地变量中
            this.currentEkbEventCommandDTO.set(eventCommandDTO);
        }
        //执行成功 更新补偿 job 事件 状态未执行成功
        updateReissueSysEvent(updateResult, updateMessage);
        return updateResult;
    }

    /**
     * 更新待办钉钉消息，同步更新数据库的消息
     * 如果是补偿事件sysEvent处理的，需要把对应的事件处理状态更新
     * @param ekbEventRequest       请求参数
     * @param necessaryDealEkbTasks 需要处理的待办任务
     * @return key: 待办消息id, value: 更新结果
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Lock(keys = {"#ekbEventRequest.flowId", "#ekbEventRequest.action"}, waitTime = 20)
    protected Map<Long, Boolean> updateTodoDingMsg(EkbReceiveEkbEventRequest ekbEventRequest, List<EkbDingTodoMessage> necessaryDealEkbTasks) {
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();

        //待办任务更新结果 key 未messageId value: 执行结果
        Map<Long, Boolean> batchExecuteResult = new HashMap<>(2);
        //更新结果
        Boolean updateDingResult = false;
        //如果没有需要处理的待办任务直接设置为true
        if (necessaryDealEkbTasks.isEmpty()) {
            updateDingResult = true;
        }
        //是否存在执行更新失败的任务
        Boolean existsFailure = false;
        try {
            //循环更新需要处理的待办任务
            for (EkbDingTodoMessage approvingTodoMsg : necessaryDealEkbTasks) {
                DingUpdateTodoMsgDTO updateTodoMsgDTO = DingUpdateTodoMsgDTO.builder().build()
                        .setMessageId(approvingTodoMsg.getEkbMessageId())
                        .setContent(eventCommandDTO.getEkbDingTodoMessage().getActionName())
                        .setCompanyId(approvingTodoMsg.getCompanyId())
                        .setUnionId(approvingTodoMsg.getDingUnionId())
                        .setRecipientUnionIds(Arrays.asList(approvingTodoMsg.getDingUnionId()))
                        .setIsDone(true)
                        .setTaskId(approvingTodoMsg.getDingTaskId());
                try {
                    //更新待办消息或钉钉任务执行者状态
                    updateDingResult = getDingMsgService().updateTodoMsg(updateTodoMsgDTO);
                    if (!updateDingResult && !existsFailure) {
                        existsFailure = true;
                    }
                    batchExecuteResult.put(approvingTodoMsg.getId(), updateDingResult);
                } catch (Exception ex) {
                    log.info("###### messagId= {} event error ,msg = {}", approvingTodoMsg.getEkbMessageId(), ex.getMessage());
                }
            }

        } catch (Exception ex) {
            log.info("###### ekb {} event error ,msg = {}", eventCommandDTO.getEkbMessage().getMsgAction(), ex.getMessage());
        }

        //如果存在执行失败审批任务，执行设置为失败，继续下一次执行补偿
        if (existsFailure) {
            updateDingResult = false;
        }
        //更新易快报到钉钉待办消息表状态
        EkbDingTodoMessage updateTodoMessage = new EkbDingTodoMessage();
        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());

        //更新易快报消息通知记录状态
        EkbMessage updateMessage = new EkbMessage();
        updateMessage.setId(eventCommandDTO.getEkbMessage().getId());

        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());

        if (updateDingResult) {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
        } else {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            //判断该事件是否已经存储
            if (ObjectUtil.isNull(this.currentReissueSysEvent.get())) {
                String errorMsg = StringUtil.concat(true, eventCommandDTO.getEkbMessage().getMsgAction(), " event update to ding msg failure");
                EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
                //审批时间
                Long eventTime = ObjectUtil.isNotNull(request.getSubmitDate()) ? request.getSubmitDate() : DateTimeUtil.getUnixTimestamp(LocalDateTime.now());
                sysEventService.saveSysEvent(eventCommandDTO.getEkbDingTodoMessage().getCompanyId(), SysEventEnum.EKB_EVENT_NOTICE.getEventSource(),
                        SysEventEnum.EKB_EVENT_NOTICE.getEventType(), JacksonUtil.toJson(getParamObject()), eventTime, eventCommandDTO.getEkbMessage().getMessageId(), errorMsg);
            }

        }
        log.info("更新当前的出站消息");
        //更新易快报到钉钉待办消息表钉钉发送状态
        boolean updateResult = updateEkbMsgAndToDingMsgStatus(updateMessage, updateTodoMessage);

        //设置处理结果
        if (updateResult) {
            eventCommandDTO.getEkbMessage().setState(updateMessage.getState());
            eventCommandDTO.getEkbDingTodoMessage().setDingTaskStatus(updateTodoMessage.getDingTaskStatus());
            //重新更新到本地变量中
            this.currentEkbEventCommandDTO.set(eventCommandDTO);
        }
        //执行成功 更新补偿 job 事件 状态未执行成功
        updateReissueSysEvent(updateResult, updateMessage);
        return batchExecuteResult;
    }


    /**
     * 删除待办钉钉消息
     *
     * @param necessaryDealEkbTasks 需要处理的任务
     * @return
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    protected Map<Long, Boolean> deleteTodoDingMsg(List<EkbDingTodoMessage> necessaryDealEkbTasks) {
        //待办任务更新结果 key 未messageId value: 执行结果
        Map<Long, Boolean> batchExecuteResult = new HashMap<>(2);
        //ekb 事件处理传输实体
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();
        //是否存在执行更新失败的任务
        Boolean existsFailure = false;
        boolean updateDingResult = false;
        if (CollUtil.isEmpty(necessaryDealEkbTasks)) {
            updateDingResult = true;
        }
        try {
            for (EkbDingTodoMessage approvingTodoMsg : necessaryDealEkbTasks) {
                DingDeleteTodoMsgDTO deleteTodoMsgDTO = DingDeleteTodoMsgDTO.builder().build()
                        .setMessageId(approvingTodoMsg.getEkbMessageId())
                        .setCompanyId(approvingTodoMsg.getCompanyId())
                        .setRecipientUnionId(approvingTodoMsg.getDingUnionId())
                        .setSubmitUnionId(eventCommandDTO.getSubmitterUser().getUnionId())
                        .setTaskId(approvingTodoMsg.getDingTaskId());
                //更新结果
                try {
                    updateDingResult = getDingMsgService().deleteTodoMsg(deleteTodoMsgDTO);
                } catch (Exception ex) {
                    log.info("###### ekb delete event error ,messageId = {}, ex={}", approvingTodoMsg.getEkbMessageId(), ex.getMessage());
                }

                if (!updateDingResult && !existsFailure) {
                    existsFailure = true;
                }
                batchExecuteResult.put(approvingTodoMsg.getId(), updateDingResult);
            }
        } catch (Exception ex) {
            log.info("###### ekb {} event error ,msg = {}", eventCommandDTO.getEkbMessage().getMsgAction(), ex.getMessage());
        }

        //如果存在执行失败审批任务，执行设置为失败，继续下一次执行补偿
        if (existsFailure) {
            updateDingResult = false;
        }
        //更新易快报到钉钉待办消息表状态
        EkbDingTodoMessage updateTodoMessage = new EkbDingTodoMessage();
        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());

        //更新易快报消息通知记录状态
        EkbMessage updateMessage = new EkbMessage();
        updateMessage.setId(eventCommandDTO.getEkbMessage().getId());

        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());
        if (updateDingResult) {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
        } else {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());

            //判断该事件是否已经存储
            if (ObjectUtil.isNull(this.currentReissueSysEvent.get())) {
                //审批时间
                EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
                Long eventTime = ObjectUtil.isNotNull(request.getSubmitDate()) ? request.getSubmitDate() : DateTimeUtil.getUnixTimestamp(LocalDateTime.now());
                String errorMsg = StringUtil.concat(true, eventCommandDTO.getEkbMessage().getMsgAction(), " event delete to ding msg failure");
                sysEventService.saveSysEvent(eventCommandDTO.getEkbDingTodoMessage().getCompanyId(), SysEventEnum.EKB_EVENT_NOTICE.getEventSource(),
                        SysEventEnum.EKB_EVENT_NOTICE.getEventType(), JacksonUtil.toJson(getParamObject()), eventTime, eventCommandDTO.getEkbMessage().getMessageId(), errorMsg);
            }
        }

        //更新易快报到钉钉待办消息表钉钉发送状态
        boolean updateResult = updateEkbMsgAndToDingMsgStatus(updateMessage, updateTodoMessage);

        //设置处理结果
        if (updateResult) {
            eventCommandDTO.getEkbMessage().setState(updateMessage.getState());
            eventCommandDTO.getEkbDingTodoMessage().setDingTaskStatus(updateTodoMessage.getDingTaskStatus());
            //重新更新到本地变量中
            this.currentEkbEventCommandDTO.set(eventCommandDTO);
        }
        //执行成功 更新补偿 job 事件 状态未执行成功
        updateReissueSysEvent(updateResult, updateMessage);
        return batchExecuteResult;
    }


    /**
     * 发送钉钉通知消息
     *
     * @return
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    protected boolean sendNotifyMsg() {
        //ekb 事件处理传输实体
        EkbEventCommandDTO eventCommandDTO = getEkbEventCommandDTO();

        EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();

        EkbWorkFlowDetailRsp detailRsp = syncEkbService.getEkbFlowDetailDescByFlowId(request.getFlowId());
        //消息内容
        String title = detailRsp.getValue().getForm().getTitle();
        //图标
        String iconImgId = Tenant.findByValue(eventCommandDTO.getDingUser().getCompanyId()).getIconImgId();
        //钉钉消息标题保证唯一，否则相同内容可能会过滤
        String msgTitle = "";
        if (request.getAction().equals(EkbActionEnum.FREEFLOW_COMMENT.getAction())) {
            msgTitle = StringUtil.concat(true, "有同事评论了", request.getSubmitterId().getName(), "提交的", request.getFormSpecification().specificationName);
        } else if (request.getAction().equals(EkbActionEnum.FREEFLOW_MENTION.getAction())) {
            msgTitle = StringUtil.concat(true, "有同事在", request.getSubmitterId().getName(), "提交的", request.getFormSpecification().specificationName, "@了你");
        } else if (request.getAction().equals(EkbActionEnum.FREEFLOW_CARBON_COPY.getAction())) {
            msgTitle = StringUtil.concat(true, "有同事在", request.getSubmitterId().getName(), "提交的", request.getFormSpecification().specificationName, "抄送给你");
        } else if (request.getAction().equals(EkbActionEnum.FLOW_PAID.getAction())) {
            msgTitle = StringUtil.concat(true, request.getSubmitterId().getName(), "提交的", request.getFormSpecification().specificationName);
            iconImgId = Tenant.findByValue(eventCommandDTO.getDingUser().getCompanyId()).getPaidImgId();
        }

        //金额描述
        String amountDec = "";
        BigDecimal sumAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(request.getDetails())) {
            for (EkbReceiveEkbEventRequest.FeeDetail feeDetail : request.getDetails()) {
                sumAmount = NumberUtil.add(sumAmount, NumberUtil.toBigDecimal(feeDetail.getFeeTypeForm().getAmount().getStandard()));
            }
            amountDec = StringUtil.concat(true, sumAmount.toPlainString(), request.getDetails().get(0).getFeeTypeForm().getAmount().getStandardUnit());
        }

        Map<String, String> msgDescMap = new LinkedHashMap<>(4);
        String legalPerson = ObjectUtil.isNotNull(request.get法人实体()) ? JacksonUtil.toJson(request.get法人实体().getName()) : "";


        Set<String> msgDescLinkHashSet = new LinkedHashSet<>();
        //1.设置标题
        msgDescLinkHashSet.add(StringUtil.concat(true, "标题：", StringPool.AT, title));

        /**
         *
         * 费用明细详情下标排序位置，标题固定排在第一位
         */
        int feeDetailDescIndex = 2;

        if (StringUtil.isNotBlank(legalPerson)) {
            legalPerson = StringUtil.concat(true, "法人实体：", StringPool.AT, legalPerson);
            msgDescLinkHashSet.add(legalPerson);
            feeDetailDescIndex = 3;
        }

        //费用明细
        String feeDetailDesc = getFeeDetailDesc(request, feeDetailDescIndex, false);
        //有顺序要求
        if (StringUtil.isNotBlank(feeDetailDesc)) {
            msgDescLinkHashSet.add(feeDetailDesc);
        }
        if (StringUtil.isNotBlank(amountDec)) {
            amountDec = StringUtil.concat(true, "合计金额：", StringPool.AT, amountDec);
            msgDescLinkHashSet.add(amountDec);
        }

        int index = 1;
        for (String msg : msgDescLinkHashSet) {
            //[0] 作为key [1] 作为value
            String[] msgArray = msg.split(StringPool.AT);
            msgDescMap.put(StringUtil.concat(true, String.valueOf(index), "、", msgArray[0]), msgArray[1] + " ");
            index++;
        }

        //消息实体拼接
        String msgDesc = msgDescMap.entrySet().stream()
                .map(entry -> entry.getKey() + entry.getValue())
                .collect(Collectors.joining(" "));
        if (StringUtil.isBlank(msgDesc)) {
            msgDesc = request.getActionName();
        }
        //保证消息内容唯一
        msgTitle = msgTitle + StringPool.EMPTY + DateTimeUtil.format(LocalDateTime.now(), DateTimeUtil.PATTERN_DATETIME);


        //支付完成事件通知任务提交者
        DingSendNotifyMsgDTO notifyMsgDTO = DingSendNotifyMsgDTO.builder().build()
                .setTitle(msgTitle)
                .setUserId(eventCommandDTO.getDingUser().getUserId())
                .setCompanyId(eventCommandDTO.getEkbDingTodoMessage().getCompanyId())
                .setEkbMessageId(eventCommandDTO.getEkbDingTodoMessage().getEkbMessageId())
                .setEkbUserId(eventCommandDTO.getEkbReceiverUser().getEkbUserId())
                .setEkbFlowId(request.getFlowId())
                .setText(msgDesc)
                .setIconImgId(iconImgId);
        //更新结果
        boolean updateDingResult = false;
        try {
            //更新待办消息,paid 事件不需要继续操作
            updateDingResult = getDingMsgService().sendNotifyMsg(notifyMsgDTO);
            if (EkbActionEnum.FLOW_PAID.getAction().equals(request.getAction())) {
                return updateDingResult;
            }
        } catch (Exception ex) {
            log.info("###### ekb {} event error ,msg = {}", eventCommandDTO.getEkbMessage().getMsgAction(), ex.getMessage());
        }

        boolean updateResult = updateSendNotifyMessage(eventCommandDTO, request, updateDingResult);

        return updateResult;
    }

    /**
     * 更新发送通知消息的状态
     *
     * 此方法主要用于更新易快报事件命令DTO中的消息状态，包括易快报到钉钉待办消息表状态和易快报消息通知记录状态
     * 根据updateDingResult参数，决定是更新为成功还是失败的状态
     * 如果更新为失败，还会判断是否需要存储系统事件
     *
     * @param eventCommandDTO 易快报事件命令DTO，包含需要更新的消息信息
     * @param request 接收到的易快报事件请求，用于获取提交日期等信息
     * @param updateDingResult 钉钉消息更新结果，true表示成功，false表示失败
     * @return 返回更新结果，true表示更新成功，false表示更新失败
     */
    private boolean updateSendNotifyMessage(EkbEventCommandDTO eventCommandDTO, EkbReceiveEkbEventRequest request, boolean updateDingResult) {
        //更新易快报到钉钉待办消息表状态
        EkbDingTodoMessage updateTodoMessage = new EkbDingTodoMessage();
        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());

        //更新易快报消息通知记录状态
        EkbMessage updateMessage = new EkbMessage();
        updateMessage.setId(eventCommandDTO.getEkbMessage().getId());

        updateTodoMessage.setId(eventCommandDTO.getEkbDingTodoMessage().getId());
        if (updateDingResult) {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FINISHED.getStatus());
        } else {
            updateTodoMessage.setDingTaskStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            updateMessage.setDingtalkPushStatus(DingTodoTaskStatusEnums.FAILURE.getStatus());
            //判断该事件是否已经存储
            if (ObjectUtil.isNull(this.currentReissueSysEvent.get())) {
                //审批时间
                Long eventTime = ObjectUtil.isNotNull(request.getSubmitDate()) ? request.getSubmitDate() : DateTimeUtil.getUnixTimestamp(LocalDateTime.now());
                String errorMsg = StringUtil.concat(true, eventCommandDTO.getEkbMessage().getMsgAction(), " event send ding msg failure");
                sysEventService.saveSysEvent(eventCommandDTO.getEkbDingTodoMessage().getCompanyId(), SysEventEnum.EKB_EVENT_NOTICE.getEventSource(),
                        SysEventEnum.EKB_EVENT_NOTICE.getEventType(), JacksonUtil.toJson(getParamObject()), eventTime, eventCommandDTO.getEkbMessage().getMessageId(), errorMsg);
            }
        }

        //更新易快报到钉钉待办消息表钉钉发送状态
        boolean updateResult = updateEkbMsgAndToDingMsgStatus(updateMessage, updateTodoMessage);

        //设置处理结果
        if (updateResult) {
            eventCommandDTO.getEkbMessage().setState(updateMessage.getState());
            eventCommandDTO.getEkbDingTodoMessage().setDingTaskStatus(updateTodoMessage.getDingTaskStatus());
            //重新更新到本地变量中
            this.currentEkbEventCommandDTO.set(eventCommandDTO);
        }

        //执行成功 更新补偿 job 事件 状态未执行成功
        updateReissueSysEvent(updateResult, updateMessage);
        return updateResult;
    }

    /**
     * 事件是否已经执行
     *
     * @return
     */
    public boolean eventHasExecute() {
        EkbMessage ekbMessage = getEkbEventCommandDTO().getEkbMessage();
        //如果该事件已经执行成功则直接返回失败
        if (ObjectUtil.isNotNull(ekbMessage.getDingtalkPushStatus()) && DingTodoTaskStatusEnums.FINISHED.getStatus().equals(ekbMessage.getDingtalkPushStatus())) {
            return true;
        }
        if (ObjectUtil.isNotNull(getEkbEventCommandDTO().getEkbDingTodoMessage().getDingTaskId())) {
            return true;
        }
        log.info("该出站消息已经处理过");
        return false;
    }

    /**
     * 更新关联执行失败sysEvent 状态
     *
     * @param executeResult 是否执行成功
     * @param updateMessage 更新的消息实体
     */
    private void updateReissueSysEvent(boolean executeResult, EkbMessage updateMessage) {
        try {
            if (!executeResult || ObjectUtil.isNull(this.currentReissueSysEvent.get())) {
                return;
            }
            //执行成功 更新补偿 job 事件 状态未执行成功
            if (DingTodoTaskStatusEnums.FINISHED.getStatus().equals(updateMessage.getDingtalkPushStatus())) {
                sysEventService.update4SuccessById(this.currentReissueSysEvent.get());
            } else if (DingTodoTaskStatusEnums.FAILURE.getStatus().equals(updateMessage.getDingtalkPushStatus())) {
                sysEventService.updateFailReasonById(this.currentReissueSysEvent.get(), "推送钉钉失败");
            }
        } catch (Exception ex) {
            log.warn("########### ekb receive event update related sysEvent status failure,id={} ", this.currentReissueSysEvent.get().getId());
        }
    }


    private boolean updateEkbMsgAndToDingMsgStatus(EkbMessage updateMessage, EkbDingTodoMessage updateTodoMessage) {
        updateTodoMessage.setUpdateTime(LocalDateTime.now());
        updateMessage.setEditTime(LocalDateTime.now());
        //更新易快报到钉钉待办消息表钉钉发送状态
        boolean updateResult = ekbDingTodoMessageService.updateById(updateTodoMessage);
        if (updateResult) {
            updateResult = ekbMessageService.updateById(updateMessage);
        }

        return updateResult;
    }

    /**
     * 获取该流程事件下所有的待审批任务列表
     *
     * @param flowId
     * @return
     */
    public List<EkbDingTodoMessage> getCurrentApprovingTodoMsgs(String flowId) {
        List<EkbDingTodoMessage> approvingTodoMsgList = ekbDingTodoMessageService.findListByFlowIdAndEkbMsgAction(flowId, EkbActionEnum.BACKLOG_APPROVING.getAction(), null);
        return approvingTodoMsgList;
    }

    /**
     * 获取当前操作用户执行事件需要处理的审批单
     *
     * @param dingUnionId
     * @param flowId
     * @param msgAction
     * @return
     */
    private EkbDingTodoMessage getCurrentApprovingTodoMsg(String dingUnionId, String flowId, String msgAction) {
        List<EkbDingTodoMessage> approvingTodoMsgList = ekbDingTodoMessageService.findListByFlowIdAndEkbMsgAction(flowId, EkbActionEnum.BACKLOG_APPROVING.getAction(), null);
        if (CollUtil.isEmpty(approvingTodoMsgList)) {
            return null;
        }
        //设置执行者
        this.executorIds.set(approvingTodoMsgList.stream().map(EkbDingTodoMessage::getDingUnionId).distinct().collect(Collectors.toList()));
        EkbDingTodoMessage approvingTodoMsg = null;
        for (EkbDingTodoMessage todoMessage : approvingTodoMsgList) {
            //只需要有任意一个结果有值，则作为唯一审批任务
            if (StringUtil.isNotBlank(todoMessage.getDingTaskId())) {
                approvingTodoMsg = todoMessage;
                break;
            }
        }

        if (ObjectUtil.isNull(approvingTodoMsg)) {
            //查询当前用户待办消息,如果没有找到，则拿最近一条待审批的数据
            approvingTodoMsg = CollUtil.isNotEmpty(approvingTodoMsgList) ? approvingTodoMsgList.stream()
                    .filter(user -> StringUtil.equals(dingUnionId, user.getDingUnionId())).findFirst().orElse(null) : null;
        }

        if (ObjectUtil.isNull(approvingTodoMsg)) {
            approvingTodoMsg = CollUtil.isNotEmpty(approvingTodoMsgList) ? approvingTodoMsgList.stream().max(Comparator.comparingLong(EkbDingTodoMessage::getId)).orElse(null) : null;
        }

        if (ObjectUtil.isNull(approvingTodoMsg)) {
            log.warn("########### approvingTodoMsg 未找到,dingUnionId={}, action ={}", dingUnionId, msgAction);
            return null;
        }
        return approvingTodoMsg;
    }

    /**
     * 保存审批的消息结果
     *
     * @param ekbDingTodoResultDTO
     * @param ids                  需要更新待办任务ids
     * @return
     */
    protected int updateApprovingMsgRemark(EkbDingTodoResultDTO ekbDingTodoResultDTO, Long[] ids) {
        if (ObjectUtil.isNull(ids) || ids.length == 0) {
            return 0;
        }
        //设置最近一次更新扩展字段的action
        ekbDingTodoResultDTO.setMsgAction(getEkbEventCommandDTO().getEkbDingTodoMessage().getEkbMsgAction());
        Map<String, Object> params = BeanUtil.beanToMap(ekbDingTodoResultDTO, false, true);
        int result = ekbDingTodoMessageService.setMultiRemarkInfo(ids, params);
        log.info("approving保存额外信息字段结果2:{} ,保存的信息: {}", result, ekbDingTodoResultDTO);
        return result;
    }

    public EkbDingTodoMessageService getEkbDingTodoMessageService() {
        return ekbDingTodoMessageService;
    }

    /**
     * 获取临时跳转地址
     *
     * @param eventCommandDTO
     * @return
     */
    private String getRedirectUrl(EkbEventCommandDTO eventCommandDTO) {
        DingCompany dingCompany = dingCompanyService.getById(eventCommandDTO.getDingUser().getCompanyId());
        if (ObjectUtil.isNull(dingCompany)) {
            log.warn("####### 钉钉用户绑定公司主体不存在,companyId={}", dingCompany.getId());
            return null;
        }
        //获取钉钉访问详情url
        EkbProvisionalAuthRequest authRequest = new EkbProvisionalAuthRequest();
        authRequest.setClient(EkbConstants.DEFAULT_CLIENT);
        authRequest.setCorpId(dingCompany.getCorpId());
        authRequest.setEkbUserId(eventCommandDTO.getEkbReceiverUser().getEkbUserId());
        authRequest.setFlowId(eventCommandDTO.getEkbMessage().getFlowId());

        String redirectUrl = null;
        try {
            redirectUrl = heSiFeiKongService.getFormProvisionalauthurl(authRequest);
        } catch (Exception ex) {
            log.warn("####### 钉钉用户获取临时访问地址异常,ex={}", ex.getMessage());
        }
        return redirectUrl;
    }

    /**
     * 获取流程节点
     *
     * @param flowId 流程节点id
     * @return
     */
    public EkbWorkFlowItemsRsp getEkbWorkFlowItems(String flowId) {
        return syncEkbService.getEkbFlowDetailByFlowIds(flowId);
    }

    /**
     * 根据流程及消息ids 查询用户ekbUserIds映射 key 为messageId, value 为ekberUserId
     *
     * @param flowId
     * @param messageIds
     * @return
     */
    public Map<String, String> getEkbUserMapByFlowIdAndMessageIds(String flowId, List<String> messageIds) {
        List<EkbMessage> ekbMessageList = ekbMessageService.listByFlowIdAndMessageIds(flowId, messageIds);
        if (CollUtil.isNotEmpty(ekbMessageList)) {
            return ekbMessageList.stream().collect(Collectors.toMap(EkbMessage::getMessageId, EkbMessage::getUserId));
        } else {
            return null;
        }
    }


    /**
     * 获取该流程事件下所有的待审批任务列表
     *
     * @param flowId
     * @return
     */
    public List<EkbDingTodoMessage> getCurrentDealTodoMsgList(String flowId, List<String> msgActions) {
        List<EkbDingTodoMessage> approvingTodoMsgList = ekbDingTodoMessageService.findListByFlowIdAndEkbMsgActions(flowId, msgActions);
        return approvingTodoMsgList;
    }

    /**
     * 获取ekb 流程详情
     *
     * @param flowId
     * @return
     */
    public EkbWorkFlowDetailRsp getEkbFlowDetailDescByFlowId(String flowId) {
        return syncEkbService.getEkbFlowDetailDescByFlowId(flowId);
    }

    /**
     * 根据钉钉用户列表过滤出来需要推送的钉钉主体用户
     *
     * @param dingUsers
     * @return
     */
    private DingUser filterDingUser(List<DingUser> dingUsers) {
        if (CollUtil.isEmpty(dingUsers)) {
            return null;
        }
        DingUser dingUser = null;
        if (!commonConfig.isProdEnv()) {
            //测试环境先查询test主体 再查询oa 主体
            dingUser = dingUsers.stream().filter(user -> Tenant.QMQB.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
            if (ObjectUtil.isNull(dingUser)) {
                dingUser = dingUsers.stream().filter(user -> Tenant.OAOP_TEST.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
            }
        } else {
            //生产环境先查询qmqb 主体 在查询oa主体
            dingUser = dingUsers.stream().filter(user -> Tenant.QMQB.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
            if (ObjectUtil.isNull(dingUser)) {
                dingUser = dingUsers.stream().filter(user -> Tenant.OAOP.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
            }
        }
        return dingUser;

//        if (!commonConfig.isProdEnv()) {
//            //测试环境先查询test主体 再查询oa 主体
//            dingUser = dingUsers.stream().filter(user -> Tenant.OAOP_TEST.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
//            if (ObjectUtil.isNull(dingUser)) {
//                dingUser = dingUsers.stream().filter(user -> Tenant.OAOP.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
//            }
//        } else {
//            //生产环境先查询qmqb 主体 在查询oa主体
//            dingUser = dingUsers.stream().filter(user -> Tenant.QMQB.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
//            if (ObjectUtil.isNull(dingUs er)) {
//                dingUser = dingUsers.stream().filter(user -> Tenant.OAOP.getValue().equals(user.getCompanyId())).findFirst().orElse(null);
//            }
//        }
//        return dingUser;
    }

    /**
     * 获取同一公司下匹配的钉钉用户对（接收者和提交者）
     *
     * @param dingUsers       接收者用户列表（需包含不同公司的用户）
     * @param submitterUsers 提交者用户列表（需包含不同公司的用户）
     * @return 包含两个用户的List（第一个为接收者，第二个为提交者），当满足以下条件时返回null：
     *         1. 无共同主体ID时
     *         2. 未查询到有效主体配置时
     *         3. 对应主体用户不完整时
     */
    private List<DingUser> getSameCompanyDingUsers(List<DingUser> dingUsers, List<DingUser> submitterUsers) {
        // 将用户列表转换为以公司ID为键的Map
        Map<Integer, DingUser> receiverMap = dingUsers.stream()
                .collect(Collectors.toMap(DingUser::getCompanyId, Function.identity()));
        Map<Integer, DingUser> submitterMap = submitterUsers.stream()
                .collect(Collectors.toMap(DingUser::getCompanyId, Function.identity()));

        // 获取共同的companyId
        List<Integer> commonKeys = receiverMap.keySet().stream()
                .filter(submitterMap::containsKey)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(commonKeys)) {
            return null;
        }

        // 查询优先级最高的公司配置
        DingAppConf finalCompany = dingAppConfService.getOne(
                new LambdaQueryWrapper<DingAppConf>()
                        .in(DingAppConf::getCompanyId, commonKeys)
                        .orderByAsc(DingAppConf::getCompanyPriority)
                        .last("LIMIT 1"));
        if (finalCompany == null) {
            return null;
        }

        // 获取对应的用户
        Integer companyId = finalCompany.getCompanyId();
        DingUser receiver = receiverMap.get(companyId);
        DingUser submitter = submitterMap.get(companyId);

        return (receiver != null && submitter != null)
                ? Arrays.asList(receiver, submitter)
                : null;
    }


    /**
     * 获取费用明细详情
     *
     * @param ekbEventRequest
     * @param feeDetailDescIndex 费用详情在消息描述列表中的位置
     * @param isSendMsg
     * @return
     */
    public String getFeeDetailDesc(EkbReceiveEkbEventRequest ekbEventRequest, int feeDetailDescIndex, boolean isSendMsg) {
        String feeDetailDesc = "";
        if (CollUtil.isNotEmpty(ekbEventRequest.getDetails())) {
            int feeDetailIndex = 1;
            feeDetailDesc = StringUtil.concat(true, "费用明细", isSendMsg ? " " : "：", StringPool.AT, "  ");
            for (EkbReceiveEkbEventRequest.FeeDetail feeDetail : ekbEventRequest.getDetails()) {
                String feeTypeName = "";
                String feeAmount = StringUtil.concat(true, feeDetail.getFeeTypeForm().getAmount().getStandard(), feeDetail.getFeeTypeForm().getAmount().getStandardUnit());
                if (EkbResourceUtil.ekbInvoiceTypeDTOMap.containsKey(feeDetail.getFeeTypeId())) {
                    EkbInvoiceTypeDTO ekbInvoiceTypeDTO = EkbResourceUtil.ekbInvoiceTypeDTOMap.get(feeDetail.getFeeTypeId());
                    //通过本地缓存获取
                    feeTypeName = ekbInvoiceTypeDTO.getName();
                } else {
                    //通过接口查询
                    EkbFeeTypeSearchResponse searchResponse = syncEkbService.getEkbInvoiceTypeByIds(Arrays.asList(feeDetail.getFeeTypeId()));
                    if (ObjectUtil.isNotNull(searchResponse) && CollUtil.isNotEmpty(searchResponse.getItems()) && StringUtil.equals(feeDetail.getFeeTypeId(), searchResponse.getItems().get(0).getId())) {
                        feeTypeName = searchResponse.getItems().get(0).getName();
                        EkbInvoiceTypeDTO typeDTO = EkbInvoiceTypeDTO.builder().build()
                                .setId(feeDetail.getFeeTypeId())
                                .setName(feeTypeName)
                                .setCode(searchResponse.getItems().get(0).getCode());
                        //查询出的数据置入缓存中
                        EkbResourceUtil.invoiceTypesAdd(typeDTO);
                    }
                }
                feeDetailDesc = StringUtil.concat(true, feeDetailDesc, StringUtil.concat(true, "" + feeDetailDescIndex + ".", String.valueOf(feeDetailIndex)), "、", feeTypeName, StringPool.COLON, feeAmount, " ");
                feeDetailIndex++;
            }
        }
        return feeDetailDesc;
    }

    public DingUserService getDingUserService() {
        return dingUserService;
    }

    public SyncEkbService getSyncEkbService() {
        return syncEkbService;
    }

    public EkbDingUserMappingService getEkbDingUserMappingService() {
        return ekbDingUserMappingService;
    }

    public EkbMessageService getEkbMessageService() {
        return ekbMessageService;
    }
}
