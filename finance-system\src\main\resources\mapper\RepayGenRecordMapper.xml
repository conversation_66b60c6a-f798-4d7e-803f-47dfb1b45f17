<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.RepayGenRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.RepayGenRecord">
        <id column="id" property="id" />
        <result column="program_id" property="programId" />
        <result column="batch_no" property="batchNo" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="status" property="status" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="finish_time" property="finishTime" />
        <result column="dbCreateDt" property="dbcreatedt" />
        <result column="dbUpdateDt" property="dbupdatedt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, program_id, batch_no, start_date, end_date, status, creator, updater, finish_time, dbCreateDt, dbUpdateDt, data_type
    </sql>

    <select id="getBatchNoById" resultType="java.lang.String">
        select batch_no from t_repay_gen_record
        where id=#{id}
    </select>
    <select id="listByCondition" resultType="com.qmqb.oa.system.domain.RepayGenRecord">
    select
    <include refid="Base_Column_List"></include>
    from t_repay_gen_record
    where 1=1
    <if test="condition.status!=null">
        and status = #{condition.status}
    </if>
    <if test="condition.startDate!=null and condition.endDate!=null">
        and start_date >= #{condition.startDate}
        and #{condition.endDate} >= end_date
    </if>
    <if test="(condition.batchNo != null and condition.batchNo!='')">
        and batch_no = #{condition.batchNo}
    </if>
    <if test="condition.dataType!=null">
        and data_type = #{condition.dataType}
    </if>
    order by start_date desc
    </select>

</mapper>
