<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
            #foreach($column in $columns)
                #if($column.query)
                    #set($dictType=$column.dictType)
                    #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if($column.htmlType == "input")
                        <el-form-item label="${comment}" prop="${column.javaField}">
                            <el-input
                                    v-model="queryParams.${column.javaField}"
                                    placeholder="请输入${comment}"
                                    clearable
                                    size="small"
                                    @keyup.enter.native="handleQuery"
                            />
                        </el-form-item>
                    #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
                        <el-form-item label="${comment}" prop="${column.javaField}">
                            <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable size="small">
                                <el-option
                                        v-for="dict in ${column.javaField}Options"
                                        :key="dict.dictValue"
                                        :label="dict.dictLabel"
                                        :value="parseInt(dict.dictValue)"
                                />
                            </el-select>
                        </el-form-item>
                    #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
                        <el-form-item label="${comment}" prop="${column.javaField}">
                            <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable size="small">
                                <el-option label="请选择字典生成" value="" />
                            </el-select>
                        </el-form-item>
                    #elseif($column.htmlType == "datetime")
                        <el-form-item label="${comment}" prop="${column.javaField}">
                            <el-date-picker clearable size="small" style="width: 200px"
                                            v-model="queryParams.${column.javaField}"
                                            type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="选择${comment}">
                            </el-date-picker>
                        </el-form-item>
                    #end
                #end
            #end
            <el-form-item>
                <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                        type="primary"
                        icon="el-icon-plus"
                        size="mini"
                        @click="handleAdd"
                        v-hasPermi="['${moduleName}:${businessName}:add']"
                >新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="success"
                        icon="el-icon-edit"
                        size="mini"
                        :disabled="single"
                        @click="handleUpdate"
                        v-hasPermi="['${moduleName}:${businessName}:edit']"
                >修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="danger"
                        icon="el-icon-delete"
                        size="mini"
                        :disabled="multiple"
                        @click="handleDelete"
                        v-hasPermi="['${moduleName}:${businessName}:remove']"
                >删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="warning"
                        icon="el-icon-download"
                        size="mini"
                        @click="handleExport"
                        v-hasPermi="['${moduleName}:${businessName}:export']"
                >导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="${businessName}List" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            #foreach($column in $columns)
                #set($javaField=$column.javaField)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
                #if($column.pk)
                    <el-table-column label="${comment}" align="center" prop="${javaField}" />
                #elseif($column.list && $column.htmlType == "datetime")
                    <el-table-column label="${comment}" align="center" prop="${javaField}" width="180">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.${javaField}, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                #elseif($column.list && "" != $column.dictType)
                    <el-table-column label="${comment}" align="center" prop="${javaField}" :formatter="${javaField}Format" />
                #elseif($column.list && "" != $javaField)
                    <el-table-column label="${comment}" align="center" prop="${javaField}" />
                #end
            #end
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click="handleUpdate(scope.row)"
                            v-hasPermi="['${moduleName}:${businessName}:edit']"
                    >修改</el-button>
                    <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-delete"
                            @click="handleDelete(scope.row)"
                            v-hasPermi="['${moduleName}:${businessName}:remove']"
                    >删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="total>0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
        />

        <!-- 添加或修改${functionName}对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                #foreach($column in $columns)
                    #set($field=$column.javaField)
                    #if($column.insert && !$column.pk)
                        #if(($column.usableColumn) || (!$column.superColumn))
                            #set($parentheseIndex=$column.columnComment.indexOf("（"))
                            #if($parentheseIndex != -1)
                                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                            #else
                                #set($comment=$column.columnComment)
                            #end
                            #set($dictType=$column.dictType)
                            #if($column.htmlType == "input")
                                <el-form-item label="${comment}" prop="${field}">
                                    <el-input v-model="form.${field}" placeholder="请输入${comment}" />
                                </el-form-item>
                            #elseif($column.htmlType == "uploadImage")
                                <el-form-item label="${comment}">
                                    <uploadImage v-model="form.${field}"/>
                                </el-form-item>
                            #elseif($column.htmlType == "editor")
                                <el-form-item label="${comment}">
                                    <editor v-model="form.${field}" :min-height="192"/>
                                </el-form-item>
                            #elseif($column.htmlType == "select" && "" != $dictType)
                                <el-form-item label="${comment}" prop="${field}">
                                    <el-select v-model="form.${field}" placeholder="请选择${comment}">
                                        <el-option
                                                v-for="dict in ${field}Options"
                                                :key="dict.dictValue"
                                                :label="dict.dictLabel"
                                                #if($column.javaType == "Integer" || $column.javaType == "Long"):value="parseInt(dict.dictValue)"#else:value="dict.dictValue"#end

                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            #elseif($column.htmlType == "select" && $dictType)
                                <el-form-item label="${comment}" prop="${field}">
                                    <el-select v-model="form.${field}" placeholder="请选择${comment}">
                                        <el-option label="请选择字典生成" value="" />
                                    </el-select>
                                </el-form-item>
                            #elseif($column.htmlType == "checkbox" && "" != $dictType)
                                <el-form-item label="${comment}">
                                    <el-checkbox-group v-model="form.${field}">
                                        <el-checkbox
                                                v-for="dict in ${field}Options"
                                                :key="dict.dictValue"
                                                :label="dict.dictValue">
                                            {{dict.dictLabel}}
                                        </el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            #elseif($column.htmlType == "checkbox" && $dictType)
                                <el-form-item label="${comment}">
                                    <el-checkbox-group v-model="form.${field}">
                                        <el-checkbox>请选择字典生成</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            #elseif($column.htmlType == "radio" && "" != $dictType)
                                <el-form-item label="${comment}">
                                    <el-radio-group v-model="form.${field}">
                                        <el-radio
                                                v-for="dict in ${field}Options"
                                                :key="dict.dictValue"
                                                #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.dictValue)"#else:label="dict.dictValue"#end

                                        >{{dict.dictLabel}}</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            #elseif($column.htmlType == "radio" && $dictType)
                                <el-form-item label="${comment}">
                                    <el-radio-group v-model="form.${field}">
                                        <el-radio label="1">请选择字典生成</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            #elseif($column.htmlType == "datetime")
                                <el-form-item label="${comment}" prop="${field}">
                                    <el-date-picker clearable size="small" style="width: 200px"
                                                    v-model="form.${field}"
                                                    type="date"
                                                    value-format="yyyy-MM-dd"
                                                    placeholder="选择${comment}">
                                    </el-date-picker>
                                </el-form-item>
                            #elseif($column.htmlType == "textarea")
                                <el-form-item label="${comment}" prop="${field}">
                                    <el-input v-model="form.${field}" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                            #end
                        #end
                    #end
                #end
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { list${BusinessName}, get${BusinessName}, del${BusinessName}, add${BusinessName}, update${BusinessName}, export${BusinessName} } from "@/api/${moduleName}/${businessName}";
        #foreach($column in $columns)
            #if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "uploadImage")
            import UploadImage from '@/components/UploadImage';
                #break
            #end
        #end
        #foreach($column in $columns)
            #if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "editor")
            import Editor from '@/components/Editor';
                #break
            #end
        #end

    export default {
        name: "${BusinessName}",
        components: {
            #foreach($column in $columns)
                #if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "uploadImage")
                    UploadImage,
                    #break
                #end
            #end
            #foreach($column in $columns)
                #if($column.insert && !$column.superColumn && !$column.pk && $column.htmlType == "editor")
                    Editor,
                    #break
                #end
            #end
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // ${functionName}表格数据
                    ${businessName}List: [],
                // 弹出层标题
                title: "",
                // 是否显示弹出层
                open: false,
                #foreach ($column in $columns)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if(${column.dictType} != '')
                        // $comment字典
                            ${column.javaField}Options: [],
                    #end
                #end
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
            #foreach ($column in $columns)
                #if($column.query)
                    $column.javaField: null#if($velocityCount != $columns.size()),#end

                #end
            #end
        },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                #foreach ($column in $columns)
                    #if($column.required)
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        $column.javaField: [
                        { required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select")"change"#else"blur"#end }
                    ]#if($velocityCount != $columns.size()),#end

                    #end
                #end
            }
        };
        },
        created() {
            this.getList();
            #foreach ($column in $columns)
                #if(${column.dictType} != '')
                    this.getDicts("${column.dictType}").then(response => {
                        this.${column.javaField}Options = response.data;
                    });
                #end
            #end
        },
        methods: {
            /** 查询${functionName}列表 */
            getList() {
                this.loading = true;
                list${BusinessName}(this.queryParams).then(response => {
                    this.${businessName}List = response.rows;
                    this.total = response.total;
                    this.loading = false;
                });
            },
            #foreach ($column in $columns)
                #if(${column.dictType} != '')
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    // $comment字典翻译
                        ${column.javaField}Format(row, column) {
                        return this.selectDictLabel#if($column.htmlType == "checkbox")s#end(this.${column.javaField}Options, row.${column.javaField});
                    },
                #end
            #end
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                #foreach ($column in $columns)
                    #if($column.htmlType == "radio")
                        $column.javaField: #if($column.javaType == "Integer" || $column.javaType == "Long")0#else"0"#end#if($velocityCount != $columns.size()),#end

                    #elseif($column.htmlType == "checkbox")
                        $column.javaField: []#if($velocityCount != $columns.size()),#end

                    #else
                        $column.javaField: null#if($velocityCount != $columns.size()),#end

                    #end
                #end
            };
                this.resetForm("form");
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm("queryForm");
                this.handleQuery();
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.${pkColumn.javaField})
                this.single = selection.length!==1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset();
                this.open = true;
                this.title = "添加${functionName}";
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset();
                const ${pkColumn.javaField} = row.${pkColumn.javaField} || this.ids
                get${BusinessName}(${pkColumn.javaField}).then(response => {
                    this.form = response.data;
                    #foreach ($column in $columns)
                        #if($column.htmlType == "checkbox")
                            this.form.$column.javaField = this.form.${column.javaField}.split(",");
                        #end
                    #end
                    this.open = true;
                    this.title = "修改${functionName}";
                });
            },
            /** 提交按钮 */
            submitForm() {
                this.#[[$]]#refs["form"].validate(valid => {
                    if (valid) {
                        #foreach ($column in $columns)
                            #if($column.htmlType == "checkbox")
                                this.form.$column.javaField = this.form.${column.javaField}.join(",");
                            #end
                        #end
                        if (this.form.${pkColumn.javaField} != null) {
                            update${BusinessName}(this.form).then(response => {
                                this.msgSuccess("修改成功");
                                this.open = false;
                                this.getList();
                            });
                        } else {
                            add${BusinessName}(this.form).then(response => {
                                this.msgSuccess("新增成功");
                                this.open = false;
                                this.getList();
                            });
                        }
                    }
                });
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const ${pkColumn.javaField}s = row.${pkColumn.javaField} || this.ids;
                this.$confirm('是否确认删除${functionName}编号为"' + ${pkColumn.javaField}s + '"的数据项?', "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(function() {
                    return del${BusinessName}(${pkColumn.javaField}s);
                }).then(() => {
                    this.getList();
                    this.msgSuccess("删除成功");
                })
            },
            /** 导出按钮操作 */
            handleExport() {
                const queryParams = this.queryParams;
                this.$confirm('是否确认导出所有${functionName}数据项?', "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(function() {
                    return export${BusinessName}(queryParams);
                }).then(response => {
                    this.download(response.msg);
                })
            }
        }
    };
</script>
