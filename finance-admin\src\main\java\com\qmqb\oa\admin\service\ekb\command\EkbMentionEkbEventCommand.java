package com.qmqb.oa.admin.service.ekb.command;

import com.hzed.structure.common.api.ResultCode;
import com.hzed.structure.tool.api.ApiResponse;
import com.qmqb.oa.admin.domain.dto.EkbEventCommandDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.common.enums.EkbActionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description ekb 被@ 事件
 * @Date 2024\10\12 0012 10:50
 * @Version 1.0
 */

@Slf4j
@Service(value = "ekbMentionCommand")
public class EkbMentionEkbEventCommand extends AbstractEkbEventCommand<EkbReceiveEkbEventRequest, EkbEventCommandDTO> {

    @Override
    public boolean validation() {
        if (!super.checkBaseParams()) {
            return false;
        }

        if (!getParamObject().getAction().equals(EkbActionEnum.FREEFLOW_MENTION.getAction())) {
            return false;
        }

        return true;
    }

    @Override
    public ApiResponse<EkbEventCommandDTO> execute() {
        if (!validation()) {
            return ApiResponse.fail("mention事件参数校验失败");
        }
        if (eventHasExecute()) {
            return ApiResponse.success(ResultCode.SUCCESS, "ekb mention event deal success");
        }

        //更新易快报消息通知记录表
        boolean updateResult = sendNotifyMsg();
        ApiResponse apiResponse = null;
        if (updateResult) {
            apiResponse = ApiResponse.success(ResultCode.SUCCESS, "ekb mention event deal success");
        } else {
            apiResponse = ApiResponse.fail();
        }
        //设置处理后的事件传输实体
        apiResponse.setData(getEkbEventCommandDTO());
        return apiResponse;
    }
}
