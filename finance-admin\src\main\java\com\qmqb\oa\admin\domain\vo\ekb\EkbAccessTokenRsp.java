package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

/**
 * 易快报获取授权Rsp
 * <AUTHOR>
 * @date 2024/7/17
 */
@Data
public class EkbAccessTokenRsp {
    /**
     * 授权码，后续所有模块开发需要依赖此返回值
     */
    private String accessToken;
    /**
     * 调用【刷新授权】接口时需要传的token
     */
    private String refreshToken;
    /**
     * 授权码过期日期时间戳(默认2小时后到期)
     */
    private String expireTime;

    /**
     * 企业ID
     */
    private String corporationId;
}
