package com.qmqb.oa.admin.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingTalkMsg {

    @JsonProperty("voice")
    private VoiceDTO voice;
    @JsonProperty("image")
    private ImageDTO image;
    @JsonProperty("oa")
    private OaDTO oa;
    @JsonProperty("file")
    private FileDTO file;
    @JsonProperty("action_card")
    private ActionCardDTO actionCard;
    @JsonProperty("link")
    private LinkDTO link;
    @JsonProperty("markdown")
    private MarkdownDTO markdown;
    @JsonProperty("text")
    private TextDTO text;
    @JsonProperty("msgtype")
    private String msgtype;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VoiceDTO {
        @JsonProperty("duration")
        private String duration;
        @JsonProperty("media_id")
        private String mediaId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageDTO {
        @JsonProperty("media_id")
        private String mediaId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OaDTO {
        @JsonProperty("head")
        private HeadDTO head;
        @JsonProperty("pc_message_url")
        private String pcMessageUrl;
        @JsonProperty("status_bar")
        private StatusBarDTO statusBar;
        @JsonProperty("body")
        private BodyDTO body;
        @JsonProperty("message_url")
        private String messageUrl;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class HeadDTO {
            @JsonProperty("bgcolor")
            private String bgcolor;
            @JsonProperty("text")
            private String text;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class StatusBarDTO {
            @JsonProperty("status_value")
            private String statusValue;
            @JsonProperty("status_bg")
            private String statusBg;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BodyDTO {
            @JsonProperty("file_count")
            private String fileCount;
            @JsonProperty("image")
            private String image;
            @JsonProperty("form")
            private FormDTO form;
            @JsonProperty("author")
            private String author;
            @JsonProperty("rich")
            private RichDTO rich;
            @JsonProperty("title")
            private String title;
            @JsonProperty("content")
            private String content;

            @Data
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public static class FormDTO {
                @JsonProperty("value")
                private String value;
                @JsonProperty("key")
                private String key;
            }

            @Data
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RichDTO {
                @JsonProperty("unit")
                private String unit;
                @JsonProperty("num")
                private String num;
            }
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileDTO {
        @JsonProperty("media_id")
        private String mediaId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionCardDTO {
        @JsonProperty("btn_json_list")
        private BtnJsonListDTO btnJsonList;
        @JsonProperty("single_url")
        private String singleUrl;
        @JsonProperty("btn_orientation")
        private String btnOrientation;
        @JsonProperty("single_title")
        private String singleTitle;
        @JsonProperty("markdown")
        private String markdown;
        @JsonProperty("title")
        private String title;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BtnJsonListDTO {
            @JsonProperty("action_url")
            private String actionUrl;
            @JsonProperty("title")
            private String title;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LinkDTO {
        @JsonProperty("picUrl")
        private String picUrl;
        @JsonProperty("messageUrl")
        private String messageUrl;
        @JsonProperty("text")
        private String text;
        @JsonProperty("title")
        private String title;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarkdownDTO {
        @JsonProperty("text")
        private String text;
        @JsonProperty("title")
        private String title;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TextDTO {
        @JsonProperty("content")
        private String content;
    }
}
