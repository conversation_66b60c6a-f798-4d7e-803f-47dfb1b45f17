package com.qmqb.oa.admin.domain.request.internal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description ekbding 消息任务执行结果传输实体
 * @Date 2024\10\24 0024 14:32
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EkbDingTodoResultDTO {
    /**
     * @see com.qmqb.oa.common.enums.EkbFlowStageNameEnums
     * 任务处理结果 开发 尚未提交 拒绝 完成 已删除
     */
    private String taskProcessedResult;



    /**
     * 最近一次更新结果执行的消息事件
     */
    private String msgAction;

}
