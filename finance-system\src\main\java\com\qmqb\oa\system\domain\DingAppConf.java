package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钉钉app应用配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_ding_app_conf")
public class DingAppConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Integer id;

    /**
     * H5微应用名称
     */
    private String dingH5AppName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司主键id: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    private Integer companyId;

    /**
     * 公司corpId
     */
    private String dingCorpId;

    /**
     * 企业内部应用AgentId
     */
    private Long dingAgentId;

    /**
     * 钉钉H5微应用appKey
     */
    private String dingAppKey;

    /**
     * 钉钉H5微应用appSecret
     */
    private String dingAppSecret;

    /**
     * 合作方类型 1-易快报
     */
    private String partnerType;

    /**
     * 合作方appKey
     */
    private String partnerAppKey;

    /**
     * 合作方appSecret
     */
    private String partnerAppSecret;

    /**
     * 钉钉H5微应用开关：0-关闭；1-开启
     */
    private Integer appSwitch;

    /**
     * 公司优先级
     */
    private Integer companyPriority;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
