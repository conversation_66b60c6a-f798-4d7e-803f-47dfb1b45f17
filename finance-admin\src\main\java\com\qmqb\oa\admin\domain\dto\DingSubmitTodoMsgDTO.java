package com.qmqb.oa.admin.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 钉钉提交待办消息接口请求实体
 * <AUTHOR>
 * @date 2024/10/11
 */
@Data
@Accessors(chain = true)
@Builder
public class DingSubmitTodoMsgDTO {

    /**
     * 易快报单据id
     */
    private String ekbFlowId;

    /**
     * 易快报消息id
     */
    private String messageId;

    /**
     * 易快报userId
     */
    private String ekbUserId;
    /**
     * 我方公司主体
     */
    private Integer companyId;
    /**
     * 钉钉待办消息id
     */
    private String sourceId;
    /**
     * 待办接收人的unionId
     */
    private String recipientUnionId;
    /**
     * 待办提交人的unionId
     */
    private String submitUnionId;
    /**
     * 待办内容
     */
    private String content;

    /**
     * 任务描述
     */
    private Map<String, String> desc;
}
