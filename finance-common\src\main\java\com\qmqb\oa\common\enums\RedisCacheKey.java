package com.qmqb.oa.common.enums;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Redis缓存key
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
public enum RedisCacheKey {
    /**
     * 钉钉API token
     */
    DINGTALK_OAPI_ACCESS_TOKEN("dingtalk_oapi_access_token:%s", 7000, TimeUnit.SECONDS),
    /**
     * 钉钉API token
     */
    DINGTALK_API_ACCESS_TOKEN("dingtalk_api_access_token:%s", 7000, TimeUnit.SECONDS),
    /**
     * 付款申请上次执行时间
     */
    PAY_APPLY_LAST_QUERY_TIME("pay_apply_last_query_time:%s", 10, TimeUnit.DAYS),
    /**
     * 餐费车费发票上次执行时间
     */
    MEAL_LAST_QUERY_TIME("meal_last_query_time:%s", 10, TimeUnit.DAYS),
    /**
     * 团建费发票上次执行时间
     */
    TB_LAST_QUERY_TIME("tb_last_query_time:%s", 10, TimeUnit.DAYS),
    /**
     * 报销凭证上次执行时间
     */
    VOUCHER_LAST_QUERY_TIME("voucher_last_query_time:%s", 10, TimeUnit.DAYS),
    /**
     * 入职审批上次执行时间
     */
    ENTRY_APPROVAL_LAST_QUERY_TIME("entry_approval_last_query_time:%s", 10, TimeUnit.DAYS),

    ;

    private final String key;
    private final int timeout;
    private final TimeUnit timeUnit;

    public String getKey() {
        return key;
    }

    public int getTimeout() {
        return timeout;
    }

    public TimeUnit getTimeUnit() {
        return timeUnit;
    }

    RedisCacheKey(String key, int timeout, TimeUnit timeUnit) {
        this.key = key;
        this.timeout = timeout;
        this.timeUnit = timeUnit;
    }

    /**
     * 获取key, %s占位符
     *
     * @param placeholder
     * @return
     */
    public String getKey(Object... placeholder) {
        return String.format(this.key, placeholder);
    }
}
