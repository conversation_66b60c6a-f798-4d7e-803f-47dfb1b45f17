package com.qmqb.oa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.InvoiceOcrRecord;

import java.util.List;

/**
 * <p>
 * 发票OCR识别记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
public interface InvoiceOcrRecordMapper extends BaseMapper<InvoiceOcrRecord> {

    /**
     * 查询发票识别记录列表
     *
     * @param invoiceOcrRecord 发票识别记录
     * @return 发票识别记录集合
     */
    List<InvoiceOcrRecord> selectInvoiceOcrRecordList(InvoiceOcrRecord invoiceOcrRecord);

}
