package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.oa.common.annotation.Excel;
import com.qmqb.oa.common.core.domain.BaseEntity;
import com.qmqb.oa.common.enums.ProcessType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 审批实例处理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_process_record")
public class ProcessRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 审批实例ID
     */
    @Excel(name = "审批实例ID")
    private String processInstanceId;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 审批实例业务编号
     */
    @Excel(name = "审批实例业务编号")
    private String businessId;

    /**
     * 发起人
     */
    @Excel(name = "发起人")
    private String originatorUserId;

    /**
     * 发起人
     */
    @Excel(name = "发起人")
    private String originatorUserName;

    /**
     * 发起部门
     */
    @Excel(name = "发起部门")
    private String originatorDeptId;

    /**
     * 发起部门
     */
    @Excel(name = "发起部门")
    private String originatorDeptName;

    /**
     * 机器人是否审批通过: 0-否, 1-是
     */
    @Excel(name = "机器人是否审批通过", dictType = "int_yes_no")
    private Integer isPass;

    /**
     * 处理结果
     */
    @Excel(name = "处理结果")
    private String result;

    /**
     * 流程类型
     * @see ProcessType
     */
    private Integer processType;

    /**
     * 流程类型
     * @see ProcessType
     */
    @TableField(exist = false)
    private List<Integer> processTypes;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    @Excel(name = "主体", dictType = "finance_tenant_id")
    private Integer tenantId;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @TableLogic
    private Integer isDeleted;


}
