package com.qmqb.oa.common.utils.http;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.qmqb.oa.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;

/**
 * 提供基于RestTemplate的http服务
 *
 * <AUTHOR>
 * @date 2024/7/18
 */
@Slf4j
@Service
public class HttpRestService {
    private static final HttpHeaders HEADERS = new HttpHeaders();

    @Autowired
    private RestTemplate restTemplate;

    static {
        HEADERS.set("Content-type", "application/json; charset=utf-8");
        HEADERS.setAccept(Lists.newArrayList(MediaType.APPLICATION_JSON));
        HEADERS.setAcceptCharset(Lists.newArrayList(Charset.forName("UTF-8")));
    }

    /**
     * POST请求
     *
     * @param url     请求地址
     * @param jsonStr 请求的JSON数据
     * @param clazz   期望返回的JSON数据封装类的Class对象
     * @param logKey  日志打印关键字
     * @param <T>     期望返回的对象
     * @return
     */
    public <T> T post(String url, String jsonStr, Class<T> clazz, String logKey) {
        HttpEntity<String> entity = new HttpEntity<>(jsonStr, HEADERS);
        ResponseEntity<String> responseEntity = null;
        try {
            log.info("{}开始推送，请求路由：{}，请求报文：{}", logKey, url, jsonStr);
            responseEntity = restTemplate.postForEntity(url, entity, String.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                String bodyStr = responseEntity.getBody();
                log.info("{}响应报文：{}", logKey, bodyStr);
                T response = null;
                response = JSON.parseObject(bodyStr, clazz);
                return response;
            } else {
                throw new ServiceException(String.format("%s失败，失败原因：%s", logKey, responseEntity));
            }
        } catch (Exception e) {
            log.error(logKey + "异常", e);
            throw new ServiceException(String.format("%s异常，失败原因：%s", logKey, e));
        }
    }


    /**
     * GET请求
     *
     * @param url    请求地址
     * @param clazz  期望返回的JSON数据封装类的Class对象
     * @param logKey 日志打印关键字
     * @param <T>    期望返回的对象
     * @return
     */
    public <T> T get(String url, Class<T> clazz, String logKey) {
        ResponseEntity<String> responseEntity = null;
        try {
            log.info("{}开始推送，请求路由：{}", logKey, url);
            responseEntity = restTemplate.getForEntity(url, String.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                String bodyStr = responseEntity.getBody();
                log.info("{}响应报文：{}", logKey, bodyStr);
                T response = null;
                response = JSON.parseObject(bodyStr, clazz);
                return response;
            } else {
                throw new ServiceException(String.format("%s失败，失败原因：%s", logKey, responseEntity));
            }
        } catch (Exception e) {
            log.error(logKey + "异常", e);
            throw new ServiceException(String.format("%s异常，失败原因：%s", logKey, e));
        }
    }

    /**
     * PUT请求
     *
     * @param url     请求地址
     * @param jsonStr 请求的JSON数据
     * @param clazz   期望返回的JSON数据封装类的Class对象
     * @param logKey  日志打印关键字
     * @param reqIsLog 是否打印请求报文  
     * @param <T>     期望返回的对象
     * @return
     */
    public <T> T put(String url, String jsonStr, Class<T> clazz, String logKey, Boolean reqIsLog) {
        HttpEntity<String> entity = new HttpEntity<>(jsonStr, HEADERS);
        ResponseEntity<String> responseEntity = null;
        try {
            if (reqIsLog) {
                log.info("{}开始推送，请求路由：{}，请求报文：{}", logKey, url, jsonStr);
            } else {
                log.info("{}开始推送，请求路由：{}", logKey, url);
            }
            responseEntity = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode()) 
                    // 204表示请求成功，但是没有返回数据
                    || HttpStatus.NO_CONTENT.equals(responseEntity.getStatusCode())) {
                String bodyStr = responseEntity.getBody();
                log.info("{}响应报文：{}", logKey, bodyStr);
                T response = null;
                response = JSON.parseObject(bodyStr, clazz);
                return response;
            } else {
                throw new ServiceException(String.format("%s失败，失败原因：%s", logKey, responseEntity));
            }
        } catch (Exception e) {
            log.error(logKey + "异常", e);
            throw new ServiceException(String.format("%s异常，失败原因：%s", logKey, e));
        }
    }


}
