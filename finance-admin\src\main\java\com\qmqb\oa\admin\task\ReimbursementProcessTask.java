package com.qmqb.oa.admin.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Month;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiOcrStructuredRecognizeResponse;
import com.dingtalk.api.response.OapiProcessinstanceFileUrlGetResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.common.util.NumberUtil;
import com.hzed.structure.oss.AliOssTemplate;
import com.hzed.structure.oss.domain.OssResult;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.api.client.DingTalkApiClient;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.config.DingTalkConfig;
import com.qmqb.oa.admin.config.InvoiceConfig;
import com.qmqb.oa.admin.domain.*;
import com.qmqb.oa.admin.service.ApproveTimeService;
import com.qmqb.oa.admin.support.FinancialRobotWatch;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.constant.*;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.domain.entity.SysDept;
import com.qmqb.oa.common.core.domain.entity.SysDictData;
import com.qmqb.oa.common.core.redis.RedisCache;
import com.qmqb.oa.common.enums.*;
import com.qmqb.oa.common.utils.MathUtil;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.common.utils.file.FileUtils;
import com.qmqb.oa.common.utils.file.MimeTypeUtils;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.system.domain.*;
import com.qmqb.oa.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 报销流程
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Slf4j
@Component("reimbursementProcessTask")
@RequiredArgsConstructor
public class ReimbursementProcessTask {

    /**
     * 发票类型
     */
    private static final String PLAIN_INVOICE = "普通发票";
    private static final String SPECIAL_INVOICE = "专用发票";
    private static final String WRITE_INVOICE = "手写发票";

    /**
     * 专票联次
     */
    private static final String INVOICE_SHEET = "发票联";
    private static final String DEDUCTING_SHEET = "抵扣联";


    /**
     * 凭证编号填充常量
     */
    private static final Character VOUCHER_NUMBER_FILL_CHAR = '0';
    private static final Integer VOUCHER_NUMBER_LENGTH = 4;

    /**
     * 摘要字符串
     */
    private static final String SUMMARY = "%s、%s、%s";

    /**
     * 附件格式
     */
    private static final String FILE_TYPE_ZIP = "zip";
    private static final String FILE_TYPE_XLSX = "xlsx";
    private static final String FILE_TYPE_XLS = "xls";

    /**
     * 审批评论
     */
    private static final String INVOICE_ERROR = "发票号码：%s，文件名：%s" + "\n";
    private static final String FILE_TYPE_ERROR = "发票附件压缩格式有误,请上传zip格式压缩包";
    private static final String INVOICE_REUSE = "\t" + "%s" + "." + "已使用过的发票不可重复使用" + "\n";
    private static final String INVOICE_LACK = "\t" + "%s" + "." + "未识别出开票日期或开票日期解析失败" + "\n";
    private static final String INVOICE_DATE_OUT_OF_RANGE = "\t" + "%s" + "." + "开票日期超过期限" + "\n";
    private static final String PAY_NAME_ERROR = "\t" + "%s" + "." + "受票方名称有误" + "\n";
    private static final String PAY_NAME_EMPTY = "\t" + "%s" + "." + "受票方名称识别为空" + "\n";
    private static final String TAX_NUMBER_ERROR = "\t" + "%s" + "." + "受票方税号有误" + "\n";
    private static final String PAY_ADDR_PHONE_ERROR = "\t" + "%s" + "." + "受票方地址、电话有误" + "\n";
    private static final String PAY_BANK_ACCOUNT_ERROR = "\t" + "%s" + "." + "受票方开户行、账号有误" + "\n";
    private static final String PAY_INFO_ERROR = "\t" + "%s" + "." + "受票方信息不完整" + "\n";
    private static final String SERVICE_TYPE_ERROR = "\t" + "%s" + "." + "货物或应税劳务、服务名称/项目名称内容有误" + "\n";
    /**
     * 钉钉根部门ID
     */
    private static final Long ROOT_DEPT_ID = 1L;

    /**
     * 2023新版发票号码长度
     */
    private static final int INVOICE_CODE_LENGTH_2023 = 20;

    private final DingTalkOApiClient dingTalkOApiClient;
    private final DingTalkApiClient dingTalkApiClient;
    private final DingTalkConfig dingTalkConfig;
    private final InvoiceConfig invoiceConfig;
    private final AliOssTemplate aliOssTemplate;
    private final InvoiceOcrRecordService invoiceOcrRecordService;
    private final SysDeptService sysDeptService;
    private final SubjectService subjectService;
    private final ReimbursementVoucherService reimbursementVoucherService;
    private final ProcessRecordService processRecordService;
    private final SysDictDataService sysDictDataService;
    private final RedisCache redisCache;
    private final ApproveTimeService approveTimeService;
    private final SysDictTypeService sysDictTypeService;
    private final FinancialRobotWatch financialRobotWatch;
    private final EmployeeSubjectService employeeSubjectService;

    /**
     * 餐费车费发票信息核验
     */
    public void meal(Integer tenantId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "餐费/车费审批");
            // 查询审批实例ID列表
            Map<String, Long> time = approveTimeService.approveListTime();
            Long beginTime = time.get(ApproveTimeService.BEGIN_TIME);
            Long endTime = time.get(ApproveTimeService.END_TIME);
            String key = RedisCacheKey.MEAL_LAST_QUERY_TIME.getKey(tenantId);
            Long lastQueryTime = redisCache.getCacheObject(key);
            if (Objects.nonNull(lastQueryTime)) {
                beginTime = lastQueryTime;
            }
            log.info("查询的开始时间：{}, 结束时间：{}", beginTime, endTime);
            List<String> processInstanceIds = dingTalkOApiClient.listProcessInstanceIds(dingTalkConfig.getMeal(tenantId), null, beginTime, endTime);
            if (CollectionUtil.isEmpty(processInstanceIds)) {
                redisCache.setCacheObject(key, endTime);
                log.info("获取餐费/车费审批实例ID列表为空,不执行此次任务");
                return;
            }
            log.info("本次查询餐费个数:{}", processInstanceIds.size());
            // 执行审批实例操作
            processInstanceIds.forEach(processInstanceId -> {
                try {
                    executeProcess(processInstanceId, ProcessType.MEAL);
                } catch (Exception e) {
                    // 钉钉预警
                    financialRobotWatch.warn(processInstanceId, e);
                    log.error("执行审批实例操作异常", e);
                }
            });
            redisCache.setCacheObject(key, endTime);
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }

    /**
     * 团建费发票信息核验
     */
    public void tb(Integer tenantId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "团建费审批");
            // 查询审批实例ID列表
            Map<String, Long> time = approveTimeService.approveListTime();
            Long beginTime = time.get(ApproveTimeService.BEGIN_TIME);
            Long endTime = time.get(ApproveTimeService.END_TIME);
            String key = RedisCacheKey.TB_LAST_QUERY_TIME.getKey(tenantId);
            Long lastQueryTime = redisCache.getCacheObject(key);
            if (Objects.nonNull(lastQueryTime)) {
                beginTime = lastQueryTime;
            }
            log.info("查询的开始时间：{}, 结束时间：{}", beginTime, endTime);
            List<String> processInstanceIds = dingTalkOApiClient.listProcessInstanceIds(dingTalkConfig.getTb(tenantId), null, beginTime, endTime);
            if (CollectionUtil.isEmpty(processInstanceIds)) {
                redisCache.setCacheObject(key, endTime);
                log.info("获取团建费审批实例ID列表为空,不执行此次任务");
                return;
            }
            log.info("本次查询团建费个数:{}", processInstanceIds.size());
            // 执行审批实例操作
            processInstanceIds.forEach(processInstanceId -> {
                try {
                    executeProcess(processInstanceId, ProcessType.TB);
                } catch (Exception e) {
                    // 钉钉预警
                    financialRobotWatch.warn(processInstanceId, e);
                    log.error("执行审批实例操作异常", e);
                }
            });
            redisCache.setCacheObject(key, endTime);
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }


    /**
     * 生成报销凭证数据
     * 查询两个月内的数据，发起审批到审批结束时间跨度较大
     */
    public void voucher(Integer tenantId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "生成报销凭证");
            Date now = new Date();
            Long beginTime = DateUtil.offsetMonth(DateUtil.beginOfDay(now), -2).getTime();
            Long endTime = DateUtil.endOfDay(now).getTime();
            // 团建费
            List<String> tbProcessInstanceIds = dingTalkApiClient.listProcessInstanceIds(dingTalkConfig.getTb(tenantId), beginTime, endTime, null,
                    Collections.emptyList(), Collections.singletonList(ProcessStatus.COMPLETED));
            if (CollectionUtil.isNotEmpty(tbProcessInstanceIds)) {
                log.info("本次查询团建费个数:{}", tbProcessInstanceIds.size());
                generateReimbursementVoucher(tbProcessInstanceIds);
            }
            // 餐费/车费
            List<String> mealProcessInstanceIds = dingTalkApiClient.listProcessInstanceIds(dingTalkConfig.getMeal(tenantId), beginTime, endTime, null,
                    Collections.emptyList(), Collections.singletonList(ProcessStatus.COMPLETED));
            if (CollectionUtil.isNotEmpty(mealProcessInstanceIds)) {
                log.info("本次查询餐费个数:{}", mealProcessInstanceIds.size());
                generateReimbursementVoucher(mealProcessInstanceIds);
            }
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }


    /**
     * 审批实例列表操作
     *
     * @param processInstanceId
     */
    public void executeProcess(String processInstanceId, ProcessType processType) {
        // 遍历实例列表，执行OCR及校验操作
        MdcUtil.putProcess(processInstanceId);
        ProcessRecord processRecord = processRecordService.getByProcessInstanceId(processInstanceId);
        if (Objects.nonNull(processRecord)) {
            log.info("该流程已处理过");
            return;
        }
        // 查询审批实例详情
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = dingTalkOApiClient.getProcessInstance(processInstanceId);
        // 审批实例业务编号
        String businessId = processInstance.getBusinessId();
        MdcUtil.putBizId(businessId);
        // 查询当前任务id
        Optional<OapiProcessinstanceGetResponse.TaskTopVo> taskOptional = processInstance.getTasks().stream()
                .filter(task -> ProcessStatus.RUNNING.equals(task.getTaskStatus()) && Approver.ROBOT_IDS.contains(task.getUserid())).findFirst();
        if (!taskOptional.isPresent()) {
            log.info("当前流程非财务机器人处理中状态");
            return;
        }

        log.info("开始处理审批流程,审批实例ID:{},审批编号:{}", processInstanceId, businessId);

        Long taskId = Long.valueOf(taskOptional.get().getTaskid());
        String userId = taskOptional.get().getUserid();

        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();

        // 获取表格信息域
        Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> tableFieldOptional = formComponentValues.stream()
                .filter(formComponentValueVo -> TableFieldId.TABLE_FIELD.equals(formComponentValueVo.getComponentType())).findFirst();
        if (!tableFieldOptional.isPresent()) {
            log.info("未获取到表格信息域");
            commonRefuse(processInstanceId, userId, taskId, "无效流程");
            return;
        }

        // 校验表格
        VerifyResult<List<TableData>> verifyTableFileResult = verifyTableFiled(processInstanceId, userId, taskId, tableFieldOptional.get());
        List<TableData> tableDataList = verifyTableFileResult.getData();
        if (CollUtil.isEmpty(tableDataList)) {
            saveProcessRecord(processInstance, processInstanceId, verifyTableFileResult.getResult(), BoolFlagEnum.NO, processType);
            return;
        }

        // 加班车费直接通过
        if (tableDataList.stream().allMatch(e -> "加班车费".equals(e.getType()))) {
            log.info("加班车费直接通过");
            commonAgree(processInstanceId, userId, taskId, "加班车费直接通过");
            return;
        }

        // 获取发票附件信息
        Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> invoiceFileComponentValueVoOptional = formComponentValues.stream()
                .filter(formComponentValueVo -> TableFieldId.DD_ATTACHMENT.equals(formComponentValueVo.getComponentType())
                        && StrUtil.contains(formComponentValueVo.getName(), "发票附件")).findFirst();
        if (!invoiceFileComponentValueVoOptional.isPresent()) {
            log.info("未获取到发票附件信息");
            commonRefuse(processInstanceId, userId, taskId, "未获取到发票附件信息");
            return;
        }

        // 校验附件
        VerifyResult<List<String>> verifyInvoiceAttachmentAndUploadResult = verifyInvoiceAttachmentAndUpload(processInstanceId, userId, taskId, invoiceFileComponentValueVoOptional.get());
        List<String> images = verifyInvoiceAttachmentAndUploadResult.getData();
        if (CollUtil.isEmpty(images)) {
            saveProcessRecord(processInstance, processInstanceId, verifyInvoiceAttachmentAndUploadResult.getResult(), BoolFlagEnum.NO, processType);
            return;
        }

        // 校验发票
        VerifyResult<List<InvoiceOcrRecord>> verifyInvoiceResult = verifyInvoice(processInstanceId, businessId, taskId, userId, tableDataList, images);

        // 加班餐费-校验公司主体
        if (ProcessType.MEAL.equals(processType)) {
            // 获取加班人员及天数附件信息
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> overtimeFileComponentValueVoOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> TableFieldId.DD_ATTACHMENT.equals(formComponentValueVo.getComponentType())
                            && StrUtil.contains(formComponentValueVo.getName(), "加班人员及天数附件")).findFirst();
            if (!overtimeFileComponentValueVoOptional.isPresent()) {
                log.info("未获取到加班人员及天数附件信息");
                commonRefuse(processInstanceId, userId, taskId, "未获取到加班人员及天数附件信息");
                return;
            }
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> company = formComponentValues.stream()
                    .filter(formComponentValueVo -> TableFieldId.TABLE_SELECT_FIELD.equals(formComponentValueVo.getComponentType())
                            && StrUtil.contains(formComponentValueVo.getName(), "付款公司")).findFirst();
            if (!company.isPresent()) {
                log.info("未获取到付款公司");
                commonRefuse(processInstanceId, userId, taskId, "未获取到付款公司");
                return;
            }
            // 校验附件
            VerifyResult<List<OvertimeExcel>> verifyOvertimeAttachmentAndUploadResult = verifyOvertimeAttachmentAndUpload(processInstanceId, userId, taskId, overtimeFileComponentValueVoOptional.get());
            List<OvertimeExcel> overtimeExcels = verifyOvertimeAttachmentAndUploadResult.getData();
            if (CollUtil.isEmpty(overtimeExcels)) {
                saveProcessRecord(processInstance, processInstanceId, verifyOvertimeAttachmentAndUploadResult.getResult(), BoolFlagEnum.NO, processType);
                return;
            }
            // 校验公司主体
            List<String> excelNames = overtimeExcels.stream().map(OvertimeExcel::getName).collect(Collectors.toList());
            if (CollUtil.isEmpty(excelNames)) {
                log.info("加班人员及天数附件，解析Excel内容失败，请检查模板！");
                commonRefuse(processInstanceId, userId, taskId, "加班人员及天数附件，解析Excel内容失败，请检查模板！");
                return;
            }
            List<EmployeeSubject> subjects = employeeSubjectService.listByNames(excelNames);
            if (overtimeExcels.size() != subjects.size()) {
                Collection<String> disjunction = CollUtil.disjunction(excelNames, subjects.stream().map(EmployeeSubject::getName).collect(Collectors.toList()));
                String names = StrUtil.join(",", disjunction);
                log.info("存在未知主体员工，请人工核实。{}", names);
                addComment(processInstanceId, userId,  "存在未知主体员工，请人工核实。" + names);
            }
            List<EmployeeSubject> differentSubjects = subjects.stream()
                    .filter(e -> !Objects.equals(Tenant.findByShortName(company.get().getValue()).getValue(), e.getCompany()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(differentSubjects)) {
                String names = StrUtil.join(",", differentSubjects.stream().map(EmployeeSubject::getName));
                log.info("主体校验不通过。{}", names);
                commonRefuse(processInstanceId, userId, taskId, "主体校验不通过。" + names);
                saveProcessRecord(processInstance, processInstanceId, "主体校验不通过。" + names, BoolFlagEnum.NO, processType);
                return;
            }
        }

        saveProcessRecord(processInstance, processInstanceId, verifyInvoiceResult.getResult(), BoolFlagEnum.YES, processType);
    }


    /**
     * 校验表格信息
     *
     * @param tableFiled
     * @return
     */
    public VerifyResult<List<TableData>> verifyTableFiled(String processInstanceId, String userId, Long taskId, OapiProcessinstanceGetResponse.FormComponentValueVo tableFiled) {
        // 获取表格报销日期范围
        String tableFieldValue = tableFiled.getValue();
        List<TableField> data = JSON.parseObject(tableFieldValue, new TypeReference<List<TableField>>() {
        });
        List<TableData> tableDataList = new ArrayList<>();
        VerifyResult<List<TableData>> result = new VerifyResult<>();
        result.setData(tableDataList);
        for (TableField field : data) {
            List<TableField.RowValue> forms = field.getRowValue();
            Optional<TableField.RowValue> departmentOpt = forms.stream().filter(form -> TableFieldId.TABLE_DEPARTMENT_FIELD.equals(form.getComponentType())).findFirst();
            if (!departmentOpt.isPresent()) {
                log.info("未获取到表格中的部门数据");
                commonRefuse(processInstanceId, userId, taskId, "未获取到表格中的部门数据");
                result.setResult("未获取到表格中的部门数据");
                break;
            }
            TableField.RowValue departmentVo = departmentOpt.get();
            // 部门
            String department = departmentVo.getValue();
            TableData tableData = new TableData();
            tableDataList.add(tableData);
            tableData.setDepartment(department);
            List<Map<String, String>> depts = JSON.parseObject(departmentVo.getExtendValue(), new TypeReference<List<Map<String, String>>>() {
            });
            if (depts.size() > 1) {
                log.info("存在多个部门数据");
                commonRefuse(processInstanceId, userId, taskId, "存在多个部门数据");
                result.setResult("存在多个部门数据");
                break;
            }
            String deptId = depts.get(0).get("id");
            tableData.setDeptId(deptId);
            SysDept sysDept = sysDeptService.selectDeptByDingTalkDeptId(deptId);
            if (Objects.isNull(sysDept)) {
                log.info("找不到对应部门");
                commonRefuse(processInstanceId, userId, taskId, "找不到对应部门,请选择正确的部门");
                result.setResult("找不到对应部门");
                break;
            }

            Optional<TableField.RowValue> dateRangeOpt = forms.stream().filter(form -> TableFieldId.TABLE_DATE_RANGE_FIELD.equals(form.getComponentType())).findFirst();
            if (!dateRangeOpt.isPresent()) {
                log.info("未获取到表格中的时间数据");
                commonRefuse(processInstanceId, userId, taskId, "未获取到表格中的时间数据");
                result.setResult("未获取到表格中的时间数据");
                break;
            }
            TableField.RowValue dateRangeVo = dateRangeOpt.get();
            // 开始时间, 结束时间
            List<String> dateRange = JSON.parseArray(dateRangeVo.getValue(), String.class);
            if (CollectionUtil.isEmpty(dateRange) || dateRange.stream().filter(StrUtil::isNotBlank).count() != 2) {
                log.info("表格中的报销日期不合法");
                commonRefuse(processInstanceId, userId, taskId, "表格中的报销日期不合法");
                result.setResult("表格中的报销日期不合法");
                break;
            }
            String beginDate = dateRange.get(0);
            String endDate = dateRange.get(1);
            DateTime begin = DateUtil.parse(beginDate, DatePattern.NORM_MONTH_PATTERN);
            DateTime end = DateUtil.parse(endDate, DatePattern.NORM_MONTH_PATTERN);
            if (DateUtil.compare(begin, end) != 0) {
                log.info("表格中的报销日期不合法");
                commonRefuse(processInstanceId, userId, taskId, "表格中的报销日期不是同一年月");
                result.setResult("表格中的报销日期不是同一年月");
                break;
            }
            tableData.setDate(begin);

            Optional<TableField.RowValue> selectOpt = forms.stream().filter(form -> TableFieldId.TABLE_SELECT_FIELD.equals(form.getComponentType())).findFirst();
            if (!selectOpt.isPresent()) {
                log.info("未获取到表格中的费用类别数据");
                commonRefuse(processInstanceId, userId, taskId, "未获取到表格中的费用类别数据");
                result.setResult("未获取到表格中的费用类别数据");
                break;
            }
            TableField.RowValue selectVo = selectOpt.get();
            // 费用类别
            String select = selectVo.getValue();
            tableData.setType(select);

            Optional<TableField.RowValue> moneyOpt = forms.stream().filter(form -> TableFieldId.TABLE_MONEY_FIELD.equals(form.getComponentType())).findFirst();
            if (!moneyOpt.isPresent()) {
                log.info("未获取到表格中的金额数据");
                commonRefuse(processInstanceId, userId, taskId, "未获取到表格中的金额数据");
                result.setResult("未获取到表格中的金额数据");
                break;
            }
            TableField.RowValue moneyVo = moneyOpt.get();
            // 金额
            String money = moneyVo.getValue();
            tableData.setAmount(money);
        }
        return result;
    }

    /**
     * 校验发票附件
     *
     * @param ddAttachment
     * @return
     */
    private VerifyResult<List<String>> verifyInvoiceAttachmentAndUpload(String processInstanceId, String userId, Long taskId, OapiProcessinstanceGetResponse.FormComponentValueVo ddAttachment) {
        String ddAttachmentValue = ddAttachment.getValue();
        List<Attachment> attachments = JSON.parseArray(ddAttachmentValue, Attachment.class);
        List<String> urls = new ArrayList<>();
        VerifyResult<List<String>> result = new VerifyResult<>();
        result.setData(urls);
        for (Attachment attachment : attachments) {
            // 文件格式校验
            if (!FILE_TYPE_ZIP.equals(attachment.getFileType())) {
                log.info("发票附件压缩格式有误");
                commonRefuse(processInstanceId, userId, taskId, FILE_TYPE_ERROR);
                result.setResult(FILE_TYPE_ERROR);
                break;
            }
            OapiProcessinstanceFileUrlGetResponse.AppSpaceResponse processFile = dingTalkOApiClient.getProcessFile(processInstanceId, attachment.getFileId());
            File downLoadFile = null;
            File folder = null;
            try {
                String fileName = IdUtil.fastSimpleUUID() + StringPool.UNDERSCORE + attachment.getFileName();
                downLoadFile = FileUtils.downloadFileFromUrl(processFile.getDownloadUri(), fileName);
                String suffix = FileUtil.getSuffix(fileName);
                if (!FILE_TYPE_ZIP.equals(suffix)) {
                    log.info("发票附件压缩格式有误");
                    commonRefuse(processInstanceId, userId, taskId, FILE_TYPE_ERROR);
                    result.setResult(FILE_TYPE_ERROR);
                    break;
                }
                // 解压缩
                List<File> images;
                try {
                    folder = ZipUtil.unzip(downLoadFile, CharsetUtil.CHARSET_GBK);
                    images = FileUtil.loopFiles(folder);
                } catch (UtilException | IORuntimeException e) {
                    log.error("文件解压异常", e);
                    commonRefuse(processInstanceId, userId, taskId, "文件解压异常,请重新压缩上传文件");
                    result.setResult("文件解压异常,请重新压缩上传文件");
                    break;
                }
                if (CollUtil.isEmpty(images)) {
                    log.info("文件夹为空");
                    commonRefuse(processInstanceId, userId, taskId, "未获取到发票文件,请检查压缩包文件存放格式是否有误");
                    result.setResult("未获取到发票文件，请检查压缩包文件存放格式是否有误");
                    break;
                }
                // 处理pdf类型的文件
                List<File> pdfFiles = images.stream().filter(file -> Objects.equals(MimeTypeUtils.PDF, FileTypeUtil.getType(file)))
                        .collect(Collectors.toList());
                images.removeAll(pdfFiles);
                List<File> disposedPdfFiles = pdfFiles.stream().map(file -> FileUtils.pdfToImage(file)).collect(Collectors.toList());
                images.addAll(disposedPdfFiles);

                // 处理ofd类型的文件
                List<File> ofdFiles = images.stream().filter(file -> Objects.equals(MimeTypeUtils.OFD, FileTypeUtil.getType(file)))
                        .collect(Collectors.toList());
                images.removeAll(ofdFiles);
                List<File> disposedOfdFiles = ofdFiles.stream().map(file -> FileUtils.ofdToImage(file)).collect(Collectors.toList());
                images.addAll(disposedOfdFiles);

                // 上传阿里云
                for (File file : images) {
                    String fileType = FileTypeUtil.getType(file);
                    if (Arrays.asList(MimeTypeUtils.IMAGE_EXTENSION).contains(fileType)) {
                        OssResult ossResult = aliOssTemplate.putFile(Constants.FILE_TYPE, file.getName(), file);
                        urls.add(ossResult.getAccessLink());
                    }
                }
            } catch (Exception e) {
                log.error("发票附件处理异常", e);
                commonRefuse(processInstanceId, userId, taskId, "发票附件处理异常,请重新压缩提交");
                result.setResult("发票附件处理异常,请重新压缩提交");
                break;
            } finally {
                FileUtil.del(downLoadFile);
                FileUtil.del(folder);
            }
        }
        return result;
    }

    /**
     * 加班人员及天数附件
     *
     * @param processInstanceId
     * @param userId
     * @param taskId
     * @param ddAttachment
     * @return
     */
    private VerifyResult<List<OvertimeExcel>> verifyOvertimeAttachmentAndUpload(String processInstanceId, String userId, Long taskId, OapiProcessinstanceGetResponse.FormComponentValueVo ddAttachment) {
        String ddAttachmentValue = ddAttachment.getValue();
        List<Attachment> attachments = JSON.parseArray(ddAttachmentValue, Attachment.class);
        List<OvertimeExcel> urls = new ArrayList<>();
        VerifyResult<List<OvertimeExcel>> result = new VerifyResult<>();
        result.setData(urls);
        for (Attachment attachment : attachments) {
            // 文件格式校验
            String fileType = attachment.getFileType();
            if (!StringUtil.equalsIgnoreCase(fileType, FILE_TYPE_XLSX) && !StringUtil.equalsIgnoreCase(fileType, FILE_TYPE_XLS)) {
                log.info("加班人员及天数附件格式有误");
                commonRefuse(processInstanceId, userId, taskId, FILE_TYPE_ERROR);
                result.setResult(FILE_TYPE_ERROR);
                break;
            }
            OapiProcessinstanceFileUrlGetResponse.AppSpaceResponse processFile = dingTalkOApiClient.getProcessFile(processInstanceId, attachment.getFileId());
            File downLoadFile = null;
            try {
                String fileName = IdUtil.fastSimpleUUID() + StringPool.UNDERSCORE + attachment.getFileName();
                downLoadFile = FileUtils.downloadFileFromUrl(processFile.getDownloadUri(), fileName);
                String suffix = FileUtil.getSuffix(fileName);
                if (!FILE_TYPE_XLSX.equals(suffix) && !FILE_TYPE_XLS.equals(suffix)) {
                    log.info("加班人员及天数附件格式有误");
                    commonRefuse(processInstanceId, userId, taskId, FILE_TYPE_ERROR);
                    result.setResult(FILE_TYPE_ERROR);
                    break;
                }
                // 解析附件
                List<OvertimeExcel> overtimeExcels = EasyExcel.read(FileUtil.getInputStream(downLoadFile))
                        .head(OvertimeExcel.class)
                        .sheet(0)
                        .headRowNumber(3)
                        .doReadSync();
                List<OvertimeExcel> list = overtimeExcels.stream().filter(e -> StrUtil.isNotBlank(e.getName())).collect(Collectors.toList());
                result.setData(list);
                return result;

            } catch (Exception e) {
                log.error("加班人员及天数附件处理异常", e);
                commonRefuse(processInstanceId, userId, taskId, "加班人员及天数附件处理异常,请重新压缩提交");
                result.setResult("加班人员及天数附件解析异常,请调整格式后重新提交");
                break;
            } finally {
                FileUtil.del(downLoadFile);
            }
        }
        return result;
    }

    /**
     * 校验发票
     *
     * @param processInstanceId
     * @param taskId
     * @return
     */
    private VerifyResult<List<InvoiceOcrRecord>> verifyInvoice(String processInstanceId,
                                                               String businessId,
                                                               Long taskId,
                                                               String userId,
                                                               List<TableData> tableData,
                                                               List<String> images) {
        DateTime time = tableData.stream().sorted(Comparator.comparing(TableData::getDate)).map(TableData::getDate).findFirst().get();
        StringBuilder sb = new StringBuilder();
        Set<String> failImages = new HashSet<>();
        List<Invoice> invoices = new ArrayList<>();
        for (String image : images) {
            String filename = image.substring(image.lastIndexOf("/") + 1);
            OapiOcrStructuredRecognizeResponse.OcrStructuredResult result = dingTalkOApiClient.ocr(image, "invoice");
            StringBuilder comment = new StringBuilder();
            if (Objects.isNull(result)) {
                log.info("发票识别失败");
                comment.append("\t发票识别失败，请重新拍摄上传\n");
                failImages.add(image);
            } else {
                Invoice invoice = JSON.parseObject(result.getData(), Invoice.class);
                invoice.setImageUrl(image);
                invoice.setFilename(filename);
                // 判断2023新版发票
                boolean is2023Invoice = StrUtil.length(invoice.getInvoiceCode()) == INVOICE_CODE_LENGTH_2023;
                // 1.已使用过的发票不可重复使用
                int serialNum = 0;
                // 校验发票代码是否已用
                Boolean repeat;
                String invoiceNumber = invoice.getInvoiceNumber();
                if (is2023Invoice) {
                    invoiceNumber = invoice.getInvoiceCode();
                    repeat = invoiceOcrRecordService.checkRepeatInvoice(StringPool.EMPTY, invoiceNumber, invoice.getSheet());
                } else {
                    repeat = invoiceOcrRecordService.checkRepeatInvoice(invoice.getInvoiceCode(), invoiceNumber, invoice.getSheet());
                }
                if (repeat) {
                    log.info("已使用过的发票不可重复使用,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                    comment.append(String.format(INVOICE_REUSE, ++serialNum));
                    failImages.add(image);
                }

                // 2.报销加班费、团建费，可使用开票时间为3个月内的发票，超过次期限不可使用。例：报销2021年1月加班、团建费，可用开票时间为2021年1月-3月的发票。
                DateTime invoiceDate = parseInvoiceDate(is2023Invoice, invoice);
                if (Objects.isNull(invoiceDate)) {
                    log.info("未识别出开票日期或开票日期解析失败,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                    comment.append(String.format(INVOICE_LACK, ++serialNum));
                    failImages.add(image);
                } else {
                    invoice.setParseInvoiceDate(invoiceDate);
                    long betweenYear = DateUtil.betweenYear(time, invoiceDate, true);
                    long betweenMonth = DateUtil.betweenMonth(time, invoiceDate, true);
                    // 同年3个月内
                    if (betweenYear == 0L) {
                        if (betweenMonth > 3L) {
                            log.info("开票日期超过期限,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                            comment.append(String.format(INVOICE_DATE_OUT_OF_RANGE, ++serialNum));
                            failImages.add(image);
                        }
                    } else if (betweenYear == 1L) {
                        // 3.由于12月的加班餐、团建费是在次年的1月份提单报销，故，使用的发票开票时间为上一年开票时间为10月-12月的发票，不能使用次年的发票。例：报销2021年12月加班餐费、团建费，使用发票开票时间应为2021年10月-12月，不可使用开票时间为2022年的发票
                        if (!Month.JANUARY.equals(time.monthEnum()) || betweenMonth > 3L) {
                            log.info("开票日期超过期限,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                            comment.append(String.format(INVOICE_DATE_OUT_OF_RANGE, ++serialNum));
                            failImages.add(image);
                        }
                    } else {
                        log.info("开票日期超过期限,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                        comment.append(String.format(INVOICE_DATE_OUT_OF_RANGE, ++serialNum));
                        failImages.add(image);
                    }
                }
                // 手写发票只校验以上三项
                String invoiceCategoryCode = parseInvoiceCategoryCode(invoice);
                if (!StrUtil.contains(invoiceCategoryCode, WRITE_INVOICE)) {
                    String invoiceConfigCompany = invoiceConfig.getCompany(TenantContextHolder.getTenantId());
                    String invoiceConfigCode = invoiceConfig.getCode(TenantContextHolder.getTenantId());
                    // 公共校验
                    String buyerName = invoice.getBuyerName();
                    if (is2023Invoice && StrUtil.contains(buyerName, invoiceConfigCompany)) {
                        buyerName = invoiceConfigCompany;
                    }
                    if (StrUtil.isBlank(buyerName)) {
                        log.info("受票方名称识别为空,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                        comment.append(String.format(PAY_NAME_EMPTY, ++serialNum));
                        failImages.add(image);
                    }
                    if (StrUtil.isNotBlank(buyerName) && !StrUtil.equals(buyerName, invoiceConfigCompany)) {
                        log.info("受票方名称不正确,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                        comment.append(String.format(PAY_NAME_ERROR, ++serialNum));
                        failImages.add(image);
                    }
                    String buyerTaxNumber = invoice.getBuyerTaxNumber();
                    if (is2023Invoice) {
                        String buyerAddrPhone = invoice.getBuyerAddrPhone();
                        String buyerBankAccount = invoice.getBuyerBankAccount();
                        if (StrUtil.contains(buyerAddrPhone, invoiceConfigCode) || StrUtil.contains(buyerBankAccount, invoiceConfigCode)) {
                            buyerTaxNumber = invoiceConfigCode;
                        }
                    }
                    if (!StrUtil.equals(buyerTaxNumber, invoiceConfigCode)) {
                        log.info("受票方税号不正确,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                        comment.append(String.format(TAX_NUMBER_ERROR, ++serialNum));
                        failImages.add(image);
                    }
                    String invoiceConfigAddrPhone = invoiceConfig.getAddrPhone(TenantContextHolder.getTenantId());
                    String invoiceConfigAccount = invoiceConfig.getAccount(TenantContextHolder.getTenantId());
                    // 4.发票为普通发票、通用机打发票的，只有受票方名称、受票方税号 的发票也可以使用，其他信息可不填，但填上去的必须是正确的
                    String buyerAddrPhone = formatStr(invoice.getBuyerAddrPhone());
                    String buyerBankAccount = formatStr(invoice.getBuyerBankAccount());
                    if (StrUtil.contains(invoice.getInvoiceType(), PLAIN_INVOICE) && Objects.nonNull(invoiceDate) && !is2023Invoice) {
                        // 受票方地址、电话和受票方开户行、账号必须同时存在或同时不存在
                        if ((StrUtil.isNotEmpty(buyerAddrPhone) && StrUtil.isEmpty(buyerBankAccount)) || (StrUtil.isEmpty(buyerAddrPhone) && StrUtil.isNotEmpty(buyerBankAccount))) {
                            log.info("受票方信息不完整,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                            comment.append(String.format(PAY_INFO_ERROR, ++serialNum));
                            failImages.add(image);
                        } else {
                            if (StrUtil.isNotEmpty(buyerAddrPhone)
                                    && !(StrUtil.equals(buyerAddrPhone, invoiceConfigAddrPhone) || StrUtil.equals(buyerAddrPhone, invoiceConfigAddrPhone.replace("仅限办公", "")))) {
                                log.info("受票方地址、电话不正确,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                                comment.append(String.format(PAY_ADDR_PHONE_ERROR, ++serialNum));
                                failImages.add(image);
                            }
                            if (StrUtil.isNotEmpty(buyerBankAccount)
                                    && !StrUtil.equals(buyerBankAccount, invoiceConfigAccount)) {
                                log.info("受票方开户行、账号不正确,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                                comment.append(String.format(PAY_BANK_ACCOUNT_ERROR, ++serialNum));
                                failImages.add(image);
                            }
                        }
                    }

                    // 5.发票为专票的，要求受票方名称、受票方税号、受票方地址、电话、受票方开户行、账号 所有的信息必须齐全也正确，且必须有发票联和抵扣联
                    if (StrUtil.contains(invoice.getInvoiceType(), SPECIAL_INVOICE)) {
                        // 专票信息一定要齐全、正确，空格或者其他的可以忽略；
                        if (!StrUtil.equals(buyerAddrPhone, invoiceConfigAddrPhone)) {
                            log.info("受票方地址、电话为空或不正确,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                            comment.append(String.format(PAY_ADDR_PHONE_ERROR, ++serialNum));
                            failImages.add(image);
                        }
                        if (!StrUtil.equals(buyerBankAccount, invoiceConfigAccount)) {
                            log.info("受票方开户行、账号为空或不正确,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                            comment.append(String.format(PAY_BANK_ACCOUNT_ERROR, ++serialNum));
                            failImages.add(image);
                        }
                    }

                    // 6.发票为“货物或应税劳务、服务名称”的，必须有“发票专用章”
                    // 无法识别

                    // 7.发票为“项目名称”的，不用“发票专用章”
                    // 无法识别

                    // 8.开票内容必须为餐费、食品、水果、饮料等，不可为蔬菜、鲜肉等过于生活化的发票；不可为电器等不符合加班费、团建费的发票
                    List<InvoiceDetail> invoiceDetails = JSON.parseArray(invoice.getInvoiceDetail(), InvoiceDetail.class);
                    List<String> goodsLabourServiceNames = invoiceDetails.stream()
                            .map(InvoiceDetail::getGoodsLabourServiceName)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList());
                    List<SysDictData> sysDictData = sysDictTypeService.selectDictDataByType(DictType.GOODS_SERVICES_TYPES.getType());
                    List<String> goodsServicesTypes = sysDictData.stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
                    for (String serviceName : goodsLabourServiceNames) {
                        if (goodsServicesTypes.stream().noneMatch(serviceName::contains)) {
                            log.info("【校验发票内容】,货物或应税劳务、服务名称/项目名称不符合要求,发票号码:{},文件名:{}", invoiceNumber, invoice.getFilename());
                            comment.append(String.format(SERVICE_TYPE_ERROR, ++serialNum));
                            failImages.add(image);
                        }
                    }
                }
                if (StrUtil.isNotEmpty(comment.toString())) {
                    comment.insert(0, String.format(INVOICE_ERROR, invoiceNumber, invoice.getFilename()));
                } else {
                    invoices.add(invoice);
                }
            }
            sb.append(comment);
        }
        // 当用户提交的是同一张发票的图片和pdf时，需要进行去重处理
        invoices = invoices.stream().filter(distinctByKey(Invoice::getInvoiceNumber)).collect(Collectors.toList());

        // 校验专票联次
        List<Invoice> specialInvoices = invoices.stream()
                .filter(invoice -> StrUtil.contains(invoice.getInvoiceType(), SPECIAL_INVOICE)
                        && StrUtil.isNotEmpty(invoice.getSheet())
                        && (StrUtil.contains(invoice.getSheet(), INVOICE_SHEET) || StrUtil.contains(invoice.getSheet(), DEDUCTING_SHEET)))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(specialInvoices)) {
            Map<String, List<Invoice>> specialInvoice = specialInvoices.stream().collect(Collectors.groupingBy(Invoice::getInvoiceNumber));
            specialInvoice.forEach((k, v) -> {
                // 必须有发票联和抵扣联
                Optional<Invoice> invoiceOpt = v.stream().filter(e -> e.getSheet().contains(INVOICE_SHEET)).findFirst();
                Optional<Invoice> deductingOpt = v.stream().filter(e -> e.getSheet().contains(DEDUCTING_SHEET)).findFirst();
                // 只存在发票联不存在抵扣联
                boolean existInvoice = invoiceOpt.isPresent() && !deductingOpt.isPresent();
                // 只存在抵扣联不存在发票联
                boolean existDeducting = !invoiceOpt.isPresent() && deductingOpt.isPresent();
                if (existInvoice || existDeducting) {
                    Invoice invoice = invoiceOpt.orElseGet(deductingOpt::get);
                    String lackSheet = StrUtil.contains(invoice.getSheet(), INVOICE_SHEET) ? DEDUCTING_SHEET : INVOICE_SHEET;
                    sb.append(String.format(INVOICE_ERROR + "\t为专用发票，必须包含发票联和抵扣联，缺少%s\n", k, invoice.getFilename(), lackSheet));
                    failImages.add(invoice.getImageUrl());
                }
            });
        }
        // 校验去重后发票合计数是否等于或大于报销金额
        BigDecimal invoiceTotalAmount = invoices
                .stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Invoice::getInvoiceNumber))), ArrayList::new))
                .stream()
                .map(Invoice::getInvoiceAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal reimbursementTotalAmount = tableData.stream().map(TableData::getAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (NumberUtil.isLess(invoiceTotalAmount, reimbursementTotalAmount)) {
            sb.append(String.format("发票合计数额小于报销金额，当前发票总额：%s,申请报销金额：%s", invoiceTotalAmount, reimbursementTotalAmount));
        }
        // 保存发票记录
        List<InvoiceOcrRecord> invoiceOcrRecords = saveInvoices(invoices, processInstanceId, businessId);
        // 执行审批
        String totalComment = sb.toString();
        if (StrUtil.isNotBlank(totalComment) && CollUtil.isNotEmpty(failImages)) {
            accessoryAgree(processInstanceId, taskId, userId, totalComment, Lists.newArrayList(failImages));
        } else {
            commonAgree(processInstanceId, userId, taskId, "已核对发票");
        }
        VerifyResult<List<InvoiceOcrRecord>> result = new VerifyResult<>();
        result.setData(invoiceOcrRecords);
        result.setResult(totalComment);
        return result;
    }

    /**
     * 根据发票代码去除重复发票
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    private <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        ConcurrentHashMap<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 发票类别代码
     *
     * @param invoice
     * @return
     */
    private String parseInvoiceCategoryCode(Invoice invoice) {
        String invoiceCategoryCode = null;
        try {
            InvoiceCodeParse invoiceCodeParse = JSON.parseObject(invoice.getInvoiceCodeParse(), InvoiceCodeParse.class);
            if (Objects.nonNull(invoiceCodeParse)) {
                invoiceCategoryCode = invoiceCodeParse.getInvoiceCategoryCode();
            }
        } catch (Exception e) {
            log.error("发票类别代码解析失败", e);
        }
        return invoiceCategoryCode;
    }

    /**
     * 解析开票日志
     *
     * @param is2023Invoice
     * @param invoice
     * @return
     */
    private DateTime parseInvoiceDate(boolean is2023Invoice, Invoice invoice) {
        String invoiceDate = invoice.getInvoiceDate();
        if (is2023Invoice && StrUtil.isBlank(invoiceDate)) {
            String invoiceNumber = invoice.getInvoiceNumber();
            if (StrUtil.isNotEmpty(invoiceNumber)) {
                // 出现案例：202310090
                try {
                    return DateTime.of(invoiceNumber.length() == 8 ? invoiceNumber : StrUtil.subPre(invoiceNumber, 8), DatePattern.PURE_DATE_PATTERN);
                } catch (Exception e) {
                    log.error("解析新版发票开票日期失败：{}", invoiceNumber, e);
                    return null;
                }
            }
        }
        if (StrUtil.isBlank(invoiceDate)) {
            return null;
        }
        DateTime dateTime = null;
        try {
            // 会出现“2021年05日27日”,全部替换成”-“进行解析
            String date = invoiceDate
                    .replace("年", StringPool.DASH)
                    .replace("月", StringPool.DASH)
                    .replace("日", StringPool.DASH);
            String c = String.valueOf(date.charAt(date.length() - 1));
            if (StringPool.DASH.equals(c)) {
                date = date.substring(0, date.lastIndexOf(StringPool.DASH));
            }
            dateTime = DateUtil.parse(date);
        } catch (Exception e) {
            log.error("开票日期解析失败", e);
        }
        return dateTime;
    }

    /**
     * 去除标点空白字符
     *
     * @param str
     * @return
     */
    private String formatStr(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        str = StrUtil.removeAllLineBreaks(str);
        str = StrUtil.cleanBlank(str);
        str = str.replaceAll("[^\\u4e00-\\u9fa5^0-9]", "");
        str = str.replaceAll("丶", "");
        return str;
    }

    /**
     * 添加评论
     *
     * @param processInstanceId
     * @param userId
     * @param remark
     */
    private void addComment(String processInstanceId, String userId, String remark) {
        dingTalkOApiClient.addComment(processInstanceId, userId, remark, null);
    }

    /**
     * 审批拒绝
     *
     * @param processInstanceId
     * @param taskId
     * @param remark
     */
    private void commonRefuse(String processInstanceId, String userId, Long taskId, String remark) {
        dingTalkOApiClient.executeTask(processInstanceId, userId, taskId, remark, ApprovalOpt.REFUSE, null);
    }

    /**
     * 审批通过
     *
     * @param processInstanceId
     * @param taskId
     * @param remark
     */
    private void commonAgree(String processInstanceId, String userId, Long taskId, String remark) {
        dingTalkOApiClient.executeTask(processInstanceId, userId, taskId, remark, ApprovalOpt.AGREE, null);
    }

    /**
     * 审批通过，添加评论
     *
     * @param processInstanceId
     * @param taskId
     * @param userId
     * @param photos
     */
    private void accessoryAgree(String processInstanceId, Long taskId, String userId, String remark, List<String> photos) {
        dingTalkOApiClient.executeTask(processInstanceId, userId, taskId, "存在核对有误发票", ApprovalOpt.AGREE, null);
        dingTalkOApiClient.addComment(processInstanceId, userId, remark, photos);
    }

    /**
     * 持久化发票记录
     *
     * @param invoices
     */
    private List<InvoiceOcrRecord> saveInvoices(List<Invoice> invoices, String processInstanceId, String businessId) {
        if (!CollectionUtil.isEmpty(invoices)) {
            List<InvoiceOcrRecord> invoiceOcrRecordList = new ArrayList<>();
            invoices.forEach(invoice -> {
                InvoiceOcrRecord invoiceOcrRecord = new InvoiceOcrRecord();
                BeanUtils.copyProperties(invoice, invoiceOcrRecord);
                invoiceOcrRecord.setProcessInstanceId(processInstanceId);
                invoiceOcrRecord.setBusinessId(businessId);
                invoiceOcrRecord.setInvoiceDate(invoice.getParseInvoiceDate());
                invoiceOcrRecord.setUseState(UseState.TO_BE_CONFIRMED.getState());
                invoiceOcrRecord.setTenantId(TenantContextHolder.getTenantId());
                invoiceOcrRecordList.add(invoiceOcrRecord);
            });
            invoiceOcrRecordService.saveBatch(invoiceOcrRecordList);
            return invoiceOcrRecordList;
        }
        return Collections.emptyList();
    }

    /**
     * 保存流程处理记录
     *
     * @param processInstance
     * @param processInstanceId
     * @param commentResult
     * @param isPass
     */
    private boolean saveProcessRecord(OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance,
                                      String processInstanceId,
                                      String commentResult,
                                      BoolFlagEnum isPass,
                                      ProcessType processType) {
        ProcessRecord processRecord = BeanUtil.copyProperties(processInstance, ProcessRecord.class);
        processRecord.setProcessInstanceId(processInstanceId);
        OapiV2UserGetResponse.UserGetResponse user = dingTalkOApiClient.getUser(processInstance.getOriginatorUserid());
        processRecord.setOriginatorUserId(processInstance.getOriginatorUserid());
        processRecord.setOriginatorUserName(Objects.nonNull(user) ? user.getName() : StringPool.EMPTY);
        processRecord.setIsPass(isPass.getStatus());
        processRecord.setResult(commentResult);
        processRecord.setProcessType(processType.getValue());
        processRecord.setTenantId(TenantContextHolder.getTenantId());
        return processRecordService.save(processRecord);
    }

    /**
     * 根据审批单表格数据，生成报销凭证
     *
     * @param processInstanceIds
     */
    public void generateReimbursementVoucher(List<String> processInstanceIds) {
        for (String processInstanceId : processInstanceIds) {
            MdcUtil.putProcess(processInstanceId);
            List<ReimbursementVoucher> reimbursementVouchers = reimbursementVoucherService.getByProcessInstanceId(processInstanceId);
            if (CollUtil.isNotEmpty(reimbursementVouchers)) {
                log.info("该报销凭证数据已生成过");
                continue;
            }
            // 查询审批实例详情
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = dingTalkOApiClient.getProcessInstance(processInstanceId);

            String businessId = processInstance.getBusinessId();
            Date finishTime = processInstance.getFinishTime();
            String result = processInstance.getResult();
            String status = processInstance.getStatus();

            // 核销发票
            if (ProcessStatus.COMPLETED.equalsIgnoreCase(status) && ApprovalOpt.AGREE.equalsIgnoreCase(result)) {
                updateInvoiceState(processInstanceId);
            }

            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();

            // 获取表格信息域
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> tableFieldOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> TableFieldId.TABLE_FIELD.equals(formComponentValueVo.getComponentType())).findFirst();
            if (!tableFieldOptional.isPresent()) {
                log.info("未获取到表格信息域");
                continue;
            }
            List<TableData> tableDataList = getTableData(processInstanceId, tableFieldOptional.get(), businessId);
            if (CollUtil.isEmpty(tableDataList)) {
                continue;
            }

            for (TableData tableData : tableDataList) {
                String deptId = tableData.getDeptId();
                ReimbursementVoucher reimbursementVoucher = new ReimbursementVoucher();
                reimbursementVoucher.setProcessInstanceId(processInstanceId);
                reimbursementVoucher.setMakeDate(finishTime);
                reimbursementVoucher.setVoucherType(0);
                // 生成凭证编号
                int number = 0;
                DateTime beginOfMonth = DateUtil.beginOfMonth(finishTime);
                DateTime endOfMonth = DateUtil.endOfMonth(finishTime);
                ReimbursementVoucher latestVoucher = reimbursementVoucherService.getLatestByMakeDate(beginOfMonth, endOfMonth);
                if (Objects.nonNull(latestVoucher)) {
                    number = new BigDecimal(latestVoucher.getVoucherNumber()).intValue();
                }
                reimbursementVoucher.setVoucherNumber(generateVoucherNumber(number));
                Subject subject = subjectService.selectBySubjectNameAndDingTalkDeptId(tableData.getType(), deptId);
                if (Objects.isNull(subject)) {
                    log.info("查不到科目数据,subjectName:{},deptId:{}, 审批单号:{}", tableData.getType(), deptId, businessId);
                    subject = new Subject();
                    subject.setSubjectCode("9999");
                }
                reimbursementVoucher.setSummary(String.format(SUMMARY, tableData.getBeginDate(), tableData.getEndDate(), tableData.getType()));
                reimbursementVoucher.setSubjectCode(subject.getSubjectCode());
                reimbursementVoucher.setCurrency(0);
                reimbursementVoucher.setBorrowDirection(0);
                reimbursementVoucher.setDomesticCurrency(new BigDecimal(tableData.getAmount()));
                SysDept sysDept = sysDeptService.selectDeptByDingTalkDeptId(deptId);
                if (Objects.isNull(sysDept)) {
                    log.info("查不到部门数据,deptId:{}, 审批单号:{}", deptId, businessId);
                    continue;
                }
                reimbursementVoucher.setDeptCode(sysDept.getDeptCode());
                if (TenantContextHolder.getTenantId() == 0) {
                    if (Lists.newArrayList("54011101", "54011102", "54011103").contains(subject.getSubjectCode())) {
                        reimbursementVoucher.setItemCode("01002");
                    }
                } else {
                    if (Lists.newArrayList("5602170401", "5602170402", "5602170403", "54011101", "54011102", "54011103").contains(subject.getSubjectCode())) {
                        reimbursementVoucher.setItemCode("01002");
                    }
                }
                reimbursementVoucher.setTenantId(TenantContextHolder.getTenantId());
                reimbursementVoucher.setCreateTime(new Date());
                // 数据量不大,先入库,上面查询会用到,性能影响较小
                reimbursementVoucherService.save(reimbursementVoucher);
            }
        }
    }

    /**
     * 更新发票状态
     *
     * @param processInstanceId
     */
    private void updateInvoiceState(String processInstanceId) {
        List<InvoiceOcrRecord> invoiceOcrRecords = invoiceOcrRecordService.listByProcessInstanceId(processInstanceId);
        if (CollUtil.isNotEmpty(invoiceOcrRecords)) {
            Date now = new Date();
            invoiceOcrRecords.forEach(invoiceOcrRecord -> {
                invoiceOcrRecord.setUseState(UseState.CONSUMED.getState());
                invoiceOcrRecord.setUpdateTime(now);
            });
            invoiceOcrRecordService.updateBatchById(invoiceOcrRecords);
        }
    }

    /**
     * 获取表格数据
     *
     * @param processInstanceId
     * @param tableFiled
     * @return
     */
    private List<TableData> getTableData(String processInstanceId, OapiProcessinstanceGetResponse.FormComponentValueVo tableFiled, String businessId) {
        // 获取表格报销日期范围
        String tableFieldValue = tableFiled.getValue();
        List<TableField> data = JSON.parseObject(tableFieldValue, new TypeReference<List<TableField>>() {
        });
        List<TableData> tableDataList = new ArrayList<>();
        for (TableField datum : data) {
            List<TableField.RowValue> forms = datum.getRowValue();
            Optional<TableField.RowValue> departmentOpt = forms.stream().filter(form -> TableFieldId.TABLE_DEPARTMENT_FIELD.equals(form.getComponentType())).findFirst();
            if (!departmentOpt.isPresent()) {
                log.info("未获取到表格中的部门数据");
                return Collections.emptyList();
            }
            TableField.RowValue departmentVo = departmentOpt.get();
            // 部门
            String department = departmentVo.getValue();
            TableData tableData = new TableData();
            tableDataList.add(tableData);
            tableData.setDepartment(department);
            List<Map<String, String>> depts = JSON.parseObject(departmentVo.getExtendValue(), new TypeReference<List<Map<String, String>>>() {
            });
            if (depts.size() > 1) {
                log.info("存在多个部门数据");
                return Collections.emptyList();
            }
            tableData.setDeptId(depts.get(0).get("id"));

            Optional<TableField.RowValue> dateRangeOpt = forms.stream().filter(form -> TableFieldId.TABLE_DATE_RANGE_FIELD.equals(form.getComponentType())).findFirst();
            if (!dateRangeOpt.isPresent()) {
                log.info("未获取到表格中的时间数据");
                return Collections.emptyList();
            }
            TableField.RowValue dateRangeVo = dateRangeOpt.get();
            // 开始时间, 结束时间
            List<String> dateRange = JSON.parseArray(dateRangeVo.getValue(), String.class);
            if (CollUtil.isEmpty(dateRange) || dateRange.stream().filter(StrUtil::isNotEmpty).count() != 2) {
                log.info("表格中的报销日期不合法");
                return Collections.emptyList();
            }
            String beginDate = dateRange.get(0);
            String endDate = dateRange.get(1);
            DateTime begin = DateUtil.parse(beginDate, DatePattern.NORM_MONTH_PATTERN);
            DateTime end = DateUtil.parse(endDate, DatePattern.NORM_MONTH_PATTERN);
            if (DateUtil.compare(begin, end) != 0) {
                log.info("表格中的报销日期不合法");
                return Collections.emptyList();
            }
            tableData.setDate(begin);
            tableData.setBeginDate(beginDate);
            tableData.setEndDate(endDate);

            Optional<TableField.RowValue> selectOpt = forms.stream().filter(form -> TableFieldId.TABLE_SELECT_FIELD.equals(form.getComponentType())).findFirst();
            if (!selectOpt.isPresent()) {
                log.info("未获取到表格中的费用类别数据");
                return Collections.emptyList();
            }
            TableField.RowValue selectVo = selectOpt.get();
            // 费用类别
            String select = selectVo.getValue();
            tableData.setType(select);

            Optional<TableField.RowValue> moneyOpt = forms.stream().filter(form -> TableFieldId.TABLE_MONEY_FIELD.equals(form.getComponentType())).findFirst();
            if (!moneyOpt.isPresent()) {
                log.info("未获取到表格中的金额数据");
                return Collections.emptyList();
            }
            TableField.RowValue moneyVo = moneyOpt.get();
            // 金额
            String money = moneyVo.getValue();
            tableData.setAmount(money);
        }
        return tableDataList;
    }

    private String generateVoucherNumber(int initValue) {
        return StrUtil.fillBefore(String.valueOf(initValue + 1), VOUCHER_NUMBER_FILL_CHAR, VOUCHER_NUMBER_LENGTH);
    }

    /**
     * 导出凭证
     *
     * @param reimbursementVoucher
     * @return
     */
    @SneakyThrows
    public AjaxResult export(ReimbursementVoucher reimbursementVoucher) {
        DateTime dateTime = DateUtil.parse(reimbursementVoucher.getExportMakeDate(), DatePattern.NORM_DATE_PATTERN);
        reimbursementVoucher.setBeginDate(DateUtil.beginOfDay(dateTime).toString(DatePattern.NORM_DATETIME_PATTERN));
        reimbursementVoucher.setEndDate(DateUtil.endOfDay(dateTime).toString(DatePattern.NORM_DATETIME_PATTERN));
        List<ReimbursementVoucher> reimbursementVouchers = reimbursementVoucherService.selectReimbursementVoucherList(reimbursementVoucher);
        if (CollUtil.isEmpty(reimbursementVouchers)) {
            return AjaxResult.error("导出失败，当前导出条件查询数据为空！");
        }
        List<VoucherExport> exports = new ArrayList<>();
        reimbursementVouchers.forEach(e -> {
            VoucherExport borrowVoucher = new VoucherExport();
            BeanUtil.copyProperties(e, borrowVoucher);
            String madeDate = DateUtil.format(e.getMakeDate(), "yyyy/MM/dd");
            borrowVoucher.setMakeDate(madeDate);
            String voucherType = sysDictDataService.selectDictLabel("finance_voucher_type", "0");
            borrowVoucher.setVoucherType(voucherType);
            String currency = sysDictDataService.selectDictLabel("finance_currency", "0");
            borrowVoucher.setCurrency(currency);
            borrowVoucher.setBorrowDirection(sysDictDataService.selectDictLabel("finance_borrow_direction", "0"));
            borrowVoucher.setItem(null);
            borrowVoucher.setOrgCode(e.getCompanyCode());
            exports.add(borrowVoucher);
            //专票要返回多两个科目
            if (Constants.CommonVal.ONE == e.getSpecialInvoice()) {
                List<VoucherExport> voucherExports = checkAndAddSpecialInvoice(borrowVoucher, e.getProcessInstanceId());
                exports.addAll(voucherExports);
                //借方金额也要修改
                double sum = voucherExports.stream().mapToDouble(tmp -> Double.parseDouble(tmp.getDomesticCurrency())).sum();
                borrowVoucher.setDomesticCurrency(MathUtil.sub(borrowVoucher.getDomesticCurrency(), Double.toString(sum)));
            }

            VoucherExport loanVoucher = new VoucherExport();
            BeanUtil.copyProperties(e, loanVoucher);
            loanVoucher.setMakeDate(madeDate);
            loanVoucher.setVoucherType(voucherType);
            String subjectCode = reimbursementVoucher.getLoanSubjectCode();
            if (!subjectCode.startsWith("1002")) {
                subjectCode = "100201";
            }
            loanVoucher.setSubjectCode(subjectCode);
            loanVoucher.setCurrency(currency);
            loanVoucher.setBorrowDirection(sysDictDataService.selectDictLabel("finance_borrow_direction", "1"));
            loanVoucher.setDeptCode(null);
            loanVoucher.setItemCode(null);
            loanVoucher.setItem(null);

            exports.add(loanVoucher);
        });
        Integer tenantId = reimbursementVoucher.getTenantId();
        ExcelUtil<VoucherExport> util = new ExcelUtil<>(VoucherExport.class);
        return util.fillEasyExcel(exports, "凭证导入模板.xlsx", tenantId == 0 ? "全民钱包凭证导出" : "深圳合众凭证导出");
    }


    /**
     * 专票要返回多两个科目
     *
     * @param borrowVoucher
     * @return
     */
    private List<VoucherExport> checkAndAddSpecialInvoice(VoucherExport borrowVoucher, String processInstanceId) {
        //找出发票
        InvoiceOcrRecord invoiceOcrRecord = invoiceOcrRecordService.getOneByProcessInstanceIdAndAmount(processInstanceId, borrowVoucher.getDomesticCurrency());
        if (Objects.isNull(invoiceOcrRecord)) {
            log.info("发票找不到, 审批实例id:{}", processInstanceId);
            invoiceOcrRecord = new InvoiceOcrRecord();
            invoiceOcrRecord.setInvoiceTax(BigDecimal.ZERO);
//            throw new BaseException("发票找不到，请联系管理员。审批实例id:"+ processInstanceId);
        }
        List<VoucherExport> voucherExports = new ArrayList<>();
        //待认证进项税额
        VoucherExport invoiceTaxVoucher = new VoucherExport();
        BeanUtil.copyProperties(borrowVoucher, invoiceTaxVoucher);
        invoiceTaxVoucher.setDomesticCurrency(invoiceOcrRecord.getInvoiceTax().toString());
        invoiceTaxVoucher.setSubjectCode("222105");
        invoiceTaxVoucher.setDeptCode(null);
        invoiceTaxVoucher.setOrgCode(null);
        voucherExports.add(invoiceTaxVoucher);

        //增值税加计抵减额
        VoucherExport vatVoucher = new VoucherExport();
        BeanUtil.copyProperties(borrowVoucher, vatVoucher);
        vatVoucher.setDomesticCurrency(MathUtil.round(MathUtil.mul(invoiceOcrRecord.getInvoiceTax().toString(), "0.1"), MathUtil.DIV_SCALE_TWO, MathUtil.ROUND_HALF_UP));
        vatVoucher.setSubjectCode("222126");
        vatVoucher.setDeptCode(null);
        vatVoucher.setOrgCode(null);
        voucherExports.add(vatVoucher);
        return voucherExports;
    }


}
