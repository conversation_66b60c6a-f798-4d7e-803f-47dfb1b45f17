package com.qmqb.oa.admin.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EkbSyncLeaderRoleContent {
    /**
     *pathType = id 时，path 传入部门或自定义档案项ID
     */
    private String pathType;

    /**
     * pathType = id 时只传最终路径
     */
    private List<String> path;

    /**
     * 员工id集合
     */
    private List<String> staffs;
}
