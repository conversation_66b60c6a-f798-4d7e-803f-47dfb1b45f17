package com.qmqb.oa.system.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.common.utils.DateUtils;
import com.qmqb.oa.system.domain.DingTalkClockResult;
import com.qmqb.oa.system.domain.vo.ClockStatisticsVo;
import com.qmqb.oa.system.mapper.DingTalkClockResultMapper;
import com.qmqb.oa.system.service.DingTalkClockResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 钉钉打卡结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@Service
public class DingTalkClockResultServiceImpl extends ServiceImpl<DingTalkClockResultMapper, DingTalkClockResult> implements DingTalkClockResultService {
    @Autowired
    private DingTalkClockResultMapper dingtalkClockResultMapper;

    /**
     * 查询钉钉打卡结果
     *
     * @param id 钉钉打卡结果ID
     * @return 钉钉打卡结果
     */
    @Override
    public DingTalkClockResult selectDingtalkClockResultById(Long id) {
        return dingtalkClockResultMapper.selectDingtalkClockResultById(id);
    }

    /**
     * 查询钉钉打卡结果列表
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 钉钉打卡结果
     */
    @Override
    public List<DingTalkClockResult> selectDingtalkClockResultList(DingTalkClockResult dingtalkClockResult) {
        return dingtalkClockResultMapper.selectDingtalkClockResultList(dingtalkClockResult);
    }

    /**
     * 新增钉钉打卡结果
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 结果
     */
    @Override
    public int insertDingtalkClockResult(DingTalkClockResult dingtalkClockResult) {
        dingtalkClockResult.setCreateTime(DateUtils.getNowDate());
        return dingtalkClockResultMapper.insertDingtalkClockResult(dingtalkClockResult);
    }

    /**
     * 修改钉钉打卡结果
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 结果
     */
    @Override
    public int updateDingtalkClockResult(DingTalkClockResult dingtalkClockResult) {
        dingtalkClockResult.setUpdateTime(DateUtils.getNowDate());
        return dingtalkClockResultMapper.updateDingtalkClockResult(dingtalkClockResult);
    }

    /**
     * 批量删除钉钉打卡结果
     *
     * @param ids 需要删除的钉钉打卡结果ID
     * @return 结果
     */
    @Override
    public int deleteDingtalkClockResultByIds(Long[] ids) {
        return dingtalkClockResultMapper.deleteDingtalkClockResultByIds(ids);
    }

    /**
     * 删除钉钉打卡结果信息
     *
     * @param id 钉钉打卡结果ID
     * @return 结果
     */
    @Override
    public int deleteDingtalkClockResultById(Long id) {
        return dingtalkClockResultMapper.deleteDingtalkClockResultById(id);
    }

    @Override
    public List<ClockStatisticsVo> statistics(DingTalkClockResult dingTalkClockResult) {
        DateTime now = DateTime.now();
        DateTime beginTime = DateUtil.beginOfDay(now);
        DateTime endTime = DateUtil.endOfDay(now);
        if (ArrayUtil.isNotEmpty(dingTalkClockResult.getWorkDateRange())) {
            beginTime = DateUtil.beginOfDay(DateUtil.parse(dingTalkClockResult.getWorkDateRange()[0]));
            endTime = DateUtil.endOfDay(DateUtil.parse(dingTalkClockResult.getWorkDateRange()[1]));
        }
        return dingtalkClockResultMapper.statistics(beginTime.toString(),
                endTime.toString(),
                dingTalkClockResult.getTenantId(),
                dingTalkClockResult.getUserName());
    }

}
