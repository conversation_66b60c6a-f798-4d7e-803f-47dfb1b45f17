package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2DepartmentListparentbydeptResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.common.util.NumberUtil;
import com.hzed.structure.common.util.ObjectUtil;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAddDeptReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchAddDeptReq;
import com.qmqb.oa.common.enums.EkbEventType;
import com.qmqb.oa.common.utils.ExceptionUtil;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.DingDept;
import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.DingDeptService;
import com.qmqb.oa.system.service.EkbDingDeptMappingService;
import com.qmqb.oa.system.service.SysEventService;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.xpath.operations.Number;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import shade.com.alibaba.fastjson2.JSONArray;
import shade.com.alibaba.fastjson2.JSONObject;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/7/16
 */
@Slf4j
@Component
public class DingTalkDeptService {

    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private H5AppDingClientService h5AppDingClientService;
    @Autowired
    private DingDeptService dingDeptService;
    @Autowired
    private EkbDingDeptMappingService ekbDingDeptMappingService;
    @Autowired
    private EkbDeptService ekbDeptService;
    @Autowired
    private SysEventService sysEventService;

    private final String DEPT_EVENT_FIELD_TYPE_1 = "deptId";
    private final String DEPT_EVENT_FIELD_TYPE_2 = "deptid";
    private final int NUM_2 = 2;
    private final int NUM_10 = 10;
    /**
     * 新增部门（钉钉和易快报）
     *
     * @param bizData
     * @param companyId
     * @throws Exception
     */
    public void createDept4DingAndEkb(JSONObject bizData, Integer companyId) throws Exception {
        // 获取部门ID列表
        JSONArray deptIdArray = getDeptIdArrayByBizData(bizData);
        // 获取accessToken
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, companyId));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        List<EkbAddDeptReq> deptList = new ArrayList<>();

        for (Object deptId : deptIdArray) {
            // 根据部门id获取部门详情
            OapiV2DepartmentGetResponse.DeptGetResponse deptDetail = getDeptDetail(Long.valueOf(deptId.toString()), accessToken);
            //查询数据库是否存在此部门，存在则不处理
            List<DingDept> list = dingDeptService.list(Wrappers.lambdaQuery(DingDept.class).eq(DingDept::getDeptId, deptId).eq(DingDept::getCompanyId, companyId));
            if (CollectionUtils.isNotEmpty(list)) {
                continue;
            }
            // 查询上级部门
            DingDept parentDept = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class)
                    .eq(DingDept::getCompanyId, companyId)
                    .eq(DingDept::getDeptId, deptDetail.getParentId()));
            // 获取当前部门order
            Integer order = getDeptOrder(companyId, deptDetail.getParentId());
            // 生成部门编码
            String deptCode;
            boolean flag = true;
            do {
                deptCode = getDeptCode(parentDept.getDeptCode(), order);
                List<DingDept> existList = dingDeptService.list(Wrappers.lambdaQuery(DingDept.class).eq(DingDept::getDeptCode, deptCode).eq(DingDept::getCompanyId, companyId));
                order++;
                flag = CollectionUtils.isNotEmpty(existList);
            } while (flag);
            // 保存钉钉部门信息
            saveDeptInfo(Long.valueOf(deptId.toString()), parentDept, deptDetail.getName(), companyId, deptCode);
            // 组装易快报批量新增部门接口请求参数
            EkbAddDeptReq ekbAddDeptReq = buildEkbAddDeptReq(deptCode, companyId, deptDetail.getName(), deptDetail.getParentId());
            deptList.add(ekbAddDeptReq);

        }
        // 调用易快报批量新增部门接口
        EkbBatchAddDeptReq ekbBatchAddDeptReq = new EkbBatchAddDeptReq();
        ekbBatchAddDeptReq.setDepartmentList(deptList);
        try {
            ekbDeptService.batchAddDept(ekbBatchAddDeptReq);
        } catch (Exception e) {
            log.error("【易快报-批量新增部门】异常!", e);
            String errorMsg = ExceptionUtil.splitExceptionMsg(e);
            // 保存事件表
            sysEventService.saveSysEvent(companyId, 2, EkbEventType.DEPT_BATCH_CREATE.getEventType(), JSON.toJSONString(ekbBatchAddDeptReq), null, null, errorMsg);
        }

    }


    /**
     * 根据bizData获取部门id集合
     * 兼容两种格式deptId命名，驼峰和全小写
     *
     * @param bizData
     * @return
     */
    private JSONArray getDeptIdArrayByBizData(JSONObject bizData) {
        if (ObjectUtil.isNotEmpty(bizData.getJSONArray(DEPT_EVENT_FIELD_TYPE_1))) {
            return bizData.getJSONArray(DEPT_EVENT_FIELD_TYPE_1);
        } else {
            return bizData.getJSONArray(DEPT_EVENT_FIELD_TYPE_2);
        }
    }


    /**
     * 组装易快报批量新增部门接口参数
     *
     * @param deptCode
     * @param companyId
     * @param deptName
     * @param parentDeptId
     * @return
     */
    public EkbAddDeptReq buildEkbAddDeptReq(String deptCode, Integer companyId, String deptName, Long parentDeptId) {
        // 组装易快报批量新增部门接口参数
        EkbAddDeptReq ekbAddDeptReq = new EkbAddDeptReq();
        try {
            ekbAddDeptReq.setCode(deptCode);
            ekbAddDeptReq.setName(deptName);
            // 查询易快报上级部门id
            EkbDingDeptMapping ekbParentDept = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getCompanyId, companyId)
                    .eq(EkbDingDeptMapping::getDingDeptId, parentDeptId));
            ekbAddDeptReq.setParentId(ekbParentDept.getEkbDeptId());
        } catch (Exception e) {
            log.error("组装易快报批量新增部门接口参数异常！deptCode:{}", deptCode, e);
        }
        return ekbAddDeptReq;
    }

    /**
     * 修改部门信息（钉钉和易快报）
     *
     * @param bizData
     * @param companyId
     */
    public void modifyDept4DingAndEkb(JSONObject bizData, Integer companyId) throws Exception {
        // 获取部门ID列表
        JSONArray deptIdArray = getDeptIdArrayByBizData(bizData);
        // 获取accessToken
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, companyId));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        for (Object deptId : deptIdArray) {
            // 根据部门id获取部门详情
            OapiV2DepartmentGetResponse.DeptGetResponse deptDetail = getDeptDetail(Long.valueOf(deptId.toString()), accessToken);
            // 查询当前部门与易快报部门映射表
            EkbDingDeptMapping ekbDingDeptMapping = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getDingDeptId, deptId)
                    .eq(EkbDingDeptMapping::getCompanyId, companyId));
            // 更新部门信息
            String deptCode = updateDeptInfo(deptDetail.getDeptId(), ekbDingDeptMapping.getDingParentId(), deptDetail.getName(), companyId, ekbDingDeptMapping.getId(), accessToken);
            // 查询父级部门与易快报部门映射表
            EkbDingDeptMapping ekbParentDeptMapping = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getDingDeptId, deptDetail.getParentId())
                    .eq(EkbDingDeptMapping::getCompanyId, companyId));
            // 同步易快报更新部门信息
            EkbAddDeptReq req = new EkbAddDeptReq();
            req.setCode(deptCode);
            req.setName(deptDetail.getName());
            req.setParentId(ekbParentDeptMapping.getEkbDeptId());
            String ekbDeptId = ekbDingDeptMapping.getEkbDeptId();
            try {
                ekbDeptService.updateDept(ekbDeptId, req);
            } catch (Exception e) {
                log.error("【易快报-修改部门信息】异常！易快报部门id:{}", ekbDeptId, e);
                String errorMsg = ExceptionUtil.splitExceptionMsg(e);
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.DEPT_BATCH_MODIFY.getEventType(), JSON.toJSONString(req), null, ekbDeptId, errorMsg);
            }

        }
    }

    /**
     * 删除部门
     *
     * @param bizData
     * @param companyId
     */
    public void deleteDept(JSONObject bizData, Integer companyId) {
        // 获取部门ID列表
        JSONArray deptIdArray = getDeptIdArrayByBizData(bizData);
        for (Object deptId : deptIdArray) {
            // 删除钉钉部门表数据
            dingDeptService.remove(Wrappers.lambdaQuery(DingDept.class)
                    .eq(DingDept::getCompanyId, companyId)
                    .eq(DingDept::getDeptId, deptId));

            EkbDingDeptMapping ekbDingDeptMapping = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getDingDeptId, deptId)
                    .eq(EkbDingDeptMapping::getCompanyId, companyId));
            String ekbDeptId = ekbDingDeptMapping.getEkbDeptId();
            // 删除映射表部门数据
            ekbDingDeptMappingService.remove(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getEkbDeptId, ekbDeptId));
            // 同步易快报停用部门
            try {
                ekbDeptService.stopDept(ekbDeptId);
            } catch (Exception e) {
                log.error("【易快报-停用部门】异常,易快报部门id:{}", ekbDeptId, e);
                String errorMsg = ExceptionUtil.splitExceptionMsg(e);
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.DEPT_BATCH_DELETE.getEventType(), null, null, ekbDeptId, errorMsg);
            }

        }

    }

    /**
     * 保存部门信息
     *
     * @param deptId
     * @param parentDept
     * @param deptName
     * @param companyId
     * @param deptCode
     */
    private void saveDeptInfo(Long deptId, DingDept parentDept, String deptName, Integer companyId, String deptCode) {
        // 保存钉钉部门表
        DingDept dingDept = new DingDept();
        dingDept.setDeptId(deptId);
        dingDept.setParentId(parentDept.getDeptId());
        dingDept.setDeptName(deptName);
        dingDept.setDeptLevel(parentDept.getDeptLevel() + 1);
        dingDept.setDeptCode(deptCode);
        dingDept.setDeptChainName(parentDept.getDeptChainName() + "-" + deptName);
        dingDept.setCompanyId(companyId);
        dingDeptService.save(dingDept);

        // 保存钉钉与易快报部门映射表
        EkbDingDeptMapping ekbDingDeptMapping = new EkbDingDeptMapping();
        ekbDingDeptMapping.setDeptCode(deptCode);
        ekbDingDeptMapping.setDingDeptId(deptId);
        ekbDingDeptMapping.setDingParentId(parentDept.getDeptId());
        ekbDingDeptMapping.setDeptName(deptName);
        ekbDingDeptMapping.setDingDeptLevel(parentDept.getDeptLevel() + 1);
        ekbDingDeptMapping.setCompanyId(companyId);
        ekbDingDeptMappingService.save(ekbDingDeptMapping);
    }

    /**
     * 修改部门信息
     *
     * @param deptId
     * @param parentDeptId
     * @param deptName
     * @param companyId
     * @param ekbDingDeptMappingId
     * @return deptCode
     */
    private String updateDeptInfo(Long deptId, Long parentDeptId, String deptName, Integer companyId, Long ekbDingDeptMappingId, String accessToken) {

        //查询父级部门列表
        OapiV2DepartmentListparentbydeptResponse.DeptListParentByDeptIdResponse listParentByDept = h5AppDingClientService.listParentByDept(deptId, accessToken);
        //新父级部门id,默认是原来父级部门
        Long newParentDeptId = parentDeptId;
        //默认父级部门没有变化
        boolean parentDeptChange = false;
        if (listParentByDept != null) {
            //父级部门id列表中包括当前部门id,并且是在第一个
            List<Long> parentIdList = listParentByDept.getParentIdList();
            if (parentIdList.size() > NUM_2) {
                //找出上一级父部门
                Long apiParentDeptId = parentIdList.get(1);
                //对比钉钉返回的部门id和数据库中保存的部门id是否一致
                if (null != apiParentDeptId && !parentDeptId.equals(apiParentDeptId)) {
                    log.info("钉钉部门id:{}与数据库部门id:{}不一致", apiParentDeptId, parentDeptId);
                    parentDeptChange = true;
                    newParentDeptId = apiParentDeptId;
                }
            }
        }

        // 查询上级部门
        DingDept parentDept = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class)
                .eq(DingDept::getCompanyId, companyId)
                .eq(DingDept::getDeptId, newParentDeptId));
        log.info("钉钉部门id:{},钉钉部门名称:{},钉钉部门父级部门id:{},钉钉部门父级部门名称:{}", deptId, deptName, newParentDeptId, parentDept.getDeptName());
        int deptLevel = 1;
        // 上级部门不为空，当前部门级别为上级部门级别+1
        if (!Objects.isNull(parentDept)) {
            deptLevel = parentDept.getDeptLevel() + 1;
        }
        // 查询当前部门
        DingDept dingDept = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class)
                .eq(DingDept::getCompanyId, companyId)
                .eq(DingDept::getDeptId, deptId));

        //如果deptName与数据库的名称不一致，则需要更新子部门的链路名称
        if (!deptName.equals(dingDept.getDeptName())) {
            parentDeptChange = true;
        }
////        // 获取当前部门order todo 修改部门时，不需要生成code
//        Integer order = getDeptOrder(companyId, dingDept.getParentId());
////        // 生成部门编码
//        String deptCode = getDeptCode(parentDept.getDeptCode(), order);
        // 更新钉钉部门表
        DingDept updateDingDept = new DingDept();
        updateDingDept.setId(dingDept.getId());
        updateDingDept.setParentId(newParentDeptId);
        updateDingDept.setDeptName(deptName);
        updateDingDept.setDeptLevel(deptLevel);
        updateDingDept.setDeptChainName(parentDept.getDeptChainName() + "-" + deptName);
        updateDingDept.setUpdateTime(LocalDateTime.now());
        dingDeptService.updateById(updateDingDept);

        // 更新钉钉与易快报部门映射表
        EkbDingDeptMapping updateEkbDingDeptMapping = new EkbDingDeptMapping();
        updateEkbDingDeptMapping.setId(ekbDingDeptMappingId);
        updateEkbDingDeptMapping.setDeptName(deptName);
        updateEkbDingDeptMapping.setDingDeptLevel(deptLevel);
        updateEkbDingDeptMapping.setDingParentId(newParentDeptId);
        updateEkbDingDeptMapping.setUpdateTime(LocalDateTime.now());
        ekbDingDeptMappingService.updateById(updateEkbDingDeptMapping);

        if (parentDeptChange) {
            //父级部门有变化，则更新子部门信息及其易快报部门映射表
            log.info("deptId:{},chainName:{},当前部门信息有变化，需要对其子孙辈部门进行改动", deptId, dingDept.getDeptChainName());
            batchUpdateChildrenByParent(companyId, parentDeptId, dingDept.getDeptChainName(), updateDingDept.getDeptChainName());
        }

        return dingDept.getDeptCode();
    }


    /**
     * 根据部门id获取部门详情
     *
     * @param deptId
     * @param accessToken
     * @throws ApiException
     */
    public OapiV2DepartmentGetResponse.DeptGetResponse getDeptDetail(Long deptId, String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(deptId);
        req.setLanguage("zh_CN");
        OapiV2DepartmentGetResponse resp = client.execute(req, accessToken);
        if (!resp.isSuccess()) {
            log.error("获取部门详情失败，部门id：{},响应code：{},响应msg:{}", deptId, resp.getErrcode(), resp.getErrmsg());
        } else {
            log.info("获取部门详情成功，部门id：{},部门名称：{},父部门id：{}", deptId, resp.getResult().getName(), resp.getResult().getParentId());
        }
        return resp.getResult();

    }

    /**
     * 获取部门order
     *
     * @param companyId
     * @param parentDeptId
     * @return
     */
    private Integer getDeptOrder(Integer companyId, Long parentDeptId) {
        // 查询当前部门在同父级编码的顺序，取最大值
        DingDept dingDept = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class)
                .eq(DingDept::getCompanyId, companyId)
                .eq(DingDept::getParentId, parentDeptId)
                .orderByDesc(DingDept::getDeptCode)
                .last("limit 1"));
        String order = "0";
        if (dingDept != null) {
            String broDeptCode = dingDept.getDeptCode();
            order = broDeptCode.substring(broDeptCode.length() - 2);
        }
        return Integer.parseInt(order) + 1;
    }

    /**
     * 获取部门编码
     *
     * @param parentCode 父级部门编码
     * @param order      当前部门在同父级编码的顺序
     * @return
     */
    public String getDeptCode(String parentCode, Integer order) {
        String pre = parentCode;
        String end = order.toString();
        if (order < NUM_10) {
            end = "0" + order;
        }
        return pre + end;
    }

    private void batchUpdateChildrenByParent(int companyId, Long parentId, String oldParentChainName, String newParentDeptChainName) {
        //按照部门级别升序排序，父级部门在前，子部门在后
        List<DingDept> deptList = dingDeptService.list(Wrappers.lambdaQuery(DingDept.class)
                .eq(DingDept::getCompanyId, companyId)
                .likeRight(DingDept::getDeptChainName, oldParentChainName)
                .orderByAsc(DingDept::getDeptLevel)
        );
        log.info("待更新的部门：{}", JSON.toJSONString(deptList));
        if (CollectionUtils.isNotEmpty(deptList)) {
            for (DingDept dingDept : deptList) {
                if (dingDept.getDeptId().equals(parentId)) {
                    //部门是当前部门，不处理
                    continue;
                }
                log.info("待更新的部门：{},deptId:{}", dingDept.getDeptChainName(), dingDept.getDeptId());
                DingDept update = new DingDept();
                update.setId(dingDept.getId());
                update.setDeptId(dingDept.getDeptId());
                DingDept parent = dingDeptService.getOne(Wrappers.lambdaQuery(DingDept.class).eq(DingDept::getCompanyId, companyId).eq(DingDept::getDeptId, dingDept.getParentId()));
                String deptChainName = parent.getDeptChainName() + "-" + dingDept.getDeptName();
                if (parent.getDeptId().equals(parentId)) {
                    //父部门的id是parentId，说明父部门的chainName有变化，不能读库，要使用参数中的newParentDeptChainName
                    deptChainName = newParentDeptChainName + "-" + dingDept.getDeptName();
                }
                update.setDeptChainName(deptChainName);
                update.setDeptLevel(deptChainName.split("-").length);
                update.setUpdateTime(LocalDateTime.now());
                dingDeptService.updateById(update);
            }
        }

    }


}
