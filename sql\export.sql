create table t_repay_stat(
    id bigint(11) unsigned auto_increment comment '自增id'
		 primary key
    ,real_repayment_date datetime not null comment '制单日期',
		`batch_no` varchar(256) DEFAULT NULL COMMENT '批次号',
    voucher_type varchar(10) comment '凭证类别'
    ,voucher_code varchar(10) comment '凭证编号'
    ,source_type varchar(10) comment '来源类型'
    ,diff_voucher varchar(10) comment '差异凭证'
    ,order_num varchar(10) comment '附单据数'
    ,remark varchar(500) comment '摘要'
    ,subject_code varchar(20) comment '科目编码'
    ,subject_name varchar(100) comment '科目'
    ,currency varchar(10) comment '币种'
    ,exchange_rate numeric(10,5) comment '汇率'
    ,number int(8) comment '数量'
    ,price numeric(20,2) comment '单价'
    ,direction varchar(10) comment '借贷方向'
    ,local_amount numeric(20,2) comment '原币'
    ,foreign_amount numeric(20,2) comment '本币'
    ,debt_no varchar(10) comment '票据号'
    ,debt_date varchar(10) comment '票据日期'
    ,order_no varchar(10) comment '业务单号'
    ,order_date varchar(10) comment '业务日期'
    ,repay_date varchar(10) comment '到期日'
    ,clerk_code varchar(10) comment '业务员编码'
    ,clerk_name varchar(10) comment '业务员'
    ,bank_no varchar(10) comment '银行帐号'
    ,settled_type varchar(10) comment '结算方式'
    ,company_code varchar(10) comment '往来单位编码'
    ,company_name varchar(10) comment '往来单位'
    ,dept_code varchar(10) comment '部门编码'
    ,dept_name varchar(10) comment '部门'
    ,stock_code varchar(10) comment '存货编码'
    ,stock_name varchar(10) comment '存货'
    ,user_code varchar(10) comment '人员编码'
    ,user_name varchar(10) comment '人员'
    ,project_code varchar(20) comment '项目编码'
    ,project_name varchar(100) comment '项目'
    ,ex_ass1_code varchar(20) comment '扩展辅助1编码'
    ,ex_ass1 varchar(100) comment '扩展辅助1'
    ,ex_ass2_code varchar(10) comment '扩展辅助2编码'
    ,ex_ass2 varchar(10) comment '扩展辅助2'
    ,ex_ass3_code varchar(10) comment '扩展辅助3编码'
    ,ex_ass3 varchar(10) comment '扩展辅助3'
    ,ex_ass4_code varchar(10) comment '扩展辅助4编码'
    ,ex_ass4 varchar(10) comment '扩展辅助4'
    ,ex_ass5_code varchar(10) comment '扩展辅助5编码'
    ,ex_ass5 varchar(10) comment '扩展辅助5'
    ,ex_ass6_code varchar(10) comment '扩展辅助6编码'
    ,ex_ass6 varchar(10) comment '扩展辅助6'
    ,ex_ass7_code varchar(10) comment '扩展辅助7编码'
    ,ex_ass7 varchar(10) comment '扩展辅助7'
    ,ex_ass8_code varchar(10) comment '扩展辅助8编码'
    ,ex_ass8 varchar(10) comment '扩展辅助8'
    ,ex_ass9_code varchar(10) comment '扩展辅助9编码'
    ,ex_ass9 varchar(10) comment '扩展辅助9'
    ,ex_ass10_code varchar(10) comment '扩展辅助10编码'
    ,ex_ass10 varchar(10) comment '扩展辅助10'
	,`dbCreateDt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'
);
alter table t_repay_stat add index `idx_batch_no`(`batch_no`) using BTREE;

CREATE TABLE `t_repay_gen_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `program_id` int(11) DEFAULT NULL COMMENT '程序编号',
  `batch_no` varchar(256) DEFAULT NULL COMMENT '批次号',
  `start_date` datetime DEFAULT NULL COMMENT '开始日期',
  `end_date` datetime DEFAULT NULL COMMENT '结束日期',
  `status` int(11) DEFAULT 0 COMMENT '状态0待执行，1已执行',
	`creator` varchar(25) DEFAULT null COMMENT '创建人',
	`updater` varchar(25) DEFAULT NULL COMMENT '更新人',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `dbCreateDt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `dbUpdateDt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`)

) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT '还款信息生成记录表';
alter table t_repay_gen_record add UNIQUE `uk_idx_batch_no`(`batch_no`) using BTREE
alter table t_repay_gen_record add index `idx_start_date`(`start_date`) using BTREE
alter table t_repay_gen_record add index `idx_end_date`(`end_date`) using BTREE



INSERT INTO`sys_job`(`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '还款账单每日生成', 'DEFAULT', 'autoGenerateRepayTask.autoGenerate(0)', '0 0 8 * * ?', '3', '1', '1', 'admin', NOW(), '', NULL, '');

