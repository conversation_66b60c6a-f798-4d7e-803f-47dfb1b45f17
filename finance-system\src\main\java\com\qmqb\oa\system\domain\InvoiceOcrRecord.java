package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qmqb.oa.common.annotation.Excel;
import com.qmqb.oa.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 发票OCR识别记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_invoice_ocr_record")
public class InvoiceOcrRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 审批实例ID
     */
    @Excel(name = "审批实例ID")
    private String processInstanceId;

    /**
     * 审批实例业务编号
     */
    @Excel(name = "审批实例业务编号")
    private String businessId;

    /**
     * 发票代码
     */
    @Excel(name = "发票代码")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @Excel(name = "发票号码")
    private String invoiceNumber;

    /**
     * 开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开票日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date invoiceDate;

    /**
     * 校验码
     */
    @Excel(name = "校验码")
    private String checkCode;

    /**
     * 发票金额
     */
    @Excel(name = "发票金额")
    private BigDecimal invoiceAmount;

    /**
     * 大写金额
     */
    @Excel(name = "大写金额")
    private String capitalAmount;

    /**
     * 发票税额
     */
    @Excel(name = "发票税额")
    private BigDecimal invoiceTax;

    /**
     * 不含税金额
     */
    @Excel(name = "不含税金额")
    private BigDecimal excludeTaxAmount;

    /**
     * 购买方名称
     */
    @Excel(name = "购买方名称")
    private String buyerName;

    /**
     * 购买方税号
     */
    @Excel(name = "购买方税号")
    private String buyerTaxNumber;

    /**
     * 购买方地址、电话
     */
    @Excel(name = "购买方地址、电话")
    private String buyerAddrPhone;

    /**
     * 购买方开户行、账号
     */
    @Excel(name = "购买方开户行、账号")
    private String buyerBankAccount;

    /**
     * 销售方名称
     */
    @Excel(name = "销售方名称")
    private String sellerName;

    /**
     * 销售方税号
     */
    @Excel(name = "销售方税号")
    private String sellerTaxNumber;

    /**
     * 销售方地址、电话
     */
    @Excel(name = "销售方地址、电话")
    private String sellerAddrPhone;

    /**
     * 销售方开户行、账号
     */
    @Excel(name = "销售方开户行、账号")
    private String sellerBankAccount;

    /**
     * 联次
     */
    @Excel(name = "联次")
    private String sheet;

    /**
     * 发票类型
     */
    @Excel(name = "发票类型")
    private String invoiceType;

    /**
     * 发票详单
     */
    @Excel(name = "发票详单")
    private String invoiceDetails;

    /**
     * 发票代码解析
     */
    @Excel(name = "发票代码解析")
    private String invoiceCodeParse;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    private String imageUrl;

    /**
     * 使用状态: 0-待确定, 1-已核销
     */
    private Integer useState;

    /**
     * 租户: 0-全民, 1-合众
     */
    private Integer tenantId;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 开票日期
     */
    @TableField(exist = false)
    private String[] invoiceDateRange;

}
