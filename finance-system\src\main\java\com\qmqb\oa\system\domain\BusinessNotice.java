package com.qmqb.oa.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qmqb.oa.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 业务提醒通知对象 t_business_notice
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_business_notice")
public class BusinessNotice {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 流水号
     */
    @Excel(name = "流水号")
    private String serialNumber;

    /**
     * 通知对象
     */
    @Excel(name = "通知对象")
    private String noticeObject;

    /**
     * 通知内容
     */
    @Excel(name = "通知内容")
    private String noticeContent;

    /**
     * 通知时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "通知时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date noticeTime;

    /**
     * 通知次数
     */
    @Excel(name = "通知次数")
    private Integer noticeCount;

    /**
     * 通知状态：1-待通知，2-通知成功，3-通知失败
     */
    @Excel(name = "通知状态：1-待通知，2-通知成功，3-通知失败")
    private Integer noticeStatus;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @Excel(name = "逻辑删除: 0-未删除, 1-删除")
    private Integer isDeleted;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台 5-汇法天下
     */
    @Excel(name = "主体", dictType = "finance_tenant_id")
    private Integer tenantId;

}
