package com.qmqb.oa.admin.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description ekb 发票类型
 * @Date 2024\11\6 0006 14:51
 * @Version 1.0
 */

@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EkbInvoiceTypeDTO {
    private String id;
    private String name;
    private String parentId;
    private String active;
    private String code;
}
