package com.qmqb.oa.admin.api.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkcontact_1_0.models.ListEmpLeaveRecordsHeaders;
import com.aliyun.dingtalkcontact_1_0.models.ListEmpLeaveRecordsRequest;
import com.aliyun.dingtalkcontact_1_0.models.ListEmpLeaveRecordsResponse;
import com.aliyun.dingtalkcontact_1_0.models.ListEmpLeaveRecordsResponseBody;
import com.aliyun.dingtalkhrm_1_0.models.QueryHrmEmployeeDismissionInfoHeaders;
import com.aliyun.dingtalkhrm_1_0.models.QueryHrmEmployeeDismissionInfoRequest;
import com.aliyun.dingtalkhrm_1_0.models.QueryHrmEmployeeDismissionInfoResponse;
import com.aliyun.dingtalkhrm_1_0.models.QueryHrmEmployeeDismissionInfoResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.hzed.structure.tool.util.RedisUtil;
import com.qmqb.oa.admin.config.DingTalkConfig;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.enums.DingTalkCodeEnum;
import com.qmqb.oa.common.enums.RedisCacheKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <p>
 * 钉钉新版API客户端
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DingTalkApiClient {

    private static com.aliyun.dingtalkoauth2_1_0.Client oauth_client;
    private static com.aliyun.dingtalkworkflow_1_0.Client workflow_client;
    private static com.aliyun.dingtalkcontact_1_0.Client contact_client;
    private static com.aliyun.dingtalkhrm_1_0.Client hrm_client;
    private static final String HEADER_TOKEN = "x-acs-dingtalk-access-token";

    private final DingTalkConfig dingTalkConfig;

    static {
        initClient();
    }

    private static void initClient() {
        try {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
            config.protocol = "https";
            config.regionId = "central";
            oauth_client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
            workflow_client = new com.aliyun.dingtalkworkflow_1_0.Client(config);
            contact_client = new com.aliyun.dingtalkcontact_1_0.Client(config);
            hrm_client = new com.aliyun.dingtalkhrm_1_0.Client(config);
        } catch (Exception e) {
            log.error("初始化DingTalk API Client失败");
        }
    }


    /**
     * 获取企业内部应用的accessToken
     *
     * @return
     */
    public String getAccessToken() {
        Integer tenantId = TenantContextHolder.getTenantId();
        String appkey = dingTalkConfig.getAppKey(tenantId);
        String appsecret = dingTalkConfig.getAppSecret(tenantId);
        RedisCacheKey keyEnum = RedisCacheKey.DINGTALK_API_ACCESS_TOKEN;
        String key = keyEnum.getKey(tenantId);
        String token = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(token)) {
            return token;
        }
        try {
            com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest req = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                    .setAppKey(appkey)
                    .setAppSecret(appsecret);
            log.info("[DingTalk API] 获取企业内部应用的accessToken, 请求参数:{}", JSON.toJSONString(req));
            GetAccessTokenResponse rsp = oauth_client.getAccessToken(req);
            log.info("[DingTalk API] 获取企业内部应用的accessToken, 响应参数:{}", JSON.toJSONString(rsp));
            String accessToken = rsp.body.accessToken;
            if (StrUtil.isNotEmpty(accessToken)) {
                RedisUtil.set(key, accessToken, keyEnum.getTimeout(), keyEnum.getTimeUnit());
            }
            return accessToken;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 获取企业内部应用的accessToken, 失败", err);
            }
        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 获取企业内部应用的accessToken, 失败", err);
            }
        }
        return null;
    }

    /**
     * 删除AccessToken
     *
     * @return
     */
    private void removeAccessToken() {
        String key = RedisCacheKey.DINGTALK_OAPI_ACCESS_TOKEN.getKey(dingTalkConfig.getAppKey(0));
        RedisUtil.del(key);
    }

    /**
     * 是否token失效,失效则清除token
     *
     * @param errCode
     * @param errMessage
     * @return
     */
    private boolean isTokenInvalid(String errCode, String errMessage) {
        DingTalkCodeEnum invalidAuthentication = DingTalkCodeEnum.INVALID_AUTHENTICATION;
        boolean invalid = StrUtil.equals(errCode, invalidAuthentication.getCode()) && StrUtil.equals(errMessage, invalidAuthentication.getMessage());
        if (invalid) {
            removeAccessToken();
        }
        return invalid;
    }

    /**
     * 获取审批实例ID列表
     *
     * @param processCode 审批流的唯一码
     * @param startTime   审批实例开始时间。Unix时间戳，单位毫秒。
     * @param endTime     审批实例结束时间，Unix时间戳，单位毫秒。
     * @param token       分页查询的游标，最开始传0，后续传返回参数中的nextToken值。
     * @param userIds     发起人userId列表，最大列表长度为10。
     * @param statuses    流程实例状态：
     *                    <p>
     *                    NEW：新创建
     *                    RUNNING：审批中
     *                    TERMINATED：被终止
     *                    COMPLETED：完成
     *                    CANCELED：取消
     * @return
     */
    public List<String> listProcessInstanceIds(String processCode, Long startTime, Long endTime, Long token, List<String> userIds, List<String> statuses) {
        try {
            ListProcessInstanceIdsHeaders headers = new ListProcessInstanceIdsHeaders();
            String accessToken = getAccessToken();
            Map<String, String> commonHeaders = new HashMap<>(2);
            commonHeaders.put(HEADER_TOKEN, accessToken);
            headers.commonHeaders = commonHeaders;
            ListProcessInstanceIdsRequest req = new ListProcessInstanceIdsRequest()
                    .setProcessCode(processCode)
                    .setStartTime(startTime)
                    .setEndTime(endTime)
                    .setNextToken(Objects.isNull(token) ? 0L : token)
                    .setUserIds(userIds)
                    .setStatuses(statuses)
                    .setMaxResults(20L);
            ListProcessInstanceIdsResponse rsp = workflow_client.listProcessInstanceIdsWithOptions(req, headers, new RuntimeOptions());
            if (!rsp.body.success || Objects.isNull(rsp.body.result)) {
                log.error("[DingTalk API] 获取审批实例ID列表, 调用失败");
                return Collections.emptyList();
            }
            List<String> ids = new ArrayList<>(16);
            ids.addAll(rsp.body.result.list);
            String nextToken = rsp.body.result.nextToken;
            while (Objects.nonNull(nextToken)) {
                ListProcessInstanceIdsRequest nextReq = new ListProcessInstanceIdsRequest()
                        .setProcessCode(processCode)
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .setNextToken(Long.valueOf(nextToken))
                        .setUserIds(userIds)
                        .setStatuses(statuses)
                        .setMaxResults(20L);
                ListProcessInstanceIdsResponse nextRsp = workflow_client.listProcessInstanceIdsWithOptions(nextReq, headers, new RuntimeOptions());
                ids.addAll(nextRsp.body.result.list);
                nextToken = nextRsp.body.result.nextToken;
            }
            return ids;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                if (isTokenInvalid(err.code, err.message)) {
                    listProcessInstanceIds(processCode, token, startTime, endTime, userIds, statuses);
                }
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 获取审批实例ID列表, 失败", err);
            }

        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 获取审批实例ID列表, 失败", err);
            }

        }
        return Collections.emptyList();
    }

    /**
     * 获取单个审批实例详情
     *
     * @param processInstanceId 审批实例ID
     * @return
     */
    public GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult getProcessInstance(String processInstanceId) {
        try {
            GetProcessInstanceHeaders headers = new GetProcessInstanceHeaders();
            String accessToken = getAccessToken();
            Map<String, String> commonHeaders = new HashMap<>(2);
            commonHeaders.put(HEADER_TOKEN, accessToken);
            headers.commonHeaders = commonHeaders;
            GetProcessInstanceRequest req = new GetProcessInstanceRequest()
                    .setProcessInstanceId(processInstanceId);
            GetProcessInstanceResponse rsp = workflow_client.getProcessInstanceWithOptions(req, headers, new RuntimeOptions());
            if (!Boolean.parseBoolean(rsp.body.success) || Objects.isNull(rsp.body.result)) {
                log.error("[DingTalk API] 获取单个审批实例详情, 调用失败");
                return null;
            }
            return rsp.body.result;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                if (isTokenInvalid(err.code, err.message)) {
                    getProcessInstance(processInstanceId);
                }
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 获取单个审批实例详情, 失败", err);
            }
        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 获取单个审批实例详情, 失败", err);
            }
        }
        return null;
    }

    /**
     * 添加审批评论
     *
     * @param processInstanceId 审批实例ID
     * @param commentUserId     评论人的userid
     * @param attachments       附件列表
     * @param photos            图片URL地址
     * @param text              评论的内容
     * @return
     */
    public Boolean addProcessInstanceComment(String processInstanceId,
                                             String commentUserId,
                                             List<AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFileAttachments> attachments,
                                             List<String> photos,
                                             String text) {
        try {
            AddProcessInstanceCommentHeaders headers = new AddProcessInstanceCommentHeaders();
            String accessToken = getAccessToken();
            Map<String, String> commonHeaders = new HashMap<>(2);
            commonHeaders.put(HEADER_TOKEN, accessToken);
            headers.commonHeaders = commonHeaders;
            AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFile file = new AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFile()
                    .setPhotos(photos)
                    .setAttachments(attachments);
            AddProcessInstanceCommentRequest req = new AddProcessInstanceCommentRequest()
                    .setProcessInstanceId(processInstanceId)
                    .setText(text)
                    .setCommentUserId(commentUserId)
                    .setFile(file);
            AddProcessInstanceCommentResponse rsp = workflow_client.addProcessInstanceCommentWithOptions(req, headers, new RuntimeOptions());
            return rsp.body.success && rsp.body.result;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                if (isTokenInvalid(err.code, err.message)) {
                    addProcessInstanceComment(processInstanceId, commentUserId, attachments, photos, text);
                }
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 添加审批评论, 失败", err);
            }
        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 添加审批评论, 失败", err);
            }
        }
        return false;
    }

    /**
     * 同意或拒绝审批任务
     *
     * @param processInstanceId 审批实例ID
     * @param actionerUserId    操作人userid，可通过调用获取审批实例详情接口获取。
     * @param attachments       附件列表
     * @param photos            图片URL地址
     * @param remark            审批意见，可为空
     * @param result            审批操作，取值。agree：同意 refuse：拒绝
     * @param taskId            任务节点id，可通过调用获取审批实例详情接口获取。
     * @return
     */
    public Boolean executeProcessInstance(String processInstanceId,
                                          String actionerUserId,
                                          List<ExecuteProcessInstanceRequest.ExecuteProcessInstanceRequestFileAttachments> attachments,
                                          List<String> photos,
                                          String remark,
                                          String result,
                                          Long taskId) {
        try {
            ExecuteProcessInstanceHeaders headers = new ExecuteProcessInstanceHeaders();
            String accessToken = getAccessToken();
            Map<String, String> commonHeaders = new HashMap<>(2);
            commonHeaders.put(HEADER_TOKEN, accessToken);
            headers.commonHeaders = commonHeaders;
            ExecuteProcessInstanceRequest.ExecuteProcessInstanceRequestFile file = new ExecuteProcessInstanceRequest.ExecuteProcessInstanceRequestFile()
                    .setPhotos(photos)
                    .setAttachments(attachments);
            ExecuteProcessInstanceRequest req = new ExecuteProcessInstanceRequest()
                    .setProcessInstanceId(processInstanceId)
                    .setRemark(remark)
                    .setResult(result)
                    .setActionerUserId(actionerUserId)
                    .setTaskId(taskId)
                    .setFile(file);
            ExecuteProcessInstanceResponse rsp = workflow_client.executeProcessInstanceWithOptions(req, headers, new RuntimeOptions());
            return rsp.body.success && rsp.body.result;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                if (isTokenInvalid(err.code, err.message)) {
                    executeProcessInstance(processInstanceId, actionerUserId, attachments, photos, remark, result, taskId);
                }
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 同意或拒绝审批任务, 失败", err);
            }
        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 同意或拒绝审批任务, 失败", err);
            }
        }
        return false;
    }

    /**
     * 查询离职记录列表
     *
     * @param startTime 如果该参数不传，开始时间距离当前时间不能超过365天。
     * @param endTime   如果该参数传参，开始时间和结束时间跨度不能超过365天。
     * @return
     */
    public List<ListEmpLeaveRecordsResponseBody.ListEmpLeaveRecordsResponseBodyRecords> listEmpLeaveRecordsWithOptions(Date startTime, Date endTime) {
        try {
            String accessToken = getAccessToken();
            Map<String, String> commonHeaders = new HashMap<>(2);
            commonHeaders.put(HEADER_TOKEN, accessToken);
            ListEmpLeaveRecordsHeaders headers = new ListEmpLeaveRecordsHeaders();
            headers.commonHeaders = commonHeaders;
            ListEmpLeaveRecordsRequest listEmpLeaveRecordsRequest = new ListEmpLeaveRecordsRequest()
                    .setStartTime(Objects.nonNull(startTime) ? DateUtil.format(startTime, DatePattern.UTC_PATTERN) : null)
                    .setEndTime(Objects.nonNull(endTime) ? DateUtil.format(endTime, DatePattern.UTC_PATTERN) : null)
                    .setNextToken("0")
                    .setMaxResults(50);
            ListEmpLeaveRecordsResponse rsp = contact_client.listEmpLeaveRecordsWithOptions(listEmpLeaveRecordsRequest, headers, new RuntimeOptions());

            List<ListEmpLeaveRecordsResponseBody.ListEmpLeaveRecordsResponseBodyRecords> records = new ArrayList<>(16);
            records.addAll(rsp.body.records);
            String nextToken = rsp.body.nextToken;
            while (Objects.nonNull(nextToken)) {

                ListEmpLeaveRecordsRequest nextReq = new ListEmpLeaveRecordsRequest()
                        .setStartTime(Objects.nonNull(startTime) ? DateUtil.format(startTime, DatePattern.UTC_PATTERN) : null)
                        .setEndTime(Objects.nonNull(endTime) ? DateUtil.format(endTime, DatePattern.UTC_PATTERN) : null)
                        .setNextToken(nextToken)
                        .setMaxResults(50);
                ListEmpLeaveRecordsResponse nextRsp = contact_client.listEmpLeaveRecordsWithOptions(nextReq, headers, new RuntimeOptions());

                records.addAll(nextRsp.body.records);
                nextToken = nextRsp.body.nextToken;
            }
            return records;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 查询离职记录列表, 失败", err);
            }
        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 查询离职记录列表, 失败", err);
            }

        }
        return Collections.emptyList();
    }

    /**
     * 批量获取员工离职信息
     *
     * @param userIds
     * @return
     */
    public List<QueryHrmEmployeeDismissionInfoResponseBody.QueryHrmEmployeeDismissionInfoResponseBodyResult> queryHrmEmployeeDismissionInfoWithOptions(List<String> userIds) {
        try {
            String accessToken = getAccessToken();
            Map<String, String> commonHeaders = new HashMap<>(2);
            commonHeaders.put(HEADER_TOKEN, accessToken);
            QueryHrmEmployeeDismissionInfoHeaders headers = new QueryHrmEmployeeDismissionInfoHeaders();
            headers.commonHeaders = commonHeaders;
            if (CollUtil.size(userIds) < 50) {
                QueryHrmEmployeeDismissionInfoRequest queryHrmEmployeeDismissionInfoRequest = new QueryHrmEmployeeDismissionInfoRequest()
                        .setUserIdList(userIds);
                QueryHrmEmployeeDismissionInfoResponse rsp = hrm_client.queryHrmEmployeeDismissionInfoWithOptions(queryHrmEmployeeDismissionInfoRequest, headers, new RuntimeOptions());
                return rsp.body.result;
            }
            List<QueryHrmEmployeeDismissionInfoResponseBody.QueryHrmEmployeeDismissionInfoResponseBodyResult> results = new ArrayList<>(userIds.size());
            List<List<String>> lists = CollUtil.splitList(userIds, 50);
            for (List<String> list : lists) {
                QueryHrmEmployeeDismissionInfoRequest queryHrmEmployeeDismissionInfoRequest = new QueryHrmEmployeeDismissionInfoRequest()
                        .setUserIdList(list);
                QueryHrmEmployeeDismissionInfoResponse rsp = hrm_client.queryHrmEmployeeDismissionInfoWithOptions(queryHrmEmployeeDismissionInfoRequest, headers, new RuntimeOptions());
                results.addAll(rsp.body.result);
            }
            return results;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 批量获取员工离职信息, 失败", err);
            }

        } catch (Exception e) {
            TeaException err = new TeaException(e.getMessage(), e);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("[DingTalk API] 批量获取员工离职信息, 失败", err);
            }

        }
        return Collections.emptyList();
    }

}
