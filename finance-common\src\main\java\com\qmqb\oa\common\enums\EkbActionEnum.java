package com.qmqb.oa.common.enums;

import com.hzed.structure.tool.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description ekb 工作流action枚举
 * @Date 2024\10\11 0011 16:35
 * @Version 1.0
 */

@AllArgsConstructor
@Getter
public enum EkbActionEnum {
    FLOW_REJECTED("flow.rejected","被驳回", "ekbRejectedCommand"),
    FREEFLOW_RETRACT("freeflow.retract","单据撤回", "ekbRetractCommand"),
    FREEFLOW_DELETE("freeflow.delete","单据删除", "ekbDeleteCommand"),
    BACKLOG_SENDING("backlog.sending","待寄送", "ekbSendingCommand"),
    FLOW_PAID("flow.paid","已支付/审批完成", "ekbPaidCommand"),
    FREEFLOW_MENTION("freeflow.mention","被@", "ekbMentionCommand"),
    BACKLOG_PAYING("backlog.paying","待支付", "ekbPayingCommand"),
    FREEFLOW_COMMENT("freeflow.comment","评论", "ekbCommentCommand"),
    BACKLOG_APPROVING("backlog.approving","待审批", "ekbApprovingCommand"),
    BACKLOG_PROCESSED("backlog.processed","已处理", "ekbProcessedCommand"),
    FREEFLOW_CARBON_COPY("freeflow.carbonCopy","抄送", "ekbCarbonCopyCommand"),


    ;

    private final String action;
    private final String description;
    /**
     * 事件处理bean 名称
     */
    private final String commandBeanName;
    public static EkbActionEnum getEkbActionEnum(String action) {
        return Arrays.stream(EkbActionEnum.values()).filter(e -> StringUtil.equals(e.getAction(), action)).findFirst().orElse(null);
    }
}
