package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

/**
 * 访问临时授权rep
 * <AUTHOR>
 * @date 2024/7/19
 */
@Data
public class EkbProvisionalAuthRsp {

    /**
     *  请求状态 true = 响应成功，false = 失败
     */
    private String code;

    /**
     * "https://app.ekuaibao.com/applet/thirdparty.html?accessToken=SGYqVpXcuhIIYQJkd0w2G0&ekbCorpId=34A73EyI8A0w00&pageType=home&overdueTokenRedirect=[https://www.ekuaibao.com](https://www.ekuaibao.com)",
     * //第三方临时访问合思URL
     */
    private String message;
}
