package com.qmqb.oa.admin.service;

import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.admin.task.ReimbursementProcessTask;
import com.qmqb.oa.common.enums.ProcessType;
import com.qmqb.oa.common.enums.Tenant;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ReimbursementCodeTaskTest extends BaseTest {

    @Autowired
    private ReimbursementProcessTask reimbursementProcessTask;

    @Test
    public void tbProcess() {
        reimbursementProcessTask.tb(Tenant.QMQB.getValue());
    }

    @Test
    public void mealProcess() {
        reimbursementProcessTask.meal(Tenant.QMQB.getValue());
    }

    @Test
    public void voucher() {
        reimbursementProcessTask.voucher(Tenant.QMQB.getValue());
    }

    @Test
    public void executeProcess() {
        TenantContextHolder.setTenantId(Tenant.QMQB.getValue());
        reimbursementProcessTask.executeProcess("yeTn9zK4SiqDY6V2xfBx0g02371717215998", ProcessType.MEAL);
    }
}
