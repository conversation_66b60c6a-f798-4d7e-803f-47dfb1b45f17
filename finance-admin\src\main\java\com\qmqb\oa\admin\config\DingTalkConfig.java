package com.qmqb.oa.admin.config;

import com.qmqb.oa.common.enums.Tenant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

/**
 * <p>
 * 钉钉配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {

    /**
     * 全民钱包
     */
    @NestedConfigurationProperty
    private Prop qmqb;
    /**
     * 深圳合众
     */
    @NestedConfigurationProperty
    private Prop szhz;
    /**
     * 佛山百益来信息咨询有限公司
     */
    @NestedConfigurationProperty
    private Prop fsbyl;
    /**
     * OA办公平台
     */
    @NestedConfigurationProperty
    private Prop oaop;

    /**
     * 法汇天下
     */
    @NestedConfigurationProperty
    private Prop fhtx;

    /**
     * 仲建信息咨询有限公司
     */
    @NestedConfigurationProperty
    private Prop zj;


    @Data
    public static class Prop {
        /**
         * 应用的唯一标识key
         */
        private String appKey;
        /**
         * 应用的密钥
         */
        private String appSecret;
        /**
         * 模板code
         */
        private TemplateCode process;

        @Data
        public static class TemplateCode {
            /**
             * 303.加班餐费/车费报销
             */
            private String meal;
            /**
             * 304.团建费报销申请
             */
            private String tb;
            /**
             * 304.付款申请
             */
            private String payApply;
            /**
             * 311.开票申请
             */
            private String billingApply;
            /**
             * 407.入职审批
             */
            private String entryApproval;
        }
    }

    public String getAppKey(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.appKey;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.appKey;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.appKey;
        } else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.appKey;
        } else if (Objects.equals(tenantId, Tenant.ZJ.getValue())) {
            return this.zj.appKey;
        } else {
            return this.qmqb.appKey;
        }
    }

    public String getAppSecret(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.appSecret;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.appSecret;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.appSecret;
        } else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.appSecret;
        } else if (Objects.equals(tenantId, Tenant.ZJ.getValue())) {
            return this.zj.appSecret;
        } else {
            return this.qmqb.appSecret;
        }
    }

    public String getMeal(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.process.meal;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.process.meal;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.process.meal;
        } else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.process.meal;
        } else {
            return this.qmqb.process.meal;
        }
    }

    public String getTb(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.process.tb;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.process.tb;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.process.tb;
        } else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.process.tb;
        } else {
            return this.qmqb.process.tb;
        }
    }

    public String getPayApply(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.process.payApply;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.process.payApply;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.process.payApply;
        } else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.process.payApply;
        } else {
            return this.qmqb.process.payApply;
        }
    }

    public String getBillingApply(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.process.billingApply;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.process.billingApply;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.process.billingApply;
        } else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.process.billingApply;
        } else {
            return this.qmqb.process.billingApply;
        }
    }

    public String getEntryApproval(Integer tenantId) {
        if (Objects.equals(tenantId, Tenant.SZHZ.getValue())) {
            return this.szhz.process.entryApproval;
        } else if (Objects.equals(tenantId, Tenant.FSBYL.getValue())) {
            return this.fsbyl.process.entryApproval;
        } else if (Objects.equals(tenantId, Tenant.OAOP.getValue())) {
            return this.oaop.process.entryApproval;
        }else if (Objects.equals(tenantId, Tenant.FHTX.getValue())) {
            return this.fhtx.process.entryApproval;
        } else if (Objects.equals(tenantId, Tenant.ZJ.getValue())) {
            return this.zj.process.entryApproval;
        } else {
            return this.qmqb.process.entryApproval;
        }
    }
}
