package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.system.domain.RepayStatJindie;
import com.qmqb.oa.system.mapper.RepayStatJindieMapper;
import com.qmqb.oa.system.service.RepayStatJindieService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Service
public class RepayStatJindieServiceImpl extends ServiceImpl<RepayStatJindieMapper, RepayStatJindie> implements RepayStatJindieService {

    @Override
    public List<RepayStatJindie> listExportBy(String batchNo) {
        return this.baseMapper.selectList(new QueryWrapper<RepayStatJindie>().eq("batch_no", batchNo));
    }
}
