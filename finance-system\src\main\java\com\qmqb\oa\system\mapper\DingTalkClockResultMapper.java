package com.qmqb.oa.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.DingTalkClockResult;
import com.qmqb.oa.system.domain.vo.ClockStatisticsVo;
import org.apache.ibatis.annotations.Param;

/**
 * 钉钉打卡结果Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
public interface DingTalkClockResultMapper extends BaseMapper<DingTalkClockResult> {
    /**
     * 查询钉钉打卡结果
     *
     * @param id 钉钉打卡结果ID
     * @return 钉钉打卡结果
     */
        DingTalkClockResult selectDingtalkClockResultById(Long id);

    /**
     * 查询钉钉打卡结果列表
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 钉钉打卡结果集合
     */
    List<DingTalkClockResult> selectDingtalkClockResultList(DingTalkClockResult dingtalkClockResult);

    /**
     * 新增钉钉打卡结果
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 结果
     */
    int insertDingtalkClockResult(DingTalkClockResult dingtalkClockResult);

    /**
     * 修改钉钉打卡结果
     *
     * @param dingtalkClockResult 钉钉打卡结果
     * @return 结果
     */
    int updateDingtalkClockResult(DingTalkClockResult dingtalkClockResult);

    /**
     * 删除钉钉打卡结果
     *
     * @param id 钉钉打卡结果ID
     * @return 结果
     */
    int deleteDingtalkClockResultById(Long id);

    /**
     * 批量删除钉钉打卡结果
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteDingtalkClockResultByIds(Long[] ids);

    List<ClockStatisticsVo> statistics(@Param("beginTime") String beginTime,
                                       @Param("endTime") String endTime,
                                       @Param("tenantId") Integer tenantId,
                                       @Param("userName") String userName);

}
