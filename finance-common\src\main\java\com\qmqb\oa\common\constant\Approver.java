package com.qmqb.oa.common.constant;

import cn.hutool.core.collection.ListUtil;

import java.util.List;

/**
 * <p>
 * 审批人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface Approver {
    /**
     * 全民办公自动化机器人
     */
    String QMQB_ROBOT_ID = "02302619220023330106";
    /**
     * 深圳合众办公自动化机器人
     * 财务自动化
     */
    String SZHZ_ROBOT_ID = "2117212129-336654355";
    /**
     * 佛山百益来办公自动化机器人
     */
    String FSBYL_ROBOT_ID = "2740532769-1288765558";
    /**
     * OA架构办公自动化机器人
     */
    String OAA_ROBOT_ID = "023026192200-1288765558";

    /**
     * 法汇天下办公自动化机器人 同OAA_ROBOT_ID
     */
    String FHTX_ROBOT_ID = "023026192200-1288765558";

    /**
     * 仲建办公自动化机器人
     */
    String ZJ_ROBOT_ID = "0230261922001301184870";
    /**
     * 办公自动化机器人
     */
    List<String> ROBOT_IDS = ListUtil.toList(QMQB_ROBOT_ID, SZHZ_ROBOT_ID, FSBYL_ROBOT_ID, OAA_ROBOT_ID, ZJ_ROBOT_ID);
    /**
     * 曾怡
     */
    String ZENG_YI_ID = "0957531256841955";
    /**
     * 欧阳莹
     */
    String OU_YANG_YING_ID = "0559186808888812";
    /**
     * 郑记纯
     */
    String ZHENG_JI_CHUN_ID = "291159030536768144";
    /**
     * 佘佩
     */
    String SHE_PEI_ID = "090945395232008288";
    /**
     * 段婉珠
     */
    String DUAN_WAN_ZHU_ID = "1735650738133073";
    /**
     * 梁玉艳
     */
    String LIANG_YU_YAN_ID = "181463073032532842";
}
