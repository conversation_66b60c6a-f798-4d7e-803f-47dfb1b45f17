<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.EmployeeSubjectMapper">
    <resultMap type="com.qmqb.oa.system.domain.EmployeeSubject" id="EmployeeSubjectResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="jobNumber" column="job_number"/>
        <result property="rawCompany" column="raw_company"/>
        <result property="company" column="company"/>
        <result property="workPlace" column="work_place"/>
        <result property="center" column="center"/>
        <result property="dept" column="dept"/>
        <result property="group" column="group"/>
        <result property="lowGroup" column="low_group"/>
        <result property="title" column="title"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectEmployeeSubjectVo">
        select id,
               `name`,
               job_number,
               raw_company,
               company,
               work_place,
               center,
               dept,
               `group`,
               low_group,
               title,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               is_deleted,
               tenant_id
        from t_employee_subject
    </sql>

    <select id="selectEmployeeSubjectList" parameterType="com.qmqb.oa.system.domain.EmployeeSubject"
            resultMap="EmployeeSubjectResult">
        <include refid="selectEmployeeSubjectVo"/>
        <where>
            <if test="name != null  and name != ''">
                and `name` like concat('%', #{name}, '%')
            </if>
            <if test="jobNumber != null  and jobNumber != ''">
                and job_number = #{jobNumber}
            </if>
            <if test="rawCompany != null  and rawCompany != ''">
                and raw_company = #{rawCompany}
            </if>
            <if test="company != null  and company != ''">
                and company = #{company}
            </if>
            <if test="isDeleted != null ">
                and is_deleted = #{isDeleted}
            </if>
            <if test="tenantId != null ">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="selectEmployeeSubjectById" parameterType="Long"
            resultMap="EmployeeSubjectResult">
        <include refid="selectEmployeeSubjectVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmployeeSubject" parameterType="com.qmqb.oa.system.domain.EmployeeSubject"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into t_employee_subject
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="jobNumber != null">
                job_number,
            </if>
            <if test="rawCompany != null">
                raw_company,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="workPlace != null">
                work_place,
            </if>
            <if test="center != null">
                center,
            </if>
            <if test="dept != null">
                dept,
            </if>
            <if test="group != null">
                `group`,
            </if>
            <if test="lowGroup != null">
                low_group,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name},
            </if>
            <if test="jobNumber != null">
                #{jobNumber},
            </if>
            <if test="rawCompany != null">
                #{rawCompany},
            </if>
            <if test="company != null">
                #{company},
            </if>
            <if test="workPlace != null">
                #{workPlace},
            </if>
            <if test="center != null">
                #{center},
            </if>
            <if test="dept != null">
                #{dept},
            </if>
            <if test="group != null">
                #{group},
            </if>
            <if test="lowGroup != null">
                #{lowGroup},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <update id="updateEmployeeSubject" parameterType="com.qmqb.oa.system.domain.EmployeeSubject">
        update t_employee_subject
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">
                `name` =
                #{name},
            </if>
            <if test="jobNumber != null">
                job_number =
                #{jobNumber},
            </if>
            <if test="rawCompany != null">
                raw_company =
                #{rawCompany},
            </if>
            <if test="company != null">
                company =
                #{company},
            </if>
            <if test="workPlace != null">
                work_place =
                #{workPlace},
            </if>
            <if test="center != null">
                center =
                #{center},
            </if>
            <if test="dept != null">
                dept =
                #{dept},
            </if>
            <if test="group != null">
                `group` =
                #{group},
            </if>
            <if test="lowGroup != null">
                low_group =
                #{lowGroup},
            </if>
            <if test="title != null">
                title =
                #{title},
            </if>
            <if test="remark != null">
                remark =
                #{remark},
            </if>
            <if test="createBy != null">
                create_by =
                #{createBy},
            </if>
            <if test="createTime != null">
                create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">
                update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time =
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted =
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                tenant_id =
                #{tenantId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeSubjectById" parameterType="Long">
        delete
        from t_employee_subject
        where id = #{id}
    </delete>

    <delete id="deleteEmployeeSubjectByIds" parameterType="String">
        delete from t_employee_subject where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listByNames" resultMap="EmployeeSubjectResult">
        select * from t_employee_subject where `name` in
        <foreach item="name" collection="names" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>
</mapper>
