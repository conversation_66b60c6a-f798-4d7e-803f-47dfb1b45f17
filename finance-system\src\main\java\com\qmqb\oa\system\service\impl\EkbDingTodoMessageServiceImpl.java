package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.system.domain.EkbDingTodoMessage;
import com.qmqb.oa.system.mapper.EkbDingTodoMessageMapper;
import com.qmqb.oa.system.service.EkbDingTodoMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 易快报到钉钉消息二次处理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Service
public class EkbDingTodoMessageServiceImpl extends ServiceImpl<EkbDingTodoMessageMapper, EkbDingTodoMessage> implements EkbDingTodoMessageService {
    @Resource
    private EkbDingTodoMessageMapper ekbDingTodoMessageMapper;
    @Override
    public EkbDingTodoMessage findOneByFlowIdAndMessageId(String flowId, String messageId, String dingUnionId) {
        LambdaQueryWrapper<EkbDingTodoMessage> queryWrapper = Wrappers.lambdaQuery(EkbDingTodoMessage.class)
                .eq(EkbDingTodoMessage::getEkbFlowId, flowId)
                .eq(EkbDingTodoMessage::getEkbMessageId, messageId)
                .eq(EkbDingTodoMessage::getDingUnionId, dingUnionId);
        return getOne(queryWrapper);
    }

    @Override
    public EkbDingTodoMessage findOneByFlowIdAndEkbMsgAction(String flowId, String ekbMsgAction) {
        LambdaQueryWrapper<EkbDingTodoMessage> queryWrapper = Wrappers.lambdaQuery(EkbDingTodoMessage.class)
                .eq(EkbDingTodoMessage::getEkbFlowId, flowId)
                .eq(EkbDingTodoMessage::getEkbMsgAction, ekbMsgAction);
        return getOne(queryWrapper);
    }

    @Override
    public List<EkbDingTodoMessage> findListByFlowIdAndEkbMsgAction(String flowId, String ekbMsgAction,String dingUnionId) {
        LambdaQueryWrapper<EkbDingTodoMessage> queryWrapper = Wrappers.lambdaQuery(EkbDingTodoMessage.class)
                .eq(EkbDingTodoMessage::getEkbFlowId, flowId)
                .eq(EkbDingTodoMessage::getEkbMsgAction, ekbMsgAction)
                .eq(StringUtil.isNotBlank(dingUnionId), EkbDingTodoMessage::getDingUnionId, dingUnionId)
                .orderByDesc(EkbDingTodoMessage::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public List<EkbDingTodoMessage> findListByFlowIdAndEkbMsgActions(String flowId, List<String> ekbMsgActions) {
        LambdaQueryWrapper<EkbDingTodoMessage> queryWrapper = Wrappers.lambdaQuery(EkbDingTodoMessage.class)
                .eq(EkbDingTodoMessage::getEkbFlowId, flowId)
                .in(EkbDingTodoMessage::getEkbMsgAction, ekbMsgActions)
                .orderByDesc(EkbDingTodoMessage::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public int setMultiRemarkInfo(Long[] ids, Map<String, Object> params) {
        return this.baseMapper.setMultiRemarkInfo(ids, params);
    }

    @Override
    public List<EkbDingTodoMessage> todoListByFlowIdAndUserIdSet(String flowId, Set<String> userIdSet) {
        return ekbDingTodoMessageMapper.todoListByFlowIdAndUserIdSet(flowId, userIdSet);
    }
}
