package com.qmqb.oa.admin.domain.vo;

import cn.hutool.db.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/4/26
 */
@Data
public class RepayDetailVo{

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 批次号生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dbCreateDt;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endDate;

    /**
     * 文件路径
     */
    private String filePath;

}
