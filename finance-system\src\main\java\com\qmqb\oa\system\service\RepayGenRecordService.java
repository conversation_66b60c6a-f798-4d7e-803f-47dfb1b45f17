package com.qmqb.oa.system.service;

import com.qmqb.oa.common.core.domain.dto.RepayGenConditionDto;
import com.qmqb.oa.system.domain.RepayGenRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 还款信息生成记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface RepayGenRecordService extends IService<RepayGenRecord> {

    /**
     * 根据条件查询
     * @param condition
     * @return
     */
    List<RepayGenRecord> listByCondition(RepayGenConditionDto condition);

    /**
     * 通过id主键 获取对应批次号
     *
     * @param id
     * @return
     */
    String getBatchNoById(Long id);

}
