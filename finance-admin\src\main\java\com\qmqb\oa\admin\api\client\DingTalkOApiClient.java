package com.qmqb.oa.admin.api.client;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.hzed.structure.common.util.BeanUtil;
import com.hzed.structure.common.util.ReflectUtil;
import com.qmqb.oa.admin.config.DingTalkConfig;
import com.qmqb.oa.admin.domain.DingTalkMsg;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.core.redis.RedisCache;
import com.qmqb.oa.common.enums.RedisCacheKey;
import com.qmqb.oa.common.exception.CustomException;
import com.taobao.api.internal.mapping.ApiField;
import com.taobao.api.internal.util.json.JSONWriter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <p>
 * 钉钉旧版API客户端
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DingTalkOApiClient {

    private final RedisCache redisCache;
    private final DingTalkConfig dingTalkConfig;

    /**
     * 鉴权异常
     */
    private static final long ERR_CODE = 88L;
    /**
     * 不合法的access_token
     */
    private static final String SUB_ERR_CODE = "40014";

    /**
     * 获取AccessToken
     *
     * @return
     */
    @SneakyThrows
    public String getAccessToken() {
        Integer tenantId = TenantContextHolder.getTenantId();
        RedisCacheKey key = RedisCacheKey.DINGTALK_OAPI_ACCESS_TOKEN;
        String token = redisCache.getCacheObject(key.getKey(tenantId));
        if (StrUtil.isNotEmpty(token)) {
            return token;
        }
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest req = new OapiGettokenRequest();
        req.setAppkey(dingTalkConfig.getAppKey(tenantId));
        req.setAppsecret(dingTalkConfig.getAppSecret(tenantId));
        OapiGettokenResponse rsp = client.execute(req);
        if (!rsp.isSuccess()) {
            log.info("[DingTalk API] 获取企业内部应用的access_token失败, 响应参数:{}", new JSONWriter().write(rsp));
            throw new CustomException(rsp.getErrmsg());
        }
        String accessToken = rsp.getAccessToken();
        if (StrUtil.isNotEmpty(accessToken)) {
            redisCache.setCacheObject(key.getKey(tenantId), accessToken, key.getTimeout(), key.getTimeUnit());
        }
        return accessToken;
    }

    /**
     * 删除AccessToken
     *
     * @return
     */
    private void removeAccessToken() {
        Integer tenantId = TenantContextHolder.getTenantId();
        RedisCacheKey key = RedisCacheKey.DINGTALK_OAPI_ACCESS_TOKEN;
        redisCache.deleteObject(key.getKey(tenantId));
    }

    /**
     * 是否token失效,失效则清除token
     *
     * @param errCode
     * @param subCode
     * @return
     */
    private boolean isTokenInvalid(Long errCode, String subCode) {
        boolean invalid = Objects.equals(errCode, ERR_CODE) && Objects.equals(subCode, SUB_ERR_CODE);
        if (invalid) {
            removeAccessToken();
        }
        return invalid;
    }

    /**
     * 获取审批实例ID列表
     *
     * @param processCode 审批流的唯一码
     * @param cursor      分页查询的游标，最开始传0，后续传返回参数中的next_cursor值
     * @param cursor      开始时间
     * @param cursor      结束时间
     * @return
     */
    @SneakyThrows
    public List<String> listProcessInstanceIds(String processCode, Long cursor, Long beginTime, Long endTime) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/listids");
        OapiProcessinstanceListidsRequest req = new OapiProcessinstanceListidsRequest();
        req.setProcessCode(processCode);
        req.setStartTime(beginTime);
        req.setEndTime(endTime);
        req.setSize(20L);
        req.setCursor(Objects.isNull(cursor) ? 0L : cursor);
        OapiProcessinstanceListidsResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.listProcessInstanceIds(processCode, cursor, beginTime, endTime);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        OapiProcessinstanceListidsResponse.PageResult pageResult = rsp.getResult();
        List<String> ids = new ArrayList<>(pageResult.getList());
        Long nextCursor = pageResult.getNextCursor();
        if (Objects.nonNull(nextCursor)) {
            List<String> nextIds = listProcessInstanceIds(processCode, nextCursor, beginTime, endTime);
            ids.addAll(nextIds);
        }
        return ids;
    }

    /**
     * 获取模板code
     *
     * @return
     */
    @SneakyThrows
    public OapiProcessGetByNameResponse getPcode() {

        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/get_by_name");
        OapiProcessGetByNameRequest req = new OapiProcessGetByNameRequest();
        req.setName("111111.团建费报销申请-测试");
        return client.execute(req, getAccessToken());
    }

    /**
     * 获取审批实例详情
     *
     * @param processInstanceId 审批实例ID
     * @return
     */
    @SneakyThrows
    public OapiProcessinstanceGetResponse.ProcessInstanceTopVo getProcessInstance(String processInstanceId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest req = new OapiProcessinstanceGetRequest();
        req.setProcessInstanceId(processInstanceId);
        OapiProcessinstanceGetResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.getProcessInstance(processInstanceId);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        return rsp.getProcessInstance();
    }

    /**
     * 下载审批附件
     *
     * @param processInstanceId 审批实例ID
     * @param fileId            文件id
     * @return
     */
    @SneakyThrows
    public OapiProcessinstanceFileUrlGetResponse.AppSpaceResponse getProcessFile(String processInstanceId, String fileId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/file/url/get");
        OapiProcessinstanceFileUrlGetRequest req = new OapiProcessinstanceFileUrlGetRequest();
        OapiProcessinstanceFileUrlGetRequest.GrantCspaceRequest request = new OapiProcessinstanceFileUrlGetRequest.GrantCspaceRequest();
        request.setProcessInstanceId(processInstanceId);
        request.setFileId(fileId);
        req.setRequest(request);
        OapiProcessinstanceFileUrlGetResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.getProcessFile(processInstanceId, fileId);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        return rsp.getResult();
    }

    /**
     * 获取用户待审批数量
     *
     * @param userid 要查询的用户userid
     * @return
     */
    @SneakyThrows
    public Long getTodoNum(String userid) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/gettodonum");
        OapiProcessGettodonumRequest req = new OapiProcessGettodonumRequest();
        req.setUserid(userid);
        OapiProcessGettodonumResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.getTodoNum(userid);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        return rsp.getCount();
    }

    /**
     * 添加审批评论
     *
     * @param processInstanceId 审批实例ID
     * @param commentUserid     评论人id
     * @param text              评论内容
     * @param photos            图片URL列表--oss图片地址
     * @return
     */
    @SneakyThrows
    public Boolean addComment(String processInstanceId, String commentUserid, String text, List<String> photos) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/instance/comment/add");
        OapiProcessInstanceCommentAddRequest req = new OapiProcessInstanceCommentAddRequest();
        OapiProcessInstanceCommentAddRequest.AddCommentRequest commentRequest = new OapiProcessInstanceCommentAddRequest.AddCommentRequest();
        commentRequest.setProcessInstanceId(processInstanceId);
        commentRequest.setCommentUserid(commentUserid);
        commentRequest.setText(text);
        OapiProcessInstanceCommentAddRequest.File file = new OapiProcessInstanceCommentAddRequest.File();
        file.setPhotos(photos);
        commentRequest.setFile(file);
        req.setRequest(commentRequest);
        OapiProcessInstanceCommentAddResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.addComment(processInstanceId, commentUserid, text, photos);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        return rsp.getResult();
    }

    /**
     * 执行审批操作带附件
     *
     * @param processInstanceId 审批实例ID
     * @param actionerUserid    操作人id--审批实例详情获取
     * @param taskId            任务节点id--审批实例详情获取
     * @param remark            操作评论
     * @param result            审批操作，同意-agree，拒绝-refuse
     * @param photos            图片URL列表--oss图片地址
     * @return
     */
    @SneakyThrows
    public void executeTask(String processInstanceId,
                            String actionerUserid,
                            Long taskId,
                            String remark,
                            String result,
                            List<String> photos) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/instance/execute");
        OapiProcessinstanceExecuteV2Request req = new OapiProcessinstanceExecuteV2Request();
        OapiProcessinstanceExecuteV2Request.ExecuteTaskRequest executeTaskRequest = new OapiProcessinstanceExecuteV2Request.ExecuteTaskRequest();
        executeTaskRequest.setProcessInstanceId(processInstanceId);
        executeTaskRequest.setActionerUserid(actionerUserid);
        executeTaskRequest.setTaskId(taskId);
        executeTaskRequest.setRemark(remark);
        executeTaskRequest.setResult(result);
        OapiProcessinstanceExecuteV2Request.File file = new OapiProcessinstanceExecuteV2Request.File();
        file.setPhotos(photos);
        executeTaskRequest.setFile(file);
        req.setRequest(executeTaskRequest);
        OapiProcessinstanceExecuteV2Response rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.executeTask(processInstanceId, actionerUserid, taskId, remark, result, photos);
            }
            throw new CustomException(rsp.getErrmsg());
        }
    }

    /**
     * OCR文字识别
     * 本接口并发限制如下：
     * 单用户QPS限制：5
     * 整体QPS限制：100
     *
     * @param imageUrl 识别图片地址，最大长度：1000。
     * @param type     识别图片类型：
     *                 idcard：身份证
     *                 invoice：营业执照增值税发票:
     *                 blicense：营业执照
     *                 bank_card：银行卡
     *                 car_no：车牌
     *                 car_invoice：机动车发票
     *                 driving_license：驾驶证
     *                 vehicle_license：行驶证
     *                 train_ticket：火车票
     *                 quota_invoice：定额发票
     *                 taxi_ticket：出租车发票
     *                 air_itinerary：机票行程单
     *                 approval_table：审批表单
     *                 roster：花名册
     * @return
     */
    @SneakyThrows
    public OapiOcrStructuredRecognizeResponse.OcrStructuredResult ocr(String imageUrl, String type) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/ocr/structured/recognize");
        OapiOcrStructuredRecognizeRequest req = new OapiOcrStructuredRecognizeRequest();
        req.setType(type);
        req.setImageUrl(imageUrl);
        OapiOcrStructuredRecognizeResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.ocr(imageUrl, type);
            }
            return null;
        }
        return rsp.getResult();
    }

    /**
     * 获取角色列表
     *
     * @param offset 部门ID
     * @param size   部门ID
     * @return
     */
    @SneakyThrows
    public List<OapiRoleListResponse.OpenRoleGroup> listRole(Long offset, Long size) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/role/list");
        OapiRoleListRequest req = new OapiRoleListRequest();
        req.setSize(ObjectUtil.defaultIfNull(size, 200L));
        req.setOffset(ObjectUtil.defaultIfNull(offset, 0L));
        OapiRoleListResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.listRole(offset, size);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        List<OapiRoleListResponse.OpenRoleGroup> groups = rsp.getResult().getList();
        Boolean hasMore = rsp.getResult().getHasMore();
        Long nextCursor = rsp.getResult().getNextCursor();
        if (hasMore) {
            groups.addAll(listRole(nextCursor, size));
        }
        return groups;
    }

    /**
     * 获取部门列表
     *
     * @param deptId 部门ID
     * @return
     */
    @SneakyThrows
    public List<OapiV2DepartmentListsubResponse.DeptBaseResponse> listDepartment(Long deptId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(deptId);
        req.setLanguage("zh_CN");
        OapiV2DepartmentListsubResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.listDepartment(deptId);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        return rsp.getResult();
    }

    /**
     * 获取部门用户详情
     *
     * @param deptId 部门ID
     * @return
     */
    @SneakyThrows
    public List<OapiV2UserListResponse.ListUserResponse> listUser(Long deptId, Long cursor) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(deptId);
        req.setCursor(ObjectUtil.defaultIfNull(cursor, 0L));
        req.setSize(100L);
        req.setOrderField("modify_desc");
        req.setContainAccessLimit(false);
        req.setLanguage("zh_CN");
        OapiV2UserListResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.listUser(deptId, cursor);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        OapiV2UserListResponse.PageResult result = rsp.getResult();
        List<OapiV2UserListResponse.ListUserResponse> list = result.getList();
        Boolean hasMore = rsp.getResult().getHasMore();
        Long nextCursor = rsp.getResult().getNextCursor();
        if (hasMore) {
            list.addAll(listUser(deptId, nextCursor));
        }
        return list;
    }

    /**
     * 获取部门详情
     *
     * @param deptId 部门ID
     * @return
     */
    @SneakyThrows
    public OapiV2DepartmentGetResponse.DeptGetResponse getDepartment(String deptId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(Long.valueOf(deptId));
        req.setLanguage("zh_CN");
        OapiV2DepartmentGetResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.getDepartment(deptId);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        return rsp.getResult();
    }

    /**
     * 根据userid获取用户详情
     *
     * @param userid 用户ID
     * @return
     */
    @SneakyThrows
    public OapiV2UserGetResponse.UserGetResponse getUser(String userid) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userid);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.getUser(userid);
            }
            return null;
        }
        return rsp.getResult();
    }


    /**
     * 解析@ApiField字段值
     *
     * @param obj
     * @return
     */
    private Map<String, Object> parseApiField(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            Field[] fields = ReflectUtil.getFields(obj.getClass());
            Map<String, Object> params = new HashMap<>(16);
            for (Field field : fields) {
                ApiField apiField = field.getAnnotation(ApiField.class);
                if (Objects.nonNull(apiField)) {
                    if (BeanUtil.isBean(field.getType())) {
                        Map<String, Object> subParams = parseApiField(ReflectUtil.getFieldValue(obj, field));
                        params.put(apiField.value(), subParams);
                    } else {
                        params.put(apiField.value(), ReflectUtil.getFieldValue(obj, field));
                    }
                }
            }
            return params;
        } catch (Exception e) {
            log.error("解析@ApiField字段值异常", e);
            return null;
        }
    }


    /**
     * 发送工作通知
     *
     * @param dingTalkMsg 内容
     * @param userIds     接收者的userid列表，最大用户列表长度100。[","]拼接
     * @return
     */
    @SneakyThrows
    public boolean sendWorkNotice(DingTalkMsg dingTalkMsg, String userIds) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setAgentId(1019811103L);
        request.setUseridList(userIds);
        request.setToAllUser(false);
        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();

        String msgtype = dingTalkMsg.getMsgtype();
        if ("text".equals(msgtype)) {
            msg.setMsgtype("text");
            msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
            msg.getText().setContent(dingTalkMsg.getText().getContent());
        } else if ("markdown".equals(msgtype)) {
            msg.setMsgtype("markdown");
            msg.setMarkdown(new OapiMessageCorpconversationAsyncsendV2Request.Markdown());
            msg.getMarkdown().setText(dingTalkMsg.getMarkdown().getText());
            msg.getMarkdown().setTitle(dingTalkMsg.getMarkdown().getTitle());
        }
        request.setMsg(msg);

        OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, getAccessToken());
        return rsp.isSuccess();
    }


    /**
     * 获取打卡结果
     *
     * @param workDateFrom 查询考勤打卡记录的起始工作日。
     * @param workDateTo   查询考勤打卡记录的结束工作日。
     * @param userIdList   员工在企业内的userId列表，最大值50。
     * @param offset       表示获取考勤数据的起始点。第一次传0，如果还有多余数据，下次获取传的offset值为之前的offset+limit，0、1、2...依次递增。
     * @return
     */
    @SneakyThrows
    public List<OapiAttendanceListResponse.Recordresult> listAttendance(Date workDateFrom, Date workDateTo, List<String> userIdList, Long offset) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/attendance/list");
        OapiAttendanceListRequest req = new OapiAttendanceListRequest();
        req.setWorkDateFrom(DateUtil.formatDateTime(workDateFrom));
        req.setWorkDateTo(DateUtil.formatDateTime(workDateTo));
        req.setUserIdList(userIdList);
        req.setOffset(offset);
        req.setLimit(50L);
        req.setIsI18n(false);
        OapiAttendanceListResponse rsp = client.execute(req, getAccessToken());
        if (!rsp.isSuccess()) {
            if (isTokenInvalid(rsp.getErrcode(), rsp.getSubCode())) {
                this.listAttendance(workDateFrom, workDateTo, userIdList, offset);
            }
            throw new CustomException(rsp.getErrmsg());
        }
        List<OapiAttendanceListResponse.Recordresult> result = new ArrayList<>(rsp.getRecordresult());
        if (rsp.getHasMore()) {
            List<OapiAttendanceListResponse.Recordresult> recordresults = this.listAttendance(workDateFrom, workDateTo, userIdList, offset + req.getLimit());
            result.addAll(recordresults);
        }
        return result;


    }
}
