package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.SubjectDept;

/**
 * <p>
 * 科目部门关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
public interface SubjectDeptService extends IService<SubjectDept> {


    /**
     * 批量删除科目部门中间表
     *
     * @param ids
     * @return
     */
    boolean deleteSubjectDeptBySubjectIds(Long[] ids);

    /**
     * 查询是否存在科目部门关系
     * @param ids
     * @return
     */
    boolean checkSubjectDeptBySubjectIds(Long[] ids);
}
