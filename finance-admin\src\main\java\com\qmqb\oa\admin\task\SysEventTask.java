package com.qmqb.oa.admin.task;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dingtalk.api.response.OapiCallBackGetCallBackFailedResultResponse;
import com.qmqb.oa.admin.service.SysEventHandler;
import com.qmqb.oa.admin.service.hsfk.DingTalkEventPolicyPatternService;
import com.qmqb.oa.admin.service.hsfk.H5AppDingClientService;
import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.common.enums.EkbEventType;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.common.utils.reflect.ReflectUtils;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.SysEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import shade.com.alibaba.fastjson2.JSONObject;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统事件定时任务
 *
 * <AUTHOR>
 * @date 2024/8/27
 */
@Slf4j
@Component("sysEventTask")
@RequiredArgsConstructor
public class SysEventTask {
    @Autowired
    private SysEventService sysEventService;
    @Autowired
    private SysEventHandler sysEventHandler;
    @Autowired
    private DingAppConfService dingAppConfService;

    @Autowired
    private H5AppDingClientService h5AppDingClientService;
    @Autowired
    private List<DingTalkEventPolicyPatternService> eventPolicyPatternService;

    /**
     * 获取钉钉推送失败的事件列表
     */
    public void pullDingCallBackFailedResult(){
        log.info("获取钉钉推送失败的事件列表定时任务开始......");
        List<DingAppConf> appConfList = dingAppConfService.list(
                Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getAppSwitch, 1));
        appConfList.forEach( c -> {
            try {
                String accessToken = h5AppDingClientService.getAccessToken(c.getDingAppKey(), c.getDingAppSecret());
                //调用钉钉推送失败的事件列表接口
                OapiCallBackGetCallBackFailedResultResponse callBackFailedRsp = h5AppDingClientService.getCallBackFailedResult(accessToken);
                List<OapiCallBackGetCallBackFailedResultResponse.Failed> failedList = callBackFailedRsp.getFailedList();
                for (OapiCallBackGetCallBackFailedResultResponse.Failed failed : failedList) {
                    Map<String, Object> stringObjectMap = H5AppDingClientService.parseApiField(failed);
                    // map = { user_add_org=null, org_dept_remove=null, org_dept_create=null, org_dept_modify=null, user_leave_org=null,  user_modify_org=null}
                    Set<String> eventOrgs = stringObjectMap.keySet();
                    // 遍历map的key
                    eventOrgs.stream().forEach(eventOrg -> {
                        //通过key找枚举值，属于钉钉用户和部门的事件才执行
                        DingTalkEventType eventType = DingTalkEventType.getEventTypeByType(eventOrg);
                        if (ObjectUtil.isNotEmpty(eventType)) {
                            // 通过已知的key获取value
                            Object eventOrgObj = stringObjectMap.get(eventOrg);
                            // value不为空
                            if (ObjectUtil.isNotEmpty(eventOrgObj)) {
                                JSONObject bizData = JSONObject.parseObject(eventOrgObj.toString());
                                DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).
                                        eq(DingAppConf::getDingCorpId, bizData.get("corpid").toString()));
                                // 保存事件表
                                sysEventService.saveSysEvent(dingAppConf.getCompanyId(), 1, eventOrg, JSON.toJSONString(bizData),
                                        null, null, null);
                            }
                        }
                    });
                }
            } catch (Exception e) {
                log.error("获取钉钉主体：{}，出现异常",c.getCompanyName(),e);
            }
            log.info("获取钉钉推送失败的事件列表定时任务结束......");
        });
    }

    public static void main(String[] args) {
        OapiCallBackGetCallBackFailedResultResponse.Failed failed = new OapiCallBackGetCallBackFailedResultResponse.Failed();
       /* failed.setUserAddOrg("userAdd");
        failed.setOrgDeptModify("deptModify");*/
        /*JSONObject object = JSON.parseObject(JSON.toJSONString(failed));
        Set<Map.Entry<String, Object>> entries = object.entrySet();
        System.out.println(JSON.toJSONString(entries));*/
        Map<String, Object> stringObjectMap = H5AppDingClientService.parseApiField(failed);
        System.out.println("结果===" + stringObjectMap);
        System.out.println("map=======" + stringObjectMap.get("user_add_org"));
        Set<String> strings = stringObjectMap.keySet();
        strings.stream().forEach(s->{
            Object o = stringObjectMap.get(s);
            System.out.println("o结果===" + o);
            System.out.println("s结果===" + s);
        });

    }

    /**
     * 处理失败的事件，不包含易快报 “更新角色下员工信息”事件
     */
    public void dealFailedEventExcludeRoleUserUpdate() {
        log.info("处理失败的事件定时任务开始......");
        //分页大小
        long pageSize = 100L;
        //查询处理状态=失败的数据(不包含“更新角色下员工信息”)，按照eventSource升序，优先处理钉钉事件
        LambdaQueryWrapper<SysEvent> wrapper = Wrappers.lambdaQuery(SysEvent.class)
                .eq(SysEvent::getStatus, 2)
                .le(SysEvent::getFailCount,5)
                .ge(SysEvent::getCreateTime, LocalDateTime.now().minusDays(7L))
                .notIn(SysEvent::getEventType,
                        EkbEventType.ROLE_USER_UPDATE.getEventType(),EkbEventType.EKB_EVENT_NOTICE.getEventType())
                .orderByAsc(SysEvent::getEventSource);
        List<SysEvent> records = sysEventService.page(new Page(1, pageSize), wrapper).getRecords();
        records.forEach(r -> {
            this.dealFailedSysEvent(r);
        });
        log.info("处理失败的事件定时任务结束......");
    }

    /**
     * 处理易快报 “更新角色下员工信息”事件
     */
    public void dealFailedEvent4EkbRoleUserUpdate() {
        log.info("处理失败的事件定时任务开始......");
        //分页大小
        long pageSize = 1L;
        //查询处理状态=失败的数据,更新角色下员工信息接口是全量同步，因此只需要执行最近一条数据即可
        LambdaQueryWrapper<SysEvent> wrapper = Wrappers.lambdaQuery(SysEvent.class)
                .eq(SysEvent::getStatus, 2)
                .ge(SysEvent::getCreateTime, LocalDateTime.now().minusDays(7L))
                .le(SysEvent::getFailCount,5)
                .eq(SysEvent::getEventType, EkbEventType.ROLE_USER_UPDATE.getEventType())
                .orderByDesc(SysEvent::getCreateTime);
        List<SysEvent> records = sysEventService.page(new Page(1, pageSize), wrapper).getRecords();
        if (records.size() > 0) {
            this.dealFailedSysEvent(records.get(0));
        }
        log.info("处理失败的事件定时任务结束......");
    }

    /**
     * 处理易快报 “发送钉钉消息”事件
     */
    public void dealFailedEvent5EkbToDingMsgUpdate() {
        log.info("处理失败的事件定时任务开始......");
        //分页大小
        long pageSize = 1L;
        //查询处理状态=失败的数据,更新角色下员工信息接口是全量同步，因此只需要执行最近一条数据即可
        LambdaQueryWrapper<SysEvent> wrapper = Wrappers.lambdaQuery(SysEvent.class)
                .eq(SysEvent::getStatus, 2)
                .ge(SysEvent::getCreateTime, LocalDateTime.now().minusDays(7L))
                .le(SysEvent::getFailCount,5)
                .eq(SysEvent::getEventType, EkbEventType.EKB_EVENT_NOTICE.getEventType())
                .orderByAsc(SysEvent::getCreateTime);
        List<SysEvent> records = sysEventService.page(new Page(1, pageSize), wrapper).getRecords();
        if (records.size() > 0) {
            this.dealFailedSysEvent(records.get(0));
        }
        log.info("处理失败的事件定时任务结束......");
    }

    private void dealFailedSysEvent(SysEvent sysEvent) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("-eventId:").append(sysEvent.getId()).append(" eventType:").append(sysEvent.getEventType());
            MdcUtil.setTrace();
            MdcUtil.setModuleName(sb.toString());
            log.info("开始处理事件");
            sysEventHandler.dealFailedEvent(sysEvent);
        } catch (Exception e) {
            log.error("处理失败，原因", e);
        } finally {
            MdcUtil.clear();
        }
    }

}
