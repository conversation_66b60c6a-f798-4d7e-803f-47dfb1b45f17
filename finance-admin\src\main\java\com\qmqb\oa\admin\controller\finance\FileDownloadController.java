package com.qmqb.oa.admin.controller.finance;

import com.qmqb.oa.system.service.SftpService;
import com.qmqb.oa.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * SFTP文件下载控制器
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Api("SFTP文件下载相关接口")
@RestController
@RequestMapping("/finance/file")
public class FileDownloadController extends BaseController {

    @Autowired
    private SftpService sftpService;

    @ApiOperation("从SFTP下载文件")
    @PreAuthorize("@ss.hasPermi('finance:file:download')")
    @GetMapping("/download")
    public void download(
            @ApiParam(value = "SFTP上的文件路径", required = true) @RequestParam String filePath,
            @ApiParam(value = "下载后的文件名称", required = true) @RequestParam String fileName,
            HttpServletRequest request,
            HttpServletResponse response) {
        sftpService.downloadFile(filePath, fileName, request, response);
    }

    @ApiOperation("从SFTP下载对账明细文件夹")
    @PreAuthorize("@ss.hasPermi('finance:file:download')")
    @GetMapping("/downloadRepayDetailFolder")
    public void downloadRepayDetailFolder(
            @ApiParam(value = "要下载的对账明细主键id", required = true) @RequestParam List<String> batchNos,
            HttpServletRequest request,
            HttpServletResponse response) {
        sftpService.downloadRepayDetailFolder(batchNos, request, response);
    }
} 