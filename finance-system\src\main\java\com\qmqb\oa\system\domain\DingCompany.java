package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钉钉主体公司表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_ding_company")
public class DingCompany implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 租户Id
     */
        @TableId(value = "id", type = IdType.ASSIGN_ID)
      private Integer id;

      /**
     * 公司主体名称
     */
      private String companyName;

      /**
     * 钉钉主体的corpId
     */
      private String corpId;


}
