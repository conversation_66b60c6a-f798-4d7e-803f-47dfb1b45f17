package com.qmqb.oa.admin.support;

import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-10
 */
@Slf4j
public class TenantContextHolder {

    public static ThreadLocal<Integer> TENANT_HOLDER = new InheritableThreadLocal<>();

    /**
     * 获得租户
     */
    public static Integer getTenantId() {
        Integer tenantId = TENANT_HOLDER.get();
        if (Objects.isNull(tenantId)) {
            log.info("获取租户ID为空,使用默认值0");
            return 0;
        }
        return tenantId;
    }

    /**
     * 设置租户
     */
    public static void setTenantId(Integer tenantId) {
        TENANT_HOLDER.set(tenantId);
    }

    /**
     * 清空租户
     */
    public static void clearTenantId() {
        TENANT_HOLDER.remove();
    }

}
