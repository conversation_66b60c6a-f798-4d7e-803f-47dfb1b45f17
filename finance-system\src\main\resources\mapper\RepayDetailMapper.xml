<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.RepayDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.RepayDetail">
        <id column="id" property="id" />
        <result column="program_id" property="programId" />
        <result column="batch_no" property="batchNo" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="file_path" property="filePath" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="db_create_dt" property="dbCreateDt" />
        <result column="db_update_dt" property="dbUpdateDt" />
        <result column="data_type" property="dataType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, program_id, batch_no, start_date, end_date, file_path, creator, updater, db_create_dt, db_update_dt, data_type
    </sql>

</mapper>
