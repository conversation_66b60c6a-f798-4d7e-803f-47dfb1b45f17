package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.common.core.domain.dto.RepayGenConditionDto;
import com.qmqb.oa.system.domain.RepayGenRecord;
import com.qmqb.oa.system.mapper.RepayGenRecordMapper;
import com.qmqb.oa.system.service.RepayGenRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 还款信息生成记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Service
public class RepayGenRecordServiceImpl extends ServiceImpl<RepayGenRecordMapper, RepayGenRecord> implements RepayGenRecordService {

    @Override
    public List<RepayGenRecord> listByCondition(RepayGenConditionDto condition) {
        return this.baseMapper.listByCondition(condition);
    }

    @Override
    public String getBatchNoById(Long id) {
        return this.baseMapper.getBatchNoById(id);
    }
}
