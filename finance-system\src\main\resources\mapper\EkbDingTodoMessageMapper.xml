<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.EkbDingTodoMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.EkbDingTodoMessage">
        <id column="id" property="id" />
        <result column="ekb_flow_id" property="ekbFlowId" />
        <result column="ekb_message_id" property="ekbMessageId" />
        <result column="ding_source_id" property="dingSourceId" />
        <result column="ding_task_id" property="dingTaskId" />
        <result column="company_id" property="companyId" />
        <result column="ding_union_id" property="dingUnionId" />
        <result column="ding_task_status" property="dingTaskStatus" />
        <result column="action_name" property="actionName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,ekb_message_id, ekb_flow_id,ding_source_id, ding_task_id, company_id, ding_union_id, ding_task_status,action_name, remark, create_time, update_time
    </sql>


    <update id="setMultiRemarkInfo">
        update t_ekb_ding_todo_message set remark =
        <foreach item="item" index="key" collection="params" open="JSON_SET(remark, " separator="," close=")">
            CONCAT('$.', #{key}), #{item}
        </foreach>
        , update_time=now() where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="todoListByFlowIdAndUserIdSet" resultType="com.qmqb.oa.system.domain.EkbDingTodoMessage">
        select edtm.*
        from t_ekb_message em
                 inner join t_ekb_ding_todo_message edtm on em.message_id = edtm.ekb_message_id
        where em.flow_id =#{flowId}
          and edtm.ekb_msg_action in ('backlog.approving','backlog.paying')
          and em.user_id in
        <foreach item="userId" collection="userIdSet" open="(" separator="," close=")">
            #{userId}
        </foreach>


    </select>

</mapper>
