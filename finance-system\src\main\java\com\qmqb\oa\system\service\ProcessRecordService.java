package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.ProcessRecord;

import java.util.List;

/**
 * <p>
 * 审批实例处理记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
public interface ProcessRecordService extends IService<ProcessRecord> {
    /**
     * 根据示例id查询
     *
     * @param processInstanceId
     * @return
     */
    ProcessRecord getByProcessInstanceId(String processInstanceId);

    /**
     * 查询审批处理记录列表
     *
     * @param processRecord 审批处理记录
     * @return 审批处理记录集合
     */
    List<ProcessRecord> selectProcessRecordList(ProcessRecord processRecord);

}
