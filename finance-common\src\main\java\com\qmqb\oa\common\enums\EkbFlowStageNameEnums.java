package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description 流程状态枚举 由于接口是从缓存中获取数据，缓存更新偶尔会有不到1秒的延迟，单据执行动作后建议等待几秒再调接口查询
 * @Date 2024\10\25 0025 16:59
 * @Version 1.0
 */
@AllArgsConstructor
@Getter
public enum EkbFlowStageNameEnums {
    DEV("开发", "开发", "1"),
    PROCESSED("完成", "完成", "2"),
    DELETE("已删除", "已删除", "3"),
    NO_SUBMIT("尚未提交", "尚未提交", "4"),
    REJECT("拒绝", "拒绝", "5"),
    RETRACT("撤回", "撤回", "6"),
    ;

    private final String code;
    private final String name;
    private final String dbCode;
}
