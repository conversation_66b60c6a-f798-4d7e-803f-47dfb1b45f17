package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qmqb.oa.common.annotation.Excel;
import com.qmqb.oa.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 离职员工对象 t_dimission_employee
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_dimission_employee")
public class DimissionEmployee extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 离职员工的userid
     */
    private String userId;

    /**
     * 员工名字
     */
    @Excel(name = "员工名字")
    private String name;

    /**
     * 国际电话区号
     */
    private String stateCode;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String mobile;

    /**
     * 离职时间
     * @JsonFormat  序列化（bean转json）时的格式
     * @DateTimeFormat 反序列（json转bean）时的格式
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "离职时间（格式示例：2023-01-01）", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leaveTime;

    /**
     * 退出企业方式，取值：oapi：调用接口删除；cancel：注销；leave：主动离职；unknown：未知原因；delete：管理员删除
     */
    private String leaveReason;

    /**
     * 最后工作日
     */
    private Date lastWorkDay;

    /**
     * 离职原因备注
     */
    private String reasonMemo;

    /**
     * 离职前工作状态：1：待入职2：试用期3：正式
     */
    private Integer preStatus;

    /**
     * 离职交接人的userid
     */
    private String handoverUserId;

    /**
     * 离职状态：1：待离职2：已离职3：未离职4：发起离职审批但还未通过5：失效（离职流程被其他流程强制终止后的状态）
     */
    private Integer status;

    /**
     * 离职前主部门名称
     */
    @Excel(name = "离职前主部门名称")
    private String mainDeptName;

    /**
     * 离职前主部门ID
     */
    private Long mainDeptId;

    /**
     * 主动原因
     */
    private String voluntaryReason;

    /**
     * 被动原因
     */
    private String passiveReason;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    private Integer isDeleted;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    @Excel(name = "主体", dictType = "finance_tenant_id")
    private Integer tenantId;

}
