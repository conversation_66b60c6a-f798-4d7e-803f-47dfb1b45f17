# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.4.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: char

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /finance-admin
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

system:
  redirectEkbUrl: ${system.domain}/finance-admin/ding/redirectEkbUrl
  #redirectEkbUrl: https://finance.qmqb.top/finance-admin/ding/redirectEkbUrl
# Spring配置
spring:
  application:
    name: finance-admin
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # thymeleaf模板配置
  thymeleaf:
    cache: false
    mode: HTML
    prefix: classpath:/html/
    encoding: UTF-8
    suffix: .html


# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.qmqb.oa.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  # logo
  global-config:
    banner: false

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:
  # 描述
  description: 管理后台

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# actuator
management:
  endpoint:
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: "shutdown"
      base-path: /monitor

# pub
pub:
  # tool
  tool:
    java-time-module-enabled: true
    long-to-string-enabled: true
    error-handler-enabled: true
  # notice
  notice:
    enabled: true
    ding-talk-webhook-map:
      SEC353cb4e030cdaa5e310753247c73a3fdb11ea0b46a37efce3f645c664e17a010: https://oapi.dingtalk.com/robot/send?access_token=9fff55ad3d1991cb611d03b4f748a483f03885ea93cd5a78a6b6aec6556dd781
## 请求日志
request:
  log:
    exclude-patterns: /system/**,/monitor/**,/captchaImage,/login,/getInfo,/getRouters

# 开票信息
invoice:
  # 全民钱包
  qmqb:
    company: 广州市全民钱包科技有限公司
    code: 91440101MA59R2NL5W
    addr-phone: 广州市天河区兴国路21号************房仅限办公***********
    account: 招商银行广州中山二路支行***************
  # 深圳合众
  szhz:
    company: 深圳合众祥瑞科技发展有限公司
    code: 91440300349884996Y
    addr-phone: 深圳市前海深港合作区前湾一路1号A栋201室入驻深圳市前海商务秘书有限公司************
    account: 平安银行深圳南山支行**************
  # 佛山百益来信息咨询有限公司
  fsbyl:
    company: 佛山百益来信息咨询有限公司
    code: 91440605MACTAAPKXC
    addr-phone: 佛山市南海区桂城街道融和路24号1座1502室（住所申报）
    account: 兴业银行佛山南海千灯湖支行392080100100317274

# 钉钉配置
dingtalk:
  # 全民钱包
  qmqb:
    app-key: dingzdbsspnvbrcddwzs
    app-secret: ljf0G7Z7_5LOQaJ-hI_45BwfxA3cYb4gW6apoT8Z41BmvKZuP3F2LUH6h6R8Zgjp
    process:
      # 303.加班餐费/车费报销模板code
      meal: PROC-7KYJ5N9W-G5C158XH12BH6BJJBOSY2-9WSVMUPJ-E
      # 304.团建费报销申请模板code
      tb: PROC-FFYJNW3V-BCM2PH1C1T50WAHB5M7M3-QNWCBORJ-11
      # 315.付款申请模板code
      pay-apply: PROC-9BC78DBB-077D-44CA-A65C-C278CAA81C91
      # 311.开票申请模板code
      billing-apply: PROC-127BBA82-BFA4-4FEC-9EF8-6504381B493D
      # 407.入职审批模板code
      entry-approval: PROC-3AC34975-B0E0-40DA-98B9-A8F2CBC7CE02
  # 深圳合众
  szhz:
    app-key: dingixtmdtgucu8bidp4
    app-secret: vRJ7PQZCCHwmwmy-RaQVt3TmzFTz9pp0Ng80yomLGoDuKlk5ZvC-aSMYKJXQ79Bn
    process:
      # 303.加班餐费/车费报销模板code
      meal: PROC-UICK1E8W-KKKUV9PXQUK9Z9ZY12JQ1-ZLM7QBGJ-6
      # 304.团建费报销申请模板code
      tb: PROC-424LSGUV-NHMU2KBPS4QY0M3FIXIP1-UZFQEEGJ-2
  # 佛山百益来信息咨询有限公司
  fsbyl:
    app-key: dinge0fg2zoytztier46
    app-secret: g_wOQeoRWPkbRG_awtGZs8-3AP5NYDRthbFETp11BWGoR3Jr9JDl77OZVSiR_Reo
    process:
      # 205-加班餐费-车费报销模板code
      meal: PROC-68F7E478-6B8D-4214-8D81-E4E6000029F0
      # 206-团建费报销申请模板code
      tb: PROC-F49CF20E-94CC-4947-8ACF-F889E558A89A
      # 302-入职审批模板code
      entry-approval: PROC-00802578-94B9-4B41-A6D1-A635D7286913
  # OA办公平台
  oaop:
    app-key: dingbfpw99uuh00jqfmz
    app-secret: sRDjr4kHdF4voqlndpje_-7_0IlowJAIpfJyQAqU3VZ1MRKTjMhx2hob0KnwU0g0
    process:
      # 205-加班餐费-车费报销模板code
      meal:
      # 206-团建费报销申请模板code
      tb:
      # 103.入职审批模板code
      entry-approval: PROC-720FD922-4FF7-4268-B00E-7B43E86E17D6
  # 法汇天下
  fhtx:
    app-key: dingukky46yfa8cj2yqf
    app-secret: 4yVS7ct9ZiO4zSziEAs6moeNjptbsLco3MGqrlrlo_kM_UR_eLV3FLcrPAar9lbe
    process:
      # 301.报销模板code
      meal: PROC-3FE9C84E-64F7-4C30-81C8-76264489B824
      # 206-团建费报销申请模板code
      tb:
      # 102.入职审批模板code
      entry-approval: PROC-7C088CF4-0C62-4E44-8367-CC39835B3C90
  # 仲建
  zj:
    app-key: ding2m0hy04vukfmuav6
    app-secret: nwiw0JVhZV2c_Mln4YPNBYWAo99-WVEzFtlg7sIsHB9GUK-skvV0weG6I4WB4xli
    process:
      # 301.报销模板code
      meal:
      # 206-团建费报销申请模板code
      tb:
      # 入职审批模板code
      entry-approval: PROC-C21A4092-28BE-4955-88C0-3D832A06B72E

#易快报配置
ekb:
  domain: https://app.ekuaibao.com #域名
  roledefId: ID01AwpivxW6MD
  accessTokenUrl: ${ekb.domain}/api/openapi/v1/auth/getAccessToken  #获取授权码
  provisionalAuthUrl: ${ekb.domain}/api/openapi/v1.1/provisional/getProvisionalAuth?accessToken= #访问临时授权
  batchAddDeptUrl: ${ekb.domain}/api/openapi/v1/departments/batch/create?accessToken= #批量新增部门
  departmentsUrl: ${ekb.domain}/api/openapi/v1/departments #获取部门列表(包含停用部门)
  batchAddUserUrl: ${ekb.domain}/api/openapi/v1.1/staffs/batch/create?accessToken= #批量新增员工
  batchUpdateUserUrl: ${ekb.domain}/api/openapi/v1.1/staffs/batch/update?accessToken= #批量修改员工
  disableOrEnableStaffUrl: ${ekb.domain}/api/openapi/v1.1/staffs/disableOrEnableStaff/ #停启用员工
  updateDepartmentUrl: ${ekb.domain}/api/openapi/v1/departments/update/$ #修改部门信息($后面拼接部门id)
  disableOrEnableDepartmentUrl: ${ekb.domain}/api/openapi/v1/departments/disableOrEnableDepartment/$ #停启用部门($后面拼接部门id)
  updateRoleUserInfoUrl: ${ekb.domain}/api/openapi/v1.1/roledefs/$${ekb.roledefId}/staffs?accessToken= #更新角色下员工信息(占位符为角色id)
  staffListUrl: ${ekb.domain}/api/openapi/v1.1/staffs #已激活员工列表
  approveStatesUrl: ${ekb.domain}/api/openapi/v1/approveStates #获取单据审批状态
  ekbFlowDetailUrl: ${ekb.domain}/api/openapi/v1.1/flowDetails #获取单据审批详情
  ekbMsgNoticeImgUrl: https://qm-contract.qmqb.top/1730448149358_277AB9B6-394C-4697-B680-37147E808546.png  #ekb 消息通知图片
  ekbGetFeeTypeByIdUrl: ${ekb.domain}/api/openapi/v2/specifications/feeType/byIdsAndCodes #获取单据审批详情