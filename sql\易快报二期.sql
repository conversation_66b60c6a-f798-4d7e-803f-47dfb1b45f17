
update t_ekb_ding_user_mapping
set ding_dept_id_list = '369666534,410921335',
    ekb_user_id = 'ID01zNdgxCRa06:ID01Cdle7s42BN',
    ekb_user_name = '肖颖桃',
    ekb_default_dept_id = 'ID01zNdgxCRa06:ID01zZAV6gePPV',
    ekb_dept_id_list = 'ID01zNdgxCRa06:ID01zZAV6gePPV,ID01zNdgxCRa06:ID01zZzVbYSjOn'
where ding_user_id = '256202693732871811';

update t_ekb_ding_user_mapping
set ekb_user_id = 'ID01zNdgxCRa06:ID01CdmvScV6uX',
    ekb_user_name = '黄绍超',
    ekb_default_dept_id = 'ID01zNdgxCRa06:ID01zZAV6geNrh',
    ekb_dept_id_list = 'ID01zNdgxCRa06:ID01zZAV6geNrh'
where ding_user_id = '295149506740101404';

delete from t_sys_event where id not in (5643,5649);

update t_sys_event set create_time = '2024-09-02 00:00:00' where event_type = 'user_batch_modify';

ALTER TABLE t_sys_event DROP COLUMN url;

ALTER TABLE t_sys_event
  ADD fail_reason VARCHAR(200) DEFAULT NULL COMMENT '失败原因' AFTER fail_count;

ALTER TABLE t_sys_event
  ADD req_params VARCHAR(100) DEFAULT NULL COMMENT '请求params' AFTER event_type;

ALTER TABLE t_ding_app_conf
  ADD app_switch TINYINT(4)  DEFAULT 1 COMMENT '钉钉H5微应用开关：0-关闭；1-开启' AFTER partner_app_secret;