package com.qmqb.oa.admin.domain.dto;

import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchUpdateRoleUserInfoReq;
import com.qmqb.oa.system.domain.DingUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 易快报同步领导角色
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EkbSyncLeaderRoleDTO {

    /**
     * 是否需要同步易快报角色
     */
    private Boolean needSyncEkbRole;

    /**
     * 易快报角色列表
     */
    private List<EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq> ekbRoleList;


}
