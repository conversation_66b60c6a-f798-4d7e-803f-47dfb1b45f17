package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 钉钉与易快报部门映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface EkbDingDeptMappingService extends IService<EkbDingDeptMapping> {

    /**
     * 没有主键id的情况批量更新
     * @param batchUpdateList
     */
    void updateBatch(List<EkbDingDeptMapping> batchUpdateList);
}
