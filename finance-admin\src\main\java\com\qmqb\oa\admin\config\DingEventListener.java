package com.qmqb.oa.admin.config;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import com.qmqb.oa.admin.service.hsfk.DingTalkEventPolicyPatternService;
import com.qmqb.oa.admin.service.hsfk.DingTalkUserService;
import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import shade.com.alibaba.fastjson2.JSONObject;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Slf4j
@Component
public class DingEventListener {
    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Autowired
    private SysEventService sysEventService;
    @Autowired
    private List<DingTalkEventPolicyPatternService> eventPolicyPatternService;

//    private static String clientId = "dingpvhrixy2gfiiei6m";
//    private static String clientSecret = "xBxrkI98uhSvChS8mzWGa2wWJtnnJxoMm9tJOjMvWFekVZ9Ud-K5q2BXw0i0klzS";
    @PostConstruct
    public void listenEvent() throws Exception{
        //监听开关
        List<DingAppConf> appConfList = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getAppSwitch,1));
        appConfList.forEach( a ->
                {
                    try {
                        OpenDingTalkStreamClientBuilder
                                .custom()
                                .credential(new AuthClientCredential(a.getDingAppKey(),a.getDingAppSecret()))
                                //注册事件监听
                                .registerAllEventListener(new GenericEventListener() {
                                    @Override
                                    public EventAckStatus onEvent(GenericOpenDingTalkEvent event) {
                                        String eventId = "";
                                        try {
                                            log.info("监听到事件" + JSON.toJSONString(event));
                                            eventId = event.getEventId();
                                            StringBuilder sb = new StringBuilder();
                                            sb.append("eventType:").append(event.getEventType()).append("-eventId:").append(eventId);
                                            MdcUtil.setTrace();
                                            MdcUtil.setModuleName(sb.toString());

                                            //处理事件
                                            process(event, a.getCompanyId());
                                            //消费成功
                                            return EventAckStatus.SUCCESS;
                                        } catch (Exception e) {
                                            log.error("{}事件处理失败",eventId,e);
                                            //消费失败
                                            return EventAckStatus.LATER;
                                        }finally {
                                            MdcUtil.clear();
                                        }
                                    }
                                })
                                .build().start();
                        log.info("完成初始化监听");
                    } catch (Exception e) {
                        log.error("监听事件失败",e);
                    }
                }
        );


    }

    private void process(GenericOpenDingTalkEvent event, Integer companyId) throws Exception {
        log.info("监听到的数据" + JSON.toJSONString(event));
        //事件唯一Id
        String eventId = event.getEventId();
        //事件类型
        String eventType = event.getEventType();
        //事件产生时间
        Long bornTime = event.getEventBornTime();
        //获取事件体
        JSONObject bizData = event.getData();

        try {
            DingTalkEventPolicyPatternService service = eventPolicyPatternService.stream().filter(l ->
                    l.isCeLueModel(eventType)).findFirst().orElse(null);
            service.toHandleEvent(bizData, companyId);
        } catch (Exception e) {
            log.error("【{}】异常!", DingTalkEventType.getEventTypeByType(eventType).getEventDesc(), e);
            String errorMsg = dingTalkUserService.getErrorMsg(e);
            // 保存事件表
            sysEventService.saveSysEvent(companyId, 1, eventType, JSON.toJSONString(bizData), null, null, errorMsg);
        }


    }

}
