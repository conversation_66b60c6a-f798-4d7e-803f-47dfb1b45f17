<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head lang="en">
    <!-- 这个必须引入的啊，钉钉的前端js SDK, 使用框架的请自行参照开发文档  https://open.dingtalk.com/document/orgapp/read-before-development?spm=ding_open_doc.document.0.0.737449817q02mh-->
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
    <!-- 这个jquery 想不想引入自己决定，没什么影响 -->
    <script th:src="@{/js/jquery.min.js}"></script>
    <meta charset="UTF-8">
    <title>主页</title>
</head>

<body>
<!--Thymeleaf 语法取值-->

<!--<p>corpId：<span th:text="${corpId}">未知</span></p>-->
<!--<p>免登code：<span th:text="${code}">未知</span></p>-->
<!--<p>年龄：[[${age}]]</p>-->

<!--<p>状态码：<span th:text="${code}"></span></p>-->
<!--<p>消息：[[${msg}]]</p>-->

<!--<hr>
<h1>H5微应用首页</h1>
<p>当前页面的url:</p>
<p id="url"></p>
<br>
<p>解析url,获取的corpID:</p>
<p id="corpId"></p>
<br>
<p>SDK初始化获取的client:</p>
<p id="client"></p>
<br>
<p>请求我们服务端,登录返回的结果:</p>
<p id="result"></p>-->


<script>

    $(function () {
        // dd.ready参数为回调函数，在环境准备就绪时触发，jsapi的调用需要保证在该回调函数触发后调用，否则无效。
        //钉钉sdk 初始化                    ding100ff8d4cf17603635c2f4657eb6378f
        var origin = window.location.origin;   // 返回基础 URL (https://www.runoob.com/)
        //http://ding-web.lnexin.cn/?corpid=dingdc0a16e71772ca06bc961a6cb783455b&client=1
        var currentUrl = document.location.toString();
        // $("#url").append(currentUrl);
        // 解析url中包含的corpId
        var param = currentUrl.split("corpId=")[1];
        var corpId = param.split("&client")[0];
        var client = param.split("&client=")[1];
        // $("#corpId").append(corpId);
        // $("#client").append(client);
        var image = document.createElement("img");
        image.src = origin + "/finance-admin/images/welcome_app.png";
        image.width = 551;
        image.height = 428;
        image.style.position = "fixed";
        image.style.top = "50%";
        image.style.left = "50%";
        image.style.margin = "-224px 0px 0px -276px";
        image.style.display = "block";
        if(client == 2){
            //pc端图片及尺寸
            image.src = origin + "/finance-admin/images/welcome_pc.png";
            // image.width = 721;
            // image.height = 560;
            image.width = 468;
            image.height = 364;
            image.style.margin = "-182px 0 0 -234px";

        }
        document.body.appendChild(image);
                dd.ready(function () {
                    //获取当前网页的url
                    //使用SDK 获取免登授权码
                    dd.runtime.permission.requestAuthCode({
                        corpId: corpId,
                        onSuccess: function (result) {
                            var code = result.code;
                            $("#code").append(code);
                            //请求我们服务端的登陆地址
                            $.ajax({
                                url: origin + "/finance-admin/ding/login",
                                type: "GET",
                                dataType: 'json',
                                data: {"authCode":code,"corpId":corpId,"client":client},
                                success: function (response) {

                                    // 我们服务器返回的信息
                                    // 下面代码主要是将返回结果显示出来，可以根据自己的数据结构随便写
                                    // alert("结果：" + response);

                                    for (item in response) {
                                        $("#result").append("<li>" + item + ":" + response[item] + "</li>")
                                    }
                                    window.location.href = response.url;

                                },
                                error: function (response){
                                    alert('login fail')
                                    alert(JSON.parse(response))
                                },
                            });

                        },
                        onFail : function(err) {
                            alert(JSON.stringify(err))

                        }
                    });
                });
    })
</script>

</body>
</html>