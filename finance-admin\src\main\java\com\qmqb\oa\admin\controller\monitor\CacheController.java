package com.qmqb.oa.admin.controller.monitor;

import com.hzed.structure.common.constant.StringPool;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.redis.RedisCache;
import com.qmqb.oa.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 缓存监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/cache")
public class CacheController {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private RedisCache redisCache;

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping()
    public AjaxResult getInfo() throws Exception {
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", dbSize);

        List<Map<String, String>> pieList = new ArrayList<>();
        commandStats.stringPropertyNames().forEach(key -> {
            Map<String, String> data = new HashMap<>(2);
            String property = commandStats.getProperty(key);
            data.put("name", StringUtils.removeStart(key, "cmdstat_"));
            data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
            pieList.add(data);
        });
        result.put("commandStats", pieList);
        return AjaxResult.success(result);
    }

    @GetMapping("/get/{key}")
    public String getCacheInRedis(@PathVariable("key") String key) {
        Boolean exists = redisCache.exists(key);
        if (Boolean.FALSE.equals(exists)) {
            return "key不存在";
        }
        String type = redisCache.type(key);
        DataType dataType = DataType.fromCode(type);
        if (dataType.equals(DataType.NONE) || dataType.equals(DataType.STRING)) {
            return redisCache.getCacheObject(key);
        } else if (dataType.equals(DataType.LIST)) {
            List<Object> objects = redisCache.getCacheList(key);
            return JacksonUtil.toJson(objects);
        } else if (dataType.equals(DataType.SET)) {
            Set<Object> objects = redisCache.getCacheSet(key);
            return JacksonUtil.toJson(objects);
        } else if (dataType.equals(DataType.ZSET)) {
            Set<Object> objects = redisCache.zRange(key, 0, -1);
            return JacksonUtil.toJson(objects);
        } else if (dataType.equals(DataType.HASH)) {
            Map<String, Object> objectMap = redisCache.getCacheMap(key);
            return JacksonUtil.toJson(objectMap);
        } else {
            return StringPool.EMPTY;
        }
    }

    @DeleteMapping("/del/{key}")
    public String delCacheInRedis(@PathVariable("key") String key) {
        Boolean exists = redisCache.exists(key);
        if (Boolean.FALSE.equals(exists)) {
            return "key不存在";
        }
        redisCache.deleteObject(key);
        return "删除成功";
    }

}
