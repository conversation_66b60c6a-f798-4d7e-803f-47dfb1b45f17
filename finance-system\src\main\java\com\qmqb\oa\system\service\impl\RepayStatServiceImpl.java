package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qmqb.oa.system.domain.RepayStat;
import com.qmqb.oa.system.mapper.RepayStatMapper;
import com.qmqb.oa.system.service.RepayStatService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Service
public class RepayStatServiceImpl extends ServiceImpl<RepayStatMapper, RepayStat> implements RepayStatService {

    @Override
    public List<RepayStat> listExportBy(String batchNo) {
        return this.baseMapper.selectList(new QueryWrapper<RepayStat>().eq("batch_no", batchNo));
    }
}
