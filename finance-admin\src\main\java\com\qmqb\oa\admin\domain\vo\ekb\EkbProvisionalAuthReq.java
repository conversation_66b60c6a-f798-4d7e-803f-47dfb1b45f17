package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

/**
 * 访问临时授权req
 * <AUTHOR>
 * @date 2024/7/18
 */
@Data
public class EkbProvisionalAuthReq {

    /**
     *
     * 易快报用户userId
     */
    private String uid;

    /**
     * 登录页面类型
     * frontPage : 首页
     * home : 我的单据
     * approve : 待我审批
     * payment : 待我支付
     * form : 单据详情页（待我审批 进入单据页面效果）
     * new : 新建单据
     * edit : 编辑/提交草稿、驳回的单据（我的单据 进入单据页面效果）
     */
    private String pageType;

    /**
     * 授权有效期
     * 单位：秒，最大不能超过 604800 秒（7天）
     */
    private String expireDate;

    /**
     * 默认false
     * 是否跳转APP端
     * true : 跳转APP端
     * false : 跳转WEB端
     */
    private Boolean isApplet;

    /**
     * 当「 pageType = form 或 backlogDetail 或 edit 」时必填，表示需要访问的单据详情页
     */
    private String flowId;
}