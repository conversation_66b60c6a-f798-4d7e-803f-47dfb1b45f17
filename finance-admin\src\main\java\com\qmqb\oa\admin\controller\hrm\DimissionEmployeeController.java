package com.qmqb.oa.admin.controller.hrm;

import com.qmqb.oa.common.annotation.Log;
import com.qmqb.oa.common.core.controller.BaseController;
import com.qmqb.oa.common.core.domain.AjaxResult;
import com.qmqb.oa.common.core.domain.model.LoginUser;
import com.qmqb.oa.common.core.page.TableDataInfo;
import com.qmqb.oa.common.enums.BusinessType;
import com.qmqb.oa.common.exception.CustomException;
import com.qmqb.oa.common.utils.ServletUtils;
import com.qmqb.oa.common.utils.poi.ExcelUtil;
import com.qmqb.oa.framework.web.service.TokenService;
import com.qmqb.oa.system.domain.DimissionEmployee;
import com.qmqb.oa.system.service.DimissionEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 离职员工Controller
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Slf4j
@RestController
@RequestMapping("/hrm/employee")
public class DimissionEmployeeController extends BaseController {
    @Autowired
    private DimissionEmployeeService dimissionEmployeeService;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询离职员工列表
     */
    @PreAuthorize("@ss.hasPermi('hrm:employee:list')")
    @GetMapping("/list")
    public TableDataInfo list(DimissionEmployee dimissionEmployee) {
        startPage();
        List<DimissionEmployee> list = dimissionEmployeeService.selectDimissionEmployeeList(dimissionEmployee);
        return getDataTable(list);
    }

    /**
     * 导出离职员工列表
     */
    @PreAuthorize("@ss.hasPermi('hrm:employee:export')")
    @Log(title = "离职员工", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DimissionEmployee dimissionEmployee) {
        List<DimissionEmployee> list = dimissionEmployeeService.selectDimissionEmployeeList(dimissionEmployee);
        ExcelUtil<DimissionEmployee> util = new ExcelUtil<DimissionEmployee>(DimissionEmployee.class);
        return util.exportExcel(list, "employee");
    }

    /**
     * 获取离职员工详细信息
     */
    @PreAuthorize("@ss.hasPermi('hrm:employee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dimissionEmployeeService.selectDimissionEmployeeById(id));
    }

    /**
     * 新增离职员工
     */
    @PreAuthorize("@ss.hasPermi('hrm:employee:add')")
    @Log(title = "离职员工", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DimissionEmployee dimissionEmployee) {
        return toAjax(dimissionEmployeeService.insertDimissionEmployee(dimissionEmployee));
    }

    /**
     * 修改离职员工
     */
    @PreAuthorize("@ss.hasPermi('hrm:employee:edit')")
    @Log(title = "离职员工", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DimissionEmployee dimissionEmployee) {
        return toAjax(dimissionEmployeeService.updateDimissionEmployee(dimissionEmployee));
    }

    /**
     * 删除离职员工
     */
    @PreAuthorize("@ss.hasPermi('hrm:employee:remove')")
    @Log(title = "离职员工", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dimissionEmployeeService.deleteDimissionEmployeeByIds(ids));
    }

    @Log(title = "离职员工", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('hrm:employee:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        List<DimissionEmployee> dimissionEmployeeList;
        try {
            ExcelUtil<DimissionEmployee> util = new ExcelUtil<>(DimissionEmployee.class);
            dimissionEmployeeList = util.importExcel(file.getInputStream());
        } catch (Exception e) {
            log.error("解析离职员工数据异常", e);
            throw new CustomException("解析离职员工数据异常，请检查数据格式或联系管理员处理！");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message = dimissionEmployeeService.importData(dimissionEmployeeList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate() {
        ExcelUtil<DimissionEmployee> util = new ExcelUtil<>(DimissionEmployee.class);
        return util.importTemplateExcel("离职员工数据");
    }
}
