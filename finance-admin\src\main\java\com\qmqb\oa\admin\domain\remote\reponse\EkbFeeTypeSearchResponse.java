package com.qmqb.oa.admin.domain.remote.reponse;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description ekb 费用类型查询实体
 * @Date 2024\11\6 0006 15:51
 * @Version 1.0
 */

@Data
public class EkbFeeTypeSearchResponse {
    /**
     * 费用类型明细
     */
    private List<Item> items;
    @Data
    public static class Item {
        /**费用类型ID*/
        private String id;
        /** 企业Id*/
        private String corporationId;
        /**费用类型名称*/
        private String name;
        /**费用类型编码*/
        private String code;
        /**费用类型*/
        private String flowType;
    }

}
