package com.qmqb.oa.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.qmqb.oa.common.constant.HttpStatus;
import com.qmqb.oa.common.core.domain.model.LoginUser;
import com.qmqb.oa.common.exception.CustomException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    private final static String CREATE_TIME = "createTime";
    private final static String CREATE_BY = "createBy";
    private final static String UPDATE_TIME = "updateTime";
    private final static String UPDATE_BY = "updateBy";
    private final static String REGION_CODE = "regionCode";

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new CustomException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    /**
     * 新增记录设值
     *
     * @param obj
     * @return
     */
    public static Object setCreateInfo(Object obj) {
        Field[] fields = ReflectUtil.getFields(obj.getClass());
        List<String> names = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        if (names.contains(CREATE_BY)) {
            ReflectUtil.setFieldValue(obj, CREATE_BY, getUsername());
        }
        if (names.contains(UPDATE_BY)) {
            ReflectUtil.setFieldValue(obj, UPDATE_BY, getUsername());
        }
        if (names.contains(CREATE_TIME)) {
            ReflectUtil.setFieldValue(obj, CREATE_TIME, DateUtil.now());
        }
        if (names.contains(UPDATE_TIME)) {
            ReflectUtil.setFieldValue(obj, UPDATE_TIME, DateUtil.now());
        }
        return obj;
    }

    /**
     * 修改记录设值
     *
     * @param obj
     * @return
     */
    public static Object setUpdateInfo(Object obj) {
        Field[] fields = ReflectUtil.getFields(obj.getClass());
        List<String> names = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        if (names.contains(UPDATE_BY)) {
            ReflectUtil.setFieldValue(obj, UPDATE_BY, getUsername());
        }
        if (names.contains(UPDATE_TIME)) {
            ReflectUtil.setFieldValue(obj, UPDATE_TIME, DateUtil.now());
        }
        return obj;
    }
}
