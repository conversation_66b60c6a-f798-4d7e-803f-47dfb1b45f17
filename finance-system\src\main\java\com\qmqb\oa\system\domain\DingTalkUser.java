package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 钉钉用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_dingtalk_user")
public class DingTalkUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户的userId
     */
    private String userId;

    /**
     * 名字
     */
    private String userName;

    /**
     * 头像。 说明 员工使用默认头像，不返回该字段，手动设置头像会返回。
     */
    private String avatar;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 国际电话区号
     */
    private String stateCode;

    /**
     * 分机号。 说明 第三方企业应用不返回该参数。
     */
    private String telephone;

    /**
     * 员工工号。
     */
    private String jobNumber;

    /**
     * 员工邮箱。 说明 企业内部应用，只有应用开通通讯录邮箱等个人信息权限，才会返回该字段。 第三方企业应用，不返回该参数；如需获取email，可以使用钉钉统一授权套件方式获取。
     */
    private String email;

    /**
     * 员工的企业邮箱。 如果员工的企业邮箱没有开通，返回信息中不包含该数据。 说明 第三方企业应用不返回该参数。
     */
    private String orgEmail;

    /**
     * 职位。
     */
    private String title;

    /**
     * 角色列表
     */
    private String roleList;

    /**
     * 所属部门id列表。
     */
    private String deptIdList;

    /**
     * 员工在对应的部门中的排序。
     */
    private String deptOrderList;

    /**
     * 员工所在部门信息及是否是领导： 员工所在部门的部门ID。 员工在对应的部门中是否是领导： true：是 false：不是
     */
    private String leaderInDept;

    /**
     * 员工的直属主管。 说明 员工在企业管理后台个人信息面板中，直属主管内有值，才会返回该字段。
     */
    private Integer managerUserid;

    /**
     * 入职时间，Unix时间戳，单位毫秒。 说明 信息面板中入职时间字段内有值才返回。
     */
    private LocalDateTime hiredDate;

    /**
     * 办公地点。 说明 员工信息面板中该字段必须有值，才正常返回。如果无值，则不返回该字段。 企业内部应用，只有应用开通通讯录邮箱等个人信息权限，才会返回该字段。
     */
    private String workPlace;

    /**
     * 是否是部门的主管： 1：是 0：不是
     */
    @TableField("is_leader")
    private Boolean leader;

    /**
     * 是否为企业的老板： 1：是 0：不是
     */
    @TableField("is_boss")
    private Boolean boss;

    /**
     * 是否为企业账号： 1：是 0：不是
     */
    @TableField("is_exclusive_account")
    private Boolean exclusiveAccount;

    /**
     * 是否为企业的管理员： 1：是 0：不是
     */
    @TableField("is_admin")
    private Boolean admin;

    /**
     * 是否激活了钉钉：1：已激活，0：未激活
     */
    @TableField("is_active")
    private Boolean active;

    /**
     * 是否号码隐藏： 1：隐藏 0：不隐藏 说明 隐藏手机号后，手机号在个人资料页隐藏，但仍可对其发DING、发起钉钉免费商务电话。
     */
    @TableField("is_hide_mobile")
    private Boolean hideMobile;

    /**
     * 是否为企业的高管： 1：是 0：不是
     */
    @TableField("is_senior")
    private Boolean senior;

    /**
     * 是否完成了实名认证： 1：已认证 0：未认证
     */
    @TableField("is_real_authed")
    private Boolean realAuthed;

    /**
     * 户在当前开发者企业账号范围内的唯一标识
     */
    private String unionId;

    /**
     * 当用户来自于关联组织时的关联信息。 说明 用户所在企业存在关联关系的企业，返回该字段。
     */
    private String unionEmpExt;

    /**
     * 扩展属性，最大长度2000个字符。 说明 员工信息面板中添加的拓展字段内有值才返回。 企业内部应用，只有应用开通通讯录邮箱等个人信息权限，才会返回该字段。
     */
    private String extension;

    /**
     * 备注。 说明 员工信息面板中该字段必须有值，才正常返回。如果无值，则不返回该字段。 企业内部应用，只有应用开通通讯录邮箱等个人信息权限，才会返回该字段。
     */
    private String remark;

    /**
     * 入职公司主体
     */
    private String company;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    private Integer tenantId;


}
