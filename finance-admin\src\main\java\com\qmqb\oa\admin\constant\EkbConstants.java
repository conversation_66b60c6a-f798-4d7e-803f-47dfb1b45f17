package com.qmqb.oa.admin.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024\10\9 0009 18:25
 * @Version 1.0
 */
public interface EkbConstants {
    /**
     * form : 单据详情页（待我审批 进入单据页面效果）
     */
    final String PAGE_TYPE_FORM = "form";
    /**
     * 首页
     */
    final String PAGE_TYPE_PAGE = "frontPage";

    /**
     * 单位：秒，最大不能超过 604800 秒（7天）
     */
    final String EXPIRE_DATE = "604800";

    /**
     * client：1-app;2-PC
     */
    final int DEFAULT_CLIENT = 1;

    @Getter
    enum LeaderRoleEnum {
        /**
         * 所有主管
         */
        CENTER_LEADER(1,"ID01Al8UM7wcWz","ID01FdFTdvSp43", "中心负责人"),
        N_2_LEADER(2,"ID01zOcngfkRu7", "ID01FdKcBUJ30b","三级主管"),
        ALL_LEADER(3,"ID01Al7gZLSDOT", "ID01FdFvvKCRRl","所有的主管"),
        ;

        /**
         * 顺序
         */
        private Integer order;

        /**
         * ekb角色id
         */
        private String ekbRoleId;

        /**
         * ekb测试角色id
         */
        private String ekbTestRoleId;

        /**
         * ekb角色名称
         */
        private String ekbRoleName;

        LeaderRoleEnum(Integer order, String ekbRoleId, String ekbTestRoleId, String ekbRoleName) {
            this.order = order;
            this.ekbRoleId = ekbRoleId;
            this.ekbTestRoleId = ekbTestRoleId;
            this.ekbRoleName = ekbRoleName;
        }


    }
}
