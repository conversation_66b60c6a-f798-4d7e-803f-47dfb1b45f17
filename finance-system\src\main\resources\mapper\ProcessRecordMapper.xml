<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.ProcessRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.ProcessRecord">
        <id column="id" property="id"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="title" property="title"/>
        <result column="business_id" property="businessId"/>
        <result column="originator_user_id" property="originatorUserId"/>
        <result column="originator_user_name" property="originatorUserName"/>
        <result column="originator_dept_id" property="originatorDeptId"/>
        <result column="originator_dept_name" property="originatorDeptName"/>
        <result column="is_pass" property="isPass"/>
        <result column="result" property="result"/>
        <result column="process_type" property="processType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        process_instance_id,
        title,
        business_id,
        originator_user_id,
        originator_user_name,
        originator_dept_id,
        originator_dept_name,
        is_pass,
        result,
        process_type,
        create_time,
        update_time,
        tenant_id,
        is_deleted
    </sql>

    <sql id="selectProcessRecordVo">
        select <include refid="Base_Column_List"/>
        from t_process_record
    </sql>

    <select id="selectProcessRecordList" parameterType="com.qmqb.oa.system.domain.ProcessRecord"
            resultMap="BaseResultMap">
        <include refid="selectProcessRecordVo"/>
        <where>
            is_deleted = 0
            <if test="processInstanceId != null  and processInstanceId != ''">
                and process_instance_id = #{processInstanceId}
            </if>
            <if test="businessId != null  and businessId != ''">
                and business_id = #{businessId}
            </if>
            <if test="originatorUserName != null  and originatorUserName != ''">
                and originator_user_name like concat('%', #{originatorUserName}, '%')
            </if>
            <if test="originatorDeptName != null  and originatorDeptName != ''">
                and originator_dept_name like concat('%', #{originatorDeptName}, '%')
            </if>
            <if test="isPass != null">
                and is_pass = #{isPass}
            </if>
            <if test="processType != null">
                and process_type = #{processType}
            </if>
            <if test="processTypes != null and processTypes.size() > 0">
                and process_type in
                <foreach collection="processTypes" item="processType" index="index"
                         separator="," open="(" close=")">
                    #{processType}
                </foreach>
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
