package com.qmqb.oa.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.hzed.structure.common.constant.StringPool;
import com.hzed.structure.common.enums.TraceTypeEnum;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.common.util.str.FaJsonUtil;
import com.hzed.structure.common.util.str.StringUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <p>
 * MDC操作工具
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
public class MdcUtil extends com.hzed.structure.common.util.MdcUtil {

    /**
     * 与日志参数绑定
     */
    private static final String EXTRA_MSG = "extra_msg";
    private static final String PROCESS = "process";
    private static final String BIZ_ID = "biz_id";

    public static void setWebTraceMsg(TraceTypeEnum type, HttpServletRequest request) {
        String traceId = WebUtil.getTraceHeader(request);
        if (StringUtil.isBlank(traceId)) {
            setTrace(type);
            setSpan(type);
        } else {
            putMDC(StringPool.TRACE_ID, traceId);
            String spanId = WebUtil.getSpanHeader(request);
            if (StringUtil.isBlank(spanId)) {
                putMDC(StringPool.TRACE_SPAN_ID, IdUtil.traceId16());
            } else {
                putMDC(StringPool.TRACE_SPAN_ID, spanId);
            }
        }
        putMDC(StringPool.TRACE_HOST_NAME, HOSTNAME_VALUE);
        putMDC(StringPool.TRACE_IP, WebUtil.getIP(request));
        putMDC(StringPool.TRACE_URI, WebUtil.getMethod(request) + StringPool.COLON + WebUtil.getServletPath(request));
    }

    public static void setWebTraceMsg(TraceTypeEnum type) {
        HttpServletRequest request = WebUtil.getRequest();
        setWebTraceMsg(type, request);
    }

    /**
     * 如果没有，则设置
     */
    public static void setTraceUri() {
        StringUtil.isBlankConsumer(getMDC(StringPool.TRACE_URI), (s) -> putMDC(StringPool.TRACE_URI, WebUtil.getServletPath()));
    }

    /**
     * 如果没有，则设置
     */
    public static void setTraceIp() {
        StringUtil.isBlankConsumer(getMDC(StringPool.TRACE_IP), (s) -> putMDC(StringPool.TRACE_IP, WebUtil.getIP()));
    }


    /**
     * 设置PROCESS
     */
    public static void putProcess(String process) {
        putExtraMsg(PROCESS, process);
    }

    /**
     * 设置BIZ_ID
     */
    public static void putBizId(String bizId) {
        putExtraMsg(BIZ_ID, bizId);
    }

    /**
     * 获取BIZ_ID
     */
    public static String getBizId() {
        return getExtraMsg().getString(BIZ_ID);
    }

    /**
     * 设置额外信息
     */
    public static void putExtraMsg(String key, String value) {
        JSONObject extraMsg = getExtraMsg();
        extraMsg.put(key, value);
        putMDC(EXTRA_MSG, extraMsg.toString());
    }

    /**
     * 获取额外信息
     *
     * @return
     */
    public static JSONObject getExtraMsg() {
        String extraMsg = getMDC(EXTRA_MSG);
        JSONObject eMsg = FaJsonUtil.parseObject(extraMsg);
        if (Objects.isNull(eMsg)) {
            eMsg = new JSONObject();
        }
        return eMsg;
    }
}

