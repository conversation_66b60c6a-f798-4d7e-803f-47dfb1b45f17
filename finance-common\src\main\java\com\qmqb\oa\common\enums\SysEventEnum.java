package com.qmqb.oa.common.enums;

import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.common.enums.EkbEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统事件枚举类
 * <AUTHOR>
 * @date 2024/8/3
 */
@Getter
@AllArgsConstructor
public enum SysEventEnum {

    /**
     * 系统事件枚举
     */
    DING_DEPT_CREATE("通讯录企业部门创建",1, DingTalkEventType.DEPT_CREATE.getEventType()),
    DING_DEPT_MODIFY("通讯录企业部门修改",1,DingTalkEventType.DEPT_MODIFY.getEventType()),
    DING_DEPT_DELETE("通讯录企业部门删除",1,DingTalkEventType.DEPT_DELETE.getEventType()),
    DING_USER_ADD_ORG("通讯录企业增加员工事件",1,DingTalkEventType.USER_ADD_ORG.getEventType()),
    DING_MODIFY_ORG("通讯录企业修改员工事件",1,DingTalkEventType.USER_MODIFY_ORG.getEventType()),
    DING_USER_LEAVE_ORG("通讯录用户离职",1,DingTalkEventType.USER_LEAVE_ORG.getEventType()),


    EKB_DEPT_BATCH_CREATE( "批量创建部门",2, EkbEventType.DEPT_BATCH_CREATE.getEventType()),
    EKB_DEPT_BATCH_MODIFY("批量修改部门",2, EkbEventType.DEPT_BATCH_MODIFY.getEventType()),
    EKB_DEPT_BATCH_DELETE("批量删除部门",2, EkbEventType.DEPT_BATCH_DELETE.getEventType()),
    EKB_USER_BATCH_ADD("批量新增员工",2, EkbEventType.USER_BATCH_ADD.getEventType()),
    EKB_USER_BATCH_MODIFY( "批量修改员工",2, EkbEventType.USER_BATCH_MODIFY.getEventType()),
    EKB_USER_BATCH_DELETE("批量停用员工",2, EkbEventType.USER_BATCH_DELETE.getEventType()),

    EKB_EVENT_NOTICE("ekb事件通知",2, EkbEventType.EKB_EVENT_NOTICE.getEventType()),
    ;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件来源 1-钉钉；2-易快报
     */
    private Integer eventSource;

    /**
     * 事件类型
     */
    private String eventType;

}
