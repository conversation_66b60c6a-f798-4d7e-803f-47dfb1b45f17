package com.qmqb.oa.admin.task;

import cn.hutool.core.date.DateTime;
import com.hzed.structure.common.util.date.DateTimeUtil;
import com.hzed.structure.common.util.date.DateUtil;
import com.qmqb.oa.admin.service.RepayStatisticService;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.constant.RepaymentGenConstant;
import com.qmqb.oa.common.core.domain.entity.SysDictData;
import com.qmqb.oa.common.enums.DictType;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.RepayGenRecord;
import com.qmqb.oa.system.service.RepayGenRecordService;
import com.qmqb.oa.system.service.SysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component("autoGenerateRepayTask")
public class AutoGenerateRepayTask {
    @Autowired
    private RepayStatisticService repayStatisticService;
    @Autowired
    private SysDictTypeService sysDictTypeService;
    @Autowired
    private RepayGenRecordService repayGenRecordService;

    /**
     * 自动生成T-1时间范围内的还款报表
     * 执行时间：每天8:00
     */
    public void autoGenerate(Integer tenantId) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName("还款报表自动生成");
            DateTime now = DateTime.now();
            //当天计算前一天的数据
            DateTime beforeOneDay = DateUtil.offsetDay(now, -1);
            DateTime startTime = DateUtil.beginOfDay(beforeOneDay);
            DateTime endTime = DateTime.of(DateUtil.format(DateUtil.endOfDay(beforeOneDay), DateTimeUtil.PATTERN_DATETIME), DateTimeUtil.PATTERN_DATETIME);
            List<SysDictData> dataTypeList = sysDictTypeService.selectDictDataByType(DictType.DATA_TYPE.getType());
            List<RepayGenRecord> recordList = new ArrayList<>();
            for (SysDictData data : dataTypeList) {
                RepayGenRecord record = repayStatisticService.generateInitRepayGenRecordDO(RepaymentGenConstant.AUTO_GEN, startTime, endTime, Integer.parseInt(data.getDictValue()));
                recordList.add(record);
            }
            boolean result = repayGenRecordService.saveBatch(recordList);
            if (!result) {
                log.error("触发每日还款统计失败");
            }
            log.info("还款报表自动生成消费者执行结束");
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }



}
