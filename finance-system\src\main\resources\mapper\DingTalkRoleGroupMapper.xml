<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.DingTalkRoleGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.oa.system.domain.DingTalkRoleGroup">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="group_name" property="groupName" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, group_id, group_name, remark, create_by, create_time, update_by, update_time, is_deleted, tenant_id
    </sql>

</mapper>
