package com.qmqb.oa.admin.service.hsfk;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.hzed.structure.common.util.AssertUtil;
import com.hzed.structure.common.util.ObjectUtil;
import com.hzed.structure.tool.api.ApiResponse;
import com.qmqb.oa.admin.constant.EkbConstants;
import com.qmqb.oa.admin.domain.request.internal.EkbProvisionalAuthRequest;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.admin.domain.vo.ekb.EkbProvisionalAuthRsp;
import com.qmqb.oa.admin.service.ekb.command.AbstractEkbEventCommand;
import com.qmqb.oa.common.enums.EkbActionEnum;
import com.qmqb.oa.common.utils.spring.SpringUtils;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.qmqb.oa.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Slf4j
@Component
public class HeSiFeiKongService {

    @Autowired
    private EkbLoginService ekbLoginService;

    @Autowired
    private EkbDingTodoMessageService ekbDingTodoMessageService;

    @Autowired
    private EkbMessageService ekbMessageService;

    @Autowired
    private DingUserService dingUserService;

    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private H5AppDingClientService h5AppDingClientService;
    @Autowired
    private EkbDingUserMappingService ekbDingUserMappingService;

    public String toLogin(String corpId,String authCode,Integer client ) throws Exception{


//        String ekbUserId = "ID01zNdgxCRa06:ID01zZzVbYSIAn";

        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getDingCorpId, corpId));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(),dingAppConf.getDingAppSecret());
        log.info("钉钉accessToken:{}",accessToken);
        OapiV2UserGetuserinfoResponse userInfo = h5AppDingClientService.getUserInfoByAuthCode(authCode, accessToken);
        log.info("钉钉userInfo:{}", JSON.toJSONString(userInfo.getBody()));
        EkbDingUserMapping ekbDingUserMapping = ekbDingUserMappingService.getOne(
                Wrappers.lambdaQuery(EkbDingUserMapping.class).eq(EkbDingUserMapping::getDingUserId, userInfo.getResult().getUserid()));
        //userId = 1905526038637188
        String ekbUserId = ekbDingUserMapping.getEkbUserId();
//        String ekbUserId = "1905526038637188";
        EkbProvisionalAuthRequest authRequest = new EkbProvisionalAuthRequest();
        authRequest.setClient(client);
        authRequest.setCorpId(corpId);
        authRequest.setEkbUserId(ekbUserId);
        authRequest.setPageType(EkbConstants.PAGE_TYPE_PAGE);

        EkbProvisionalAuthRsp provisionalAuthRsp = ekbLoginService.getProvisionalAuth(authRequest);
        log.info("易快报provisionalAuthRsp:{}",provisionalAuthRsp);

        return provisionalAuthRsp.getMessage();
    }


    /**
     * 获取表单ekb 临时访问Url
     * @param authRequest
     * @return
     * @throws Exception
     */
    public String getFormProvisionalauthurl(EkbProvisionalAuthRequest authRequest) throws Exception{

        authRequest.setPageType(EkbConstants.PAGE_TYPE_FORM);
        EkbProvisionalAuthRsp provisionalAuthRsp = ekbLoginService.getProvisionalAuth(authRequest);
        log.info("易快报provisionalAuthRsp:{}",provisionalAuthRsp);

        return provisionalAuthRsp.getMessage();
    }

    /**
     * ekb 事件处理
     * @param ekbEventRequest
     * @return
     */
    public ApiResponse receiveEkbEvent(EkbReceiveEkbEventRequest ekbEventRequest){
        AssertUtil.notNull(ekbEventRequest, "事件实体不能为空");
        AssertUtil.notNull(ekbEventRequest.getAction(), "事件action参数不能为空");
        EkbActionEnum ekbActionEnum = EkbActionEnum.getEkbActionEnum(ekbEventRequest.getAction());
        if (ObjectUtil.isNull(ekbActionEnum)) {
            return ApiResponse.fail("改事件暂时不支持");
        }

        //获取事件处理service
        AbstractEkbEventCommand eventAbstractCommand = SpringUtils.getBean(ekbActionEnum.getCommandBeanName());
        //设置请求参数
        eventAbstractCommand.setParamObject(ekbEventRequest);
        //执行相关事件
        ApiResponse apiResponse;
        try {
            apiResponse = eventAbstractCommand.execute();
        } catch (Exception e){
            log.error("易快报消息处理异常",e);
            apiResponse = ApiResponse.fail("易快报消息处理异常");
        }finally {
            //销毁本地线程变量
            eventAbstractCommand.destroyThreadLocal();
        }

        return apiResponse;
    }



}
