package com.qmqb.oa.admin.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.oa.admin.domain.request.RepayDeatilRequest;
import com.qmqb.oa.admin.domain.vo.RepayDetailVo;
import com.qmqb.oa.system.domain.RepayDetail;
import com.qmqb.oa.system.service.RepayDetailService;

import cn.hutool.core.util.StrUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/6/9
 */
@Service
public class RepayDetailBizService {

    @Autowired
    private RepayDetailService repayDetailService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public IPage<RepayDetailVo> listRepayDetail(RepayDeatilRequest repayDeatilRequest) {
        LambdaQueryWrapper<RepayDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(repayDeatilRequest.getBatchNo()),RepayDetail::getBatchNo, repayDeatilRequest.getBatchNo());
        queryWrapper.eq(repayDeatilRequest.getDataType()!=null,RepayDetail::getDataType, repayDeatilRequest.getDataType());
        if (StrUtil.isNotBlank(repayDeatilRequest.getStartDate())) {
            LocalDateTime startDateTime = LocalDate.parse(repayDeatilRequest.getStartDate(), DATE_FORMATTER).atStartOfDay();
            queryWrapper.ge(RepayDetail::getStartDate, startDateTime);
        }
        if (StrUtil.isNotBlank(repayDeatilRequest.getEndDate())) {
            LocalDateTime endDateTime = LocalDate.parse(repayDeatilRequest.getEndDate(), DATE_FORMATTER).atTime(23, 59, 59);
            queryWrapper.le(RepayDetail::getEndDate, endDateTime);
        }
        queryWrapper.orderByDesc(RepayDetail::getDbCreateDt);
        Page<RepayDetail> page = new Page<>(repayDeatilRequest.getPageNum(), repayDeatilRequest.getPageSize());
        IPage<RepayDetail> iPage = repayDetailService.page(page, queryWrapper);
        IPage<RepayDetailVo> result = iPage.convert(repayDetail -> {
            RepayDetailVo repayDetailVo = new RepayDetailVo();
            BeanUtil.copyProperties(repayDetail,repayDetailVo);
            return repayDetailVo;
        });
        return result;
    }
}
