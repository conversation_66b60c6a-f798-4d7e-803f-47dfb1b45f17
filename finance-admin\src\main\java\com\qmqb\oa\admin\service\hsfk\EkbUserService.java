package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.oa.admin.config.EkbConfig;
import com.qmqb.oa.admin.domain.vo.ekb.*;
import com.qmqb.oa.common.enums.EkbEventType;
import com.qmqb.oa.common.utils.http.HttpRestService;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.qmqb.oa.system.domain.vo.RoleUserInfoVo;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.EkbDingUserMappingService;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 易快报用户业务接口类
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Slf4j
@Component
public class EkbUserService {

    @Autowired
    private EkbConfig ekbConfig;
    @Autowired
    private HttpRestService httpRestService;
    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private EkbDingUserMappingService ekbDingUserMappingService;
    @Autowired
    private EkbLoginService ekbLoginService;
    @Autowired
    private SysEventService sysEventService;

    /**
     * 批量新增员工
     *
     * @param ekbBatchAddUserReq
     * @return 0-调用接口失败，1-调用接口成功
     */
    public void batchAddUser(EkbBatchAddUserListReq ekbBatchAddUserReq) {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getBatchAddUserUrl() + accessTokenRep.getAccessToken();
        EkbBatchAddOrUpdateUserListRsp ekbBatchAddDeptRsp = httpRestService.post(url, JSON.toJSONString(ekbBatchAddUserReq),
                EkbBatchAddOrUpdateUserListRsp.class, "【易快报-批量新增员工】");
        updateEkb(ekbBatchAddDeptRsp);
    }

    /**
     * 调用易快报批量新增员工后
     *
     * 批量更新易快报初始数据
     * @param ekbBatchAddDeptRsp
     */
    private void updateEkb(EkbBatchAddOrUpdateUserListRsp ekbBatchAddDeptRsp) {
        List<EkbAddOrUpdateUserRsp> items = ekbBatchAddDeptRsp.getItems();
        items.forEach(updateEkbUser -> {
            LambdaUpdateWrapper<EkbDingUserMapping> updateWrapper = Wrappers.lambdaUpdate(EkbDingUserMapping.class)
                    .set(EkbDingUserMapping::getEkbUserId, updateEkbUser.getId())
                    .set(EkbDingUserMapping::getEkbUserName, updateEkbUser.getName())
                    .set(EkbDingUserMapping::getEkbDefaultDeptId, updateEkbUser.getDefaultDepartment())
                    .set(EkbDingUserMapping::getEkbDeptIdList, handleEkbDeptIdList(updateEkbUser.getDepartments()))
                    .set(EkbDingUserMapping::getUpdateTime, LocalDateTime.now())
                    .eq(EkbDingUserMapping::getMobile, updateEkbUser.getCellphone());
            ekbDingUserMappingService.update(updateWrapper);
        });
    }

    /**
     * 更新角色下员工信息
     */
    public void updateRoleUserInfo() {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getUpdateRoleUserInfoUrl() + accessTokenRep.getAccessToken();
        // 查询出易快报所有用户数据
        List<RoleUserInfoVo> roleUserInfoVoList = ekbDingUserMappingService.selectUserIdAndCompanyDeptId();
        EkbBatchUpdateRoleUserInfoReq ekbBatchUpdateRoleUserInfoReq = new EkbBatchUpdateRoleUserInfoReq();
        List<EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq> ekbUpdateRoleUserInfoReqs = new ArrayList<>();
        // map的key为部门id，value为该部门下的所有用户id
        HashMap<String, List<String>> hashMap = new HashMap<>(8);

        for (RoleUserInfoVo roleUserInfoVo : roleUserInfoVoList) {
            // 遍历，把部门id和用户id存入map中
            hashMap.computeIfAbsent(roleUserInfoVo.getEkbRoleDeptId(), k -> new ArrayList<>()).add(roleUserInfoVo.getEkbUserId());
        }
        // 遍历map，封装请求参数
        hashMap.entrySet().stream().forEach((entry) -> {
            EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq ekbUpdateRoleUserInfoReq = new EkbBatchUpdateRoleUserInfoReq.EkbUpdateRoleUserInfoReq();
            ekbUpdateRoleUserInfoReq.setPathType("id");
            List<String> pathList = new ArrayList<>();
            pathList.add(entry.getKey());
            ekbUpdateRoleUserInfoReq.setPath(pathList);
            ekbUpdateRoleUserInfoReq.setStaffs(entry.getValue());
            ekbUpdateRoleUserInfoReqs.add(ekbUpdateRoleUserInfoReq);
        });
        ekbBatchUpdateRoleUserInfoReq.setContents(ekbUpdateRoleUserInfoReqs);

        // 请求易快报接口
        httpRestService.put(url, JSON.toJSONString(ekbBatchUpdateRoleUserInfoReq), Object.class, "【易快报-更新角色下员工信息】", false);

    }

    /**
     * 批量修改易快报员工
     *
     * @param ekbBatchUpdateUserReq
     * @return 0-调用接口失败，1-调用接口成功
     */
    public void batchUpdateUser(EkbBatchUpdateUserListReq ekbBatchUpdateUserReq) {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getBatchUpdateUserUrl() + accessTokenRep.getAccessToken();

        EkbBatchAddOrUpdateUserListRsp ekbBatchAddDeptRsp = httpRestService.put(url, JSON.toJSONString(ekbBatchUpdateUserReq),
                EkbBatchAddOrUpdateUserListRsp.class, "【易快报-批量修改员工】", true);
        batchUpdateEkbUser(ekbBatchUpdateUserReq);
    }

    /**
     * 调用批量修改后
     * 批量处理易快报映射数据
     *
     * @param ekbBatchAddOrUpdateUserReq
     */
    private void batchUpdateEkbUser(EkbBatchUpdateUserListReq ekbBatchAddOrUpdateUserReq) {
        List<EkbUpdateUserReq> staffList = ekbBatchAddOrUpdateUserReq.getStaffList();
        staffList.forEach(ekbAddOrUpdateUser -> {
            LambdaUpdateWrapper<EkbDingUserMapping> updateWrapper = Wrappers.lambdaUpdate(EkbDingUserMapping.class)
                    .set(EkbDingUserMapping::getEkbUserId, ekbAddOrUpdateUser.getId())
                    .set(EkbDingUserMapping::getEkbUserName, ekbAddOrUpdateUser.getName())
                    .set(EkbDingUserMapping::getEkbDefaultDeptId, ekbAddOrUpdateUser.getDefaultDepartment())
                    .set(EkbDingUserMapping::getEkbDeptIdList, handleEkbDeptIdList(ekbAddOrUpdateUser.getDepartments()))
                    .set(EkbDingUserMapping::getUpdateTime, LocalDateTime.now())
                    .eq(EkbDingUserMapping::getMobile, ekbAddOrUpdateUser.getCellphone());
            ekbDingUserMappingService.update(updateWrapper);
        });
    }

    private String handleEkbDeptIdList(List<String> deptIdList) {
        String ekbDeptIdString = "";
        StringBuilder sb = new StringBuilder();
        for (String deptId : deptIdList) {
            sb.append(deptId).append(",");
        }
        if (sb.length() > 0) {
            ekbDeptIdString = sb.substring(0, sb.length() - 1);
        }
        return ekbDeptIdString;
    }

    /**
     * 调用易快报停启用员工
     *
     * @param ekbUserIds
     * @return 0-调用接口失败，1-调用接口成功
     */
    public void disableOrEnableStaff(List<String> ekbUserIds) {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String param = new StringBuilder()
                .append(ekbUserIds)
                .append("?accessToken=").append(accessTokenRep.getAccessToken())
                .append("&active=false")
                .append("&doCheck=false").toString();
        String url = ekbConfig.getDisableOrEnableStaffUrl() + param;
        httpRestService.put(url, null, null, "【易快报-批量停启用员工】", true);
    }
}
