package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalktodo_1_0.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.qmqb.oa.admin.config.EkbConfig;
import com.qmqb.oa.admin.domain.dto.DingDeleteTodoMsgDTO;
import com.qmqb.oa.admin.domain.dto.DingSendNotifyMsgDTO;
import com.qmqb.oa.admin.domain.dto.DingSubmitTodoMsgDTO;
import com.qmqb.oa.admin.domain.dto.DingUpdateTodoMsgDTO;
import com.qmqb.oa.common.exception.ServiceException;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.service.DingAppConfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉消息处理类
 *
 * <AUTHOR>
 * @date 2024/10/10
 */
@Slf4j
@Component
public class DingMsgService {
    @Autowired
    private H5AppDingClientService h5AppDingClientService;
    @Autowired
    private DingAppConfService dingAppConfService;
    @Value("${system.redirectEkbUrl}")
    private String redirectEkbUrl;

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalktodo_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalktodo_1_0.Client(config);
    }

    /**
     * 发送钉钉通知消息
     *
     * @param notifyMsgDTO
     * @return
     * @throws Exception
     */
    public boolean sendNotifyMsg(DingSendNotifyMsgDTO notifyMsgDTO) throws Exception {
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, notifyMsgDTO.getCompanyId()));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setAgentId(dingAppConf.getDingAgentId());
        request.setUseridList(notifyMsgDTO.getUserId());
        //设置为false时，userIdList或者deptList必须指定参数
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();

        msg.setMsgtype("link");
        msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
        msg.getLink().setTitle(notifyMsgDTO.getTitle());
        msg.getLink().setText(notifyMsgDTO.getText());
        String param = new StringBuilder()
                .append("?flowId=").append(notifyMsgDTO.getEkbFlowId())
                .append("&ekbUserId=").append(notifyMsgDTO.getEkbUserId())
                .append("&dingCorpId=").append(dingAppConf.getDingCorpId())
                .toString();
        String url = redirectEkbUrl + param;
        msg.getLink().setMessageUrl(url);
        //@lALOACZwe2Rk=上传媒体文件后的唯一id, 如果要定制提示logo,需要调用上传媒体文件接口
        msg.getLink().setPicUrl(notifyMsgDTO.getIconImgId());

        request.setMsg(msg);

        log.info(JSON.toJSONString(request));
        try {
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, accessToken);
            log.info("易快报messageId:{}发送钉钉通知消息接口响应：{}", notifyMsgDTO.getEkbMessageId(), JSON.toJSONString(rsp));
            return rsp.getErrcode() == 0;
        } catch (Exception e) {
            log.error("易快报messageId:{}，发送钉钉通知消息响应异常", notifyMsgDTO.getEkbMessageId(), e);
            throw new ServiceException(e);
        }

    }

    /**
     * 发送待办消息
     *
     * @param todoMsgDTO
     * @return 返回钉钉taskId
     * @throws Exception
     */
    public String sendTodoMsg(DingSubmitTodoMsgDTO todoMsgDTO) throws Exception {
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        //获取token
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, todoMsgDTO.getCompanyId()));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());

        CreateTodoTaskHeaders createTodoTaskHeaders = new CreateTodoTaskHeaders();
        createTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;
        String param = new StringBuilder()
                .append("?flowId=").append(todoMsgDTO.getEkbFlowId())
                .append("&ekbUserId=").append(todoMsgDTO.getEkbUserId())
                .append("&dingCorpId=").append(dingAppConf.getDingCorpId())
                .toString();
        String url = redirectEkbUrl + param;
        CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs()
                .setDingNotify("1");

        //待办内容setFieldKey setFieldValue
        List<CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList> contentFieldList = todoMsgDTO.getDesc().entrySet().stream()
                .map(entry -> new CreateTodoTaskRequest.CreateTodoTaskRequestContentFieldList()
                        .setFieldValue(entry.getValue())
                        .setFieldKey(entry.getKey()))
                .collect(Collectors.toList());


        CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl detailUrl = new CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl()
                //todo 是否区分代办链接的客户端
                .setAppUrl(url)
                .setPcUrl(url);
        CreateTodoTaskRequest createTodoTaskRequest = new CreateTodoTaskRequest()
                //串联待办流程的唯一流水号
                .setSourceId(todoMsgDTO.getSourceId())
                //消息标题
                .setSubject(todoMsgDTO.getContent())
                .setExecutorIds(java.util.Arrays.asList(
                        todoMsgDTO.getRecipientUnionId()
                ))
                .setParticipantIds(java.util.Arrays.asList(
                        todoMsgDTO.getRecipientUnionId()
                ))
                .setDetailUrl(detailUrl)
                .setContentFieldList(contentFieldList)
                .setIsOnlyShowExecutor(false)
                //优先级 10：较低 20：普通 30：紧急 40：非常紧急
                .setPriority(20)
//                .setNotifyConfigs(notifyConfigs)
                ;

        try {
            //待办提醒人的unionId，如果提醒人和接收人是同一个，则钉钉不会弹出待办消息
            String unionIdParam = todoMsgDTO.getSubmitUnionId();
            CreateTodoTaskResponse response = client.createTodoTaskWithOptions(unionIdParam, createTodoTaskRequest, createTodoTaskHeaders, new RuntimeOptions());
            log.info("易快报messageId:{} 请求参数: {}, 创建钉钉待办接口响应：{}", todoMsgDTO.getMessageId(), JSON.toJSONString(createTodoTaskRequest), JSON.toJSONString(response));
            return response.getBody().getId();
        } catch (Exception e) {
            log.error("易快报messageId:{}，钉钉sourceId:{}，创建钉钉待办接口响应异常", todoMsgDTO.getMessageId(), todoMsgDTO.getSourceId(), e);
            throw new ServiceException(e);

        }
    }

    /**
     * 更新待办消息
     *
     * @param todoMsgDTO
     * @return true=更新成功；false=更新失败
     * @throws Exception
     */
    public boolean updateTodoMsg(DingUpdateTodoMsgDTO todoMsgDTO) throws Exception {

        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        //获取token
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, todoMsgDTO.getCompanyId()));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());

        UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
        updateTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;

        UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest()
                //.setSubject("更新钉钉待办")
                .setDescription("应用可以调用该接口更新钉钉待办任务信息及状态，isv在自身应用待办数据被更新后，需调用该接口将钉钉侧待办数据同步更新。")
//                .setExecutorIds(todoMsgDTO.getRecipientUnionIds())
                //完成状态 true：已完成 false：未完成
                .setDone(todoMsgDTO.getIsDone());
        try {
            log.info("易快报messageId:{} 请求参数:{},更新待办接口请求：{} ", todoMsgDTO.getMessageId(), JSON.toJSONString(todoMsgDTO), JSON.toJSONString(updateTodoTaskRequest));
            UpdateTodoTaskResponse response = client.updateTodoTaskWithOptions(todoMsgDTO.getUnionId(), todoMsgDTO.getTaskId(), updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
            log.info("易快报messageId:{}更新待办接口响应：{}", todoMsgDTO.getMessageId(), JSON.toJSONString(response));
            return response.getBody().getResult();
        } catch (Exception e) {
            log.error("易快报messageId:{}，钉钉待办id:{}，更新钉钉待办接口响应异常", todoMsgDTO.getMessageId(), todoMsgDTO.getTaskId(), e);
            throw new ServiceException(e);
        }
    }

    /**
     * 更新钉钉待办执行者状态
     *
     * @param todoMsgDTO
     * @return true=更新成功；false=更新失败
     * @throws Exception
     */
    public boolean updateTodoTaskExecutorStatus(DingUpdateTodoMsgDTO todoMsgDTO) throws Exception {

        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        //获取token
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, todoMsgDTO.getCompanyId()));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());

        UpdateTodoTaskExecutorStatusHeaders updateTodoTaskExecutorStatusHeaders = new UpdateTodoTaskExecutorStatusHeaders();
        updateTodoTaskExecutorStatusHeaders.xAcsDingtalkAccessToken = accessToken;

        List<UpdateTodoTaskExecutorStatusRequest.UpdateTodoTaskExecutorStatusRequestExecutorStatusList> executorStatusList = new ArrayList<>();
        for (String recipientUnionId : todoMsgDTO.getRecipientUnionIds()) {
            UpdateTodoTaskExecutorStatusRequest.UpdateTodoTaskExecutorStatusRequestExecutorStatusList executorStatus = new UpdateTodoTaskExecutorStatusRequest.UpdateTodoTaskExecutorStatusRequestExecutorStatusList()
                    .setId(recipientUnionId)
                    .setIsDone(todoMsgDTO.getIsDone());
            executorStatusList.add(executorStatus);
        }

        //更新执行者状态请求参数
        UpdateTodoTaskExecutorStatusRequest updateTodoTaskExecutorStatusRequest = new UpdateTodoTaskExecutorStatusRequest()
                .setExecutorStatusList(executorStatusList);


        try {
            log.info("易快报messageId:{} 内部请求参数: {}, \n 更新钉钉待办执行者状态接口请求：{}", todoMsgDTO.getMessageId(), JSON.toJSONString(todoMsgDTO), JSON.toJSONString(updateTodoTaskExecutorStatusRequest));
            UpdateTodoTaskExecutorStatusResponse response = client.updateTodoTaskExecutorStatusWithOptions(todoMsgDTO.getUnionId(), todoMsgDTO.getTaskId(), updateTodoTaskExecutorStatusRequest, updateTodoTaskExecutorStatusHeaders, new RuntimeOptions());
            log.info("易快报messageId:{}更新钉钉待办执行者状态接口响应：{}", todoMsgDTO.getMessageId(), JSON.toJSONString(response));
            return response.getBody().getResult();
        } catch (Exception e) {
            log.error("易快报messageId:{}，钉钉待办id:{}，更新钉钉待办执行者状态接口响应异常", todoMsgDTO.getMessageId(), todoMsgDTO.getTaskId(), e);
            throw new ServiceException(e);
        }
    }

    /**
     * 删除钉钉待办消息
     *
     * @param todoMsgDTO
     * @return true=删除成功；false=删除失败
     * @throws Exception
     */
    public boolean deleteTodoMsg(DingDeleteTodoMsgDTO todoMsgDTO) throws Exception {
        com.aliyun.dingtalktodo_1_0.Client client = createClient();
        //获取token
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, todoMsgDTO.getCompanyId()));
        DeleteTodoTaskHeaders deleteTodoTaskHeaders = new DeleteTodoTaskHeaders();
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        deleteTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;

        DeleteTodoTaskRequest deleteTodoTaskRequest = new DeleteTodoTaskRequest();
        try {
            DeleteTodoTaskResponse response = client.deleteTodoTaskWithOptions(todoMsgDTO.getSubmitUnionId(), todoMsgDTO.getTaskId(), deleteTodoTaskRequest, deleteTodoTaskHeaders, new RuntimeOptions());
            log.info("易快报messageId:{}删除钉钉待办接口响应：{}", todoMsgDTO.getMessageId(), JSON.toJSONString(response));
            return response.getBody().getResult();
        } catch (Exception e) {
            log.error("易快报messageId:{}，钉钉待办id:{}，删除钉钉待办接口响应异常", todoMsgDTO.getMessageId(), todoMsgDTO.getTaskId(), e);
            throw new ServiceException(e);
        }
    }

}


