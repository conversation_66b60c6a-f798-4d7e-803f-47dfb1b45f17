package com.qmqb.oa.system.service.impl;

import java.util.List;

import com.qmqb.oa.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.qmqb.oa.system.mapper.BusinessNoticeMapper;
import com.qmqb.oa.system.domain.BusinessNotice;
import com.qmqb.oa.system.service.BusinessNoticeService;

/**
 * 业务提醒通知Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Service
public class BusinessNoticeServiceImpl extends ServiceImpl<BusinessNoticeMapper, BusinessNotice> implements BusinessNoticeService {
    @Autowired
    private BusinessNoticeMapper businessNoticeMapper;

    /**
     * 查询业务提醒通知
     *
     * @param id 业务提醒通知ID
     * @return 业务提醒通知
     */
    @Override
    public BusinessNotice selectBusinessNoticeById(Long id) {
        return businessNoticeMapper.selectBusinessNoticeById(id);
    }

    /**
     * 查询业务提醒通知列表
     *
     * @param businessNotice 业务提醒通知
     * @return 业务提醒通知
     */
    @Override
    public List<BusinessNotice> selectBusinessNoticeList(BusinessNotice businessNotice) {
        return businessNoticeMapper.selectBusinessNoticeList(businessNotice);
    }

    /**
     * 新增业务提醒通知
     *
     * @param businessNotice 业务提醒通知
     * @return 结果
     */
    @Override
    public int insertBusinessNotice(BusinessNotice businessNotice) {
        businessNotice.setCreateTime(DateUtils.getNowDate());
        return businessNoticeMapper.insertBusinessNotice(businessNotice);
    }

    /**
     * 修改业务提醒通知
     *
     * @param businessNotice 业务提醒通知
     * @return 结果
     */
    @Override
    public int updateBusinessNotice(BusinessNotice businessNotice) {
        businessNotice.setUpdateTime(DateUtils.getNowDate());
        return businessNoticeMapper.updateBusinessNotice(businessNotice);
    }

    /**
     * 批量删除业务提醒通知
     *
     * @param ids 需要删除的业务提醒通知ID
     * @return 结果
     */
    @Override
    public int deleteBusinessNoticeByIds(Long[] ids) {
        return businessNoticeMapper.deleteBusinessNoticeByIds(ids);
    }

    /**
     * 删除业务提醒通知信息
     *
     * @param id 业务提醒通知ID
     * @return 结果
     */
    @Override
    public int deleteBusinessNoticeById(Long id) {
        return businessNoticeMapper.deleteBusinessNoticeById(id);
    }

}
