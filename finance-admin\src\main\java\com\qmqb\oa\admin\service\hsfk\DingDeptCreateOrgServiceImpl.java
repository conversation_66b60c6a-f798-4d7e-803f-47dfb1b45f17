package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import shade.com.alibaba.fastjson2.JSONObject;

import java.util.Objects;

/**
 * 通讯录企业创建部门事件
 *
 * <AUTHOR>
 * @date 2024/8/31
 */
@Slf4j
@Service
public class DingDeptCreateOrgServiceImpl implements DingTalkEventPolicyPatternService{

    @Autowired
    private DingTalkDeptService dingTalkDeptService;
    @Autowired
    private SysEventService sysEventService;

    @Override
    public Boolean isCeLueModel(String type) {
        return Objects.equals(DingTalkEventType.DEPT_CREATE.getEventType(), type);
    }

    @Override
    public void toHandleEvent(JSONObject bizData, Integer companyId) throws Exception {
        dingTalkDeptService.createDept4DingAndEkb(bizData, companyId);
    }

    @Override
    public void toExecuteEvent(SysEvent sysEvent) throws Exception {
        JSONObject bizData = JSONObject.parseObject(sysEvent.getReqData());
        dingTalkDeptService.createDept4DingAndEkb(bizData, sysEvent.getCompanyId());
        sysEventService.update4SuccessById(sysEvent);
    }
}