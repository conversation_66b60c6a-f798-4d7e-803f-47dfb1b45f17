package com.qmqb.oa.system.service.impl;

import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.mapper.DingAppConfMapper;
import com.qmqb.oa.system.service.DingAppConfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 钉钉app应用配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class DingAppConfServiceImpl extends ServiceImpl<DingAppConfMapper, DingAppConf> implements DingAppConfService {

}
