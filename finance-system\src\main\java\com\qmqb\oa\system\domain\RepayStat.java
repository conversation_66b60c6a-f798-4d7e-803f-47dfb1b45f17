package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.oa.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_repay_stat")
public class RepayStat implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 自增id
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 制单日期
     */
      @Excel(name = "制单日期", width = 30, dateFormat = "yyyy-MM-dd")
      private Date realRepaymentDate;

      /**
     * 批次号
     */
      @Excel(name = "批次号")
      private String batchNo;

      /**
     * 凭证类别
     */
      @Excel(name = "凭证类别")
      private String voucherType;

      /**
     * 凭证编号
     */
      @Excel(name = "凭证编号")
      private String voucherCode;

      /**
     * 来源类型
     */
      @Excel(name = "来源类型")
      private String sourceType;

      /**
     * 差异凭证
     */
      @Excel(name = "差异凭证")
      private String diffVoucher;

      /**
     * 附单据数
     */
      @Excel(name = "附单据数")
      private String orderNum;

      /**
     * 摘要
     */
      @Excel(name = "摘要")
      private String remark;

      /**
     * 科目编码
     */
      @Excel(name = "科目编码")
      private String subjectCode;

      /**
     * 科目
     */
      @Excel(name = "科目")
      private String subjectName;

      /**
     * 币种
     */
      @Excel(name = "币种")
      private String currency;

      /**
     * 汇率
     */
      @Excel(name = "汇率")
      private BigDecimal exchangeRate;

      /**
     * 数量
     */
      @Excel(name = "数量")
      private Integer number;

      /**
     * 单价
     */
      @Excel(name = "单价")
      private BigDecimal price;

      /**
     * 借贷方向
     */
      @Excel(name = "借贷方向")
      private String direction;

      /**
     * 原币
     */
      @Excel(name = "原币")
      private BigDecimal localAmount;

      /**
     * 本币
     */
      @Excel(name = "本币")
      private BigDecimal foreignAmount;

      /**
     * 票据号
     */
      @Excel(name = "票据号")
      private String debtNo;

      /**
     * 票据日期
     */
      @Excel(name = "票据日期")
      private String debtDate;

      /**
     * 业务单号
     */
      @Excel(name = "业务单号")
      private String orderNo;

      /**
     * 业务日期
     */
      @Excel(name = "业务日期")
      private String orderDate;

      /**
     * 到期日
     */
      @Excel(name = "到期日")
      private String repayDate;

      /**
     * 业务员编码
     */
      @Excel(name = "业务员编码")
      private String clerkCode;

      /**
     * 业务员
     */
      @Excel(name = "业务员")
      private String clerkName;

      /**
     * 银行帐号
     */
      @Excel(name = "银行帐号")
      private String bankNo;

      /**
     * 结算方式
     */
      @Excel(name = "结算方式")
      private String settledType;

      /**
     * 往来单位编码
     */
      @Excel(name = "往来单位编码")
      private String companyCode;

      /**
     * 往来单位
     */
      @Excel(name = "往来单位")
      private String companyName;

      /**
     * 部门编码
     */
      @Excel(name = "部门编码")
      private String deptCode;

      /**
     * 部门
     */
      @Excel(name = "部门")
      private String deptName;

      /**
     * 存货编码
     */
      @Excel(name = "存货编码")
      private String stockCode;

      /**
     * 存货
     */
      @Excel(name = "存货")
      private String stockName;

      /**
     * 人员编码
     */
      @Excel(name = "人员编码")
      private String userCode;

      /**
     * 人员
     */
      @Excel(name = "人员")
      private String userName;

      /**
     * 项目编码
     */
      @Excel(name = "项目编码")
      private String projectCode;

      /**
     * 项目
     */
      @Excel(name = "项目")
      private String projectName;

      /**
       * 记录创建时间
       */
      @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd")
      @TableField("dbCreateDt")
      private Date dbcreatedt;
      /**
     * 扩展辅助1编码
     */
      @Excel(name = "扩展辅助1编码")
      private String exAss1Code;

      /**
     * 扩展辅助1
     */
      @Excel(name = "扩展辅助1")
      private String exAss1;

      /**
     * 扩展辅助2编码
     */
      @Excel(name = "扩展辅助2编码")
      private String exAss2Code;

      /**
     * 扩展辅助2
     */
      @Excel(name = "扩展辅助2")
      private String exAss2;

      /**
     * 扩展辅助3编码
     */
      @Excel(name = "扩展辅助3编码")
      private String exAss3Code;

      /**
     * 扩展辅助3
     */
      @Excel(name = "扩展辅助3")
      private String exAss3;

      /**
     * 扩展辅助4编码
     */
      @Excel(name = "扩展辅助4编码")
      private String exAss4Code;

      /**
     * 扩展辅助4
     */
      @Excel(name = "扩展辅助4")
      private String exAss4;

      /**
     * 扩展辅助5编码
     */
      @Excel(name = "扩展辅助5编码")
      private String exAss5Code;

      /**
     * 扩展辅助5
     */
      @Excel(name = "扩展辅助5")
      private String exAss5;

      /**
     * 扩展辅助6编码
     */
      @Excel(name = "扩展辅助6编码")
      private String exAss6Code;

      /**
     * 扩展辅助6
     */
      @Excel(name = "扩展辅助6")
      private String exAss6;

      /**
     * 扩展辅助7编码
     */
      @Excel(name = "扩展辅助7编码")
      private String exAss7Code;

      /**
     * 扩展辅助7
     */
      @Excel(name = "扩展辅助7")
      private String exAss7;

      /**
     * 扩展辅助8编码
     */
      @Excel(name = "扩展辅助8编码")
      private String exAss8Code;

      /**
     * 扩展辅助8
     */
      @Excel(name = "扩展辅助8")
      private String exAss8;

      /**
     * 扩展辅助9编码
     */
      @Excel(name = "扩展辅助9编码")
      private String exAss9Code;

      /**
     * 扩展辅助9
     */
      @Excel(name = "扩展辅助9")
      private String exAss9;

      /**
     * 扩展辅助10编码
     */
      @Excel(name = "扩展辅助10编码")
      private String exAss10Code;

      /**
     * 扩展辅助10
     */
      @Excel(name = "扩展辅助10")
      private String exAss10;



}
