package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.DingDept;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 钉钉部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface DingDeptService extends IService<DingDept> {

    /**
     * 获取最大子部门层级
     * @param deptChainName
     * @param companyId
     * @return
     */
    Integer getMaxChildDeptLevel(String deptChainName, Integer companyId);

    /**
     * 获取子部门列表
     * @param deptCode
     * @param companyId
     * @param maxLevel
     * @return
     */
    List<DingDept> childList(String deptCode,Integer companyId,Integer maxLevel);
}
