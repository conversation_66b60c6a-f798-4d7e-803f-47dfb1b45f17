<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.DimissionEmployeeMapper">
    <resultMap type="com.qmqb.oa.system.domain.DimissionEmployee" id="DimissionEmployeeResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="stateCode" column="state_code"/>
        <result property="mobile" column="mobile"/>
        <result property="leaveTime" column="leave_time"/>
        <result property="leaveReason" column="leave_reason"/>
        <result property="lastWorkDay" column="last_work_day"/>
        <result property="reasonMemo" column="reason_memo"/>
        <result property="preStatus" column="pre_status"/>
        <result property="handoverUserId" column="handover_user_id"/>
        <result property="status" column="status"/>
        <result property="mainDeptName" column="main_dept_name"/>
        <result property="mainDeptId" column="main_dept_id"/>
        <result property="voluntaryReason" column="voluntary_reason"/>
        <result property="passiveReason" column="passive_reason"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectDimissionEmployeeVo">
        select id,
               user_id,
               name,
               state_code,
               mobile,
               leave_time,
               leave_reason,
               last_work_day,
               reason_memo,
               pre_status,
               handover_user_id,
               status,
               main_dept_name,
               main_dept_id,
               voluntary_reason,
               passive_reason,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               is_deleted,
               tenant_id
        from t_dimission_employee
    </sql>

    <select id="selectDimissionEmployeeList" parameterType="com.qmqb.oa.system.domain.DimissionEmployee"
            resultMap="DimissionEmployeeResult">
        <include refid="selectDimissionEmployeeVo"/>
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="tenantId != null ">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectDimissionEmployeeById" parameterType="Long"
            resultMap="DimissionEmployeeResult">
        <include refid="selectDimissionEmployeeVo"/>
        where id = #{id}
    </select>

    <insert id="insertDimissionEmployee" parameterType="com.qmqb.oa.system.domain.DimissionEmployee"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into t_dimission_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="stateCode != null">
                state_code,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="leaveTime != null">
                leave_time,
            </if>
            <if test="leaveReason != null">
                leave_reason,
            </if>
            <if test="lastWorkDay != null">
                last_work_day,
            </if>
            <if test="reasonMemo != null">
                reason_memo,
            </if>
            <if test="preStatus != null">
                pre_status,
            </if>
            <if test="handoverUserId != null">
                handover_user_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="mainDeptName != null">
                main_dept_name,
            </if>
            <if test="mainDeptId != null">
                main_dept_id,
            </if>
            <if test="voluntaryReason != null">
                voluntary_reason,
            </if>
            <if test="passiveReason != null">
                passive_reason,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="stateCode != null">
                #{stateCode},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="leaveTime != null">
                #{leaveTime},
            </if>
            <if test="leaveReason != null">
                #{leaveReason},
            </if>
            <if test="lastWorkDay != null">
                #{lastWorkDay},
            </if>
            <if test="reasonMemo != null">
                #{reasonMemo},
            </if>
            <if test="preStatus != null">
                #{preStatus},
            </if>
            <if test="handoverUserId != null">
                #{handoverUserId},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="mainDeptName != null">
                #{mainDeptName},
            </if>
            <if test="mainDeptId != null">
                #{mainDeptId},
            </if>
            <if test="voluntaryReason != null">
                #{voluntaryReason},
            </if>
            <if test="passiveReason != null">
                #{passiveReason},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <update id="updateDimissionEmployee" parameterType="com.qmqb.oa.system.domain.DimissionEmployee">
        update t_dimission_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">
                user_id =
                #{userId},
            </if>
            <if test="name != null and name != ''">
                name =
                #{name},
            </if>
            <if test="stateCode != null">
                state_code =
                #{stateCode},
            </if>
            <if test="mobile != null">
                mobile =
                #{mobile},
            </if>
            <if test="leaveTime != null">
                leave_time =
                #{leaveTime},
            </if>
            <if test="leaveReason != null">
                leave_reason =
                #{leaveReason},
            </if>
            <if test="lastWorkDay != null">
                last_work_day =
                #{lastWorkDay},
            </if>
            <if test="reasonMemo != null">
                reason_memo =
                #{reasonMemo},
            </if>
            <if test="preStatus != null">
                pre_status =
                #{preStatus},
            </if>
            <if test="handoverUserId != null">
                handover_user_id =
                #{handoverUserId},
            </if>
            <if test="status != null">
                status =
                #{status},
            </if>
            <if test="mainDeptName != null">
                main_dept_name =
                #{mainDeptName},
            </if>
            <if test="mainDeptId != null">
                main_dept_id =
                #{mainDeptId},
            </if>
            <if test="voluntaryReason != null">
                voluntary_reason =
                #{voluntaryReason},
            </if>
            <if test="passiveReason != null">
                passive_reason =
                #{passiveReason},
            </if>
            <if test="remark != null">
                remark =
                #{remark},
            </if>
            <if test="createBy != null">
                create_by =
                #{createBy},
            </if>
            <if test="createTime != null">
                create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">
                update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time =
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted =
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                tenant_id =
                #{tenantId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDimissionEmployeeById" parameterType="Long">
        delete
        from t_dimission_employee
        where id = #{id}
    </delete>

    <delete id="deleteDimissionEmployeeByIds" parameterType="String">
        delete from t_dimission_employee where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

