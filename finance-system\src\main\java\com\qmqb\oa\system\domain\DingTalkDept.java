package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 钉钉部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_dingtalk_dept")
public class DingTalkDept implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 在父部门中的次序值
     */
    private Integer orderNum;

    /**
     * 级别
     */
    private Integer deptLevel;

    /**
     * 部门的主管列表
     */
    private String deptManagerUseridList;

    /**
     * 部门标识字段。
     */
    private String sourceIdentifier;

    /**
     * 是否同步创建一个关联此部门的企业群：1：创建0：不创建
     */
    @TableField("is_create_dept_group")
    private Boolean createDeptGroup;

    /**
     * 当部门群已经创建后，是否有新人加入部门会自动加入该群：1：自动加入群0：不会自动加入群
     */
    @TableField("is_auto_add_user")
    private Boolean autoAddUser;

    /**
     * 是否默认同意加入该部门的申请：1：表示加入该部门的申请将默认同意0：表示加入该部门的申请需要有权限的管理员同意
     */
    @TableField("is_auto_approve_apply")
    private Boolean autoApproveApply;

    /**
     * 部门是否来自关联组织：1：是0：不是第三方企业应用不返回该参数。
     */
    @TableField("is_from_union_org")
    private Boolean fromUnionOrg;

    /**
     * 教育部门标签：campus：校区period：学段grade：年级class：班级第三方企业应用不返回该参数。
     */
    private String tags;

    /**
     * 部门群ID
     */
    private String deptGroupChatId;

    /**
     * 部门群是否包含子部门：1：包含0：不包含
     */
    @TableField("is_group_contain_sub_dept")
    private Boolean groupContainSubDept;

    /**
     * 企业群群主ID
     */
    private String orgDeptOwner;

    /**
     * 是否限制本部门成员查看通讯录：1：开启限制。开启后本部门成员只能看到限定范围内的通讯录0：不限制
     */
    @TableField("is_outer_dept")
    private Boolean outerDept;

    /**
     * 当限制部门成员的通讯录查看范围时（即outer_dept为1时），配置的部门员工可见部门列表。
     */
    private String outerPermitDepts;

    /**
     * 当限制部门成员的通讯录查看范围时（即outer_dept为1时），配置的部门员工可见员工列表。
     */
    private String outerPermitUsers;

    /**
     * 是否隐藏本部门：1：隐藏部门，隐藏后本部门将不会显示在公司通讯录中0：显示部门
     */
    @TableField("is_hide_dept")
    private Boolean hideDept;

    /**
     * 当隐藏本部门时（即hide_dept为1时），配置的允许在通讯录中查看本部门的员工列表。
     */
    private String userPermits;

    /**
     * 当隐藏本部门时（即hide_dept为1时），配置的允许在通讯录中查看本部门的部门列表。
     */
    private String deptPermits;

    /**
     * 部门状态（0正常 1停用）
     */
    private Boolean deptStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除: 0-未删除, 1-删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 租户: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    private Integer tenantId;


}
