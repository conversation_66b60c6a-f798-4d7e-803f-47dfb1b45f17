package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

import java.util.List;

/**
 * 易快报新增部门req
 * <AUTHOR>
 * @date 2024/7/22
 */
@Data
public class EkbAddDeptReq {
    /**
     * 部门编码
     * （1）全民主体格式：“QM-”开头
     * （2）OA主体格式：“OA-”开头
     * 若传参的 code 与停用部门重复，则新增部门
     * 若传参的 code 与启用部门重复，报错 ”该部门编码已存在“
     */
    private String code;

    /**
     * 部门名称
     * 不可传 ""，同一层级不可传重复的值
     */
    private String name;

    /**
     * 上级部门ID
     * 根部门为 ""
     */
    private String parentId;

}
