package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

import java.util.List;

/**
 * 易快报-获取部门
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
public class EkbDepartmentRsp {
    /**
     * 部门id
     */
    private String id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父级部门
     */
    private String parentId;

    /**
     * 部门是否已停用
     */
    private Boolean active;

    /**
     * 部门编码
     */
    private String code;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 部门关联法人实体ID和成本中心ID
     */
    private List form;

    /**
     * 排序序号
     */
    private String order;
}
