package com.qmqb.oa.admin.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.qmqb.oa.admin.api.client.DingTalkApiClient;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.config.DingTalkConfig;
import com.qmqb.oa.admin.domain.DingTalkMsg;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.constant.ApprovalOpt;
import com.qmqb.oa.common.constant.Approver;
import com.qmqb.oa.common.constant.ProcessStatus;
import com.qmqb.oa.common.constant.StringPool;
import com.qmqb.oa.common.enums.ProcessType;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.system.domain.BusinessNotice;
import com.qmqb.oa.system.domain.ProcessRecord;
import com.qmqb.oa.system.service.BusinessNoticeService;
import com.qmqb.oa.system.service.ProcessRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <p>
 * 开票申请
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Slf4j
@Component("billingApplyTask")
@RequiredArgsConstructor
public class BillingApplyTask {

    private final DingTalkConfig dingTalkConfig;
    private final DingTalkApiClient dingTalkApiClient;
    private final DingTalkOApiClient dingTalkOApiClient;
    private final BusinessNoticeService businessNoticeService;
    private final ProcessRecordService processRecordService;

    /**
     * 查找待通知审批单
     */
    public void searchToNotice(Long startTime, Long endTime) {
        if (Objects.isNull(startTime) || Objects.equals(startTime, 0L)) {
            // 审批时间一般当天，最晚5天；拉取7天内数据，防止遗漏
            startTime = DateUtil.beginOfDay(DateUtil.offsetDay(DateTime.now(), -7)).getTime();
        }
        if (Objects.isNull(endTime) || Objects.equals(endTime, 0L)) {
            endTime = DateUtil.endOfDay(DateTime.now()).getTime();
        }
        List<String> processInstanceIds = dingTalkApiClient.listProcessInstanceIds(dingTalkConfig.getBillingApply(Tenant.QMQB.getValue()),
                startTime, endTime, null,
                Collections.emptyList(), Collections.singletonList(ProcessStatus.COMPLETED));
        List<BusinessNotice> saveList = new ArrayList<>();
        for (String processInstanceId : processInstanceIds) {
            int count = businessNoticeService.count(Wrappers.lambdaQuery(BusinessNotice.class).eq(BusinessNotice::getSerialNumber, processInstanceId));
            if (SqlHelper.retBool(count)) {
                log.info("存在通知记录，不重复通知;实例号:{}", processInstanceId);
                continue;
            }
            GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult processInstance = dingTalkApiClient.getProcessInstance(processInstanceId);
            if (Objects.isNull(processInstance)) {
                log.info("获取单个审批实例详情失败;实例号:{}", processInstanceId);
                continue;
            }
            String businessId = processInstance.getBusinessId();
            String result = processInstance.getResult();
            if (!ApprovalOpt.AGREE.equals(result)) {
                log.info("非审批通过流程单，不通知;流水号:{}", businessId);
                continue;
            }
            List<String> ccUserIds = processInstance.getCcUserIds();
            if (!CollUtil.containsAny(ccUserIds, Approver.ROBOT_IDS)) {
                log.info("抄送人不包含对应人员，不通知;流水号:{}", businessId);
                continue;
            }
            BusinessNotice saveObj = new BusinessNotice();
            saveObj.setSerialNumber(processInstanceId);
            Optional<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks> any = processInstance.getTasks().stream()
                    .filter(e -> Approver.ZHENG_JI_CHUN_ID.equals(e.getUserId())).findAny();
            // 发起人+郑记纯
            saveObj.setNoticeObject(processInstance.getOriginatorUserId().concat(any.isPresent() ? StrPool.COMMA + Approver.ZHENG_JI_CHUN_ID : ""));
            saveObj.setNoticeContent("发票已开出15天，请注意款项是否已收齐");
            DateTime finishTime = DateUtil.parse(processInstance.getFinishTime(), "yyyy-MM-dd'T'HH:mm'Z'");
            DateTime after15 = DateUtil.offsetDay(finishTime, 15);
            saveObj.setNoticeTime(after15);
            saveObj.setNoticeCount(0);
            saveObj.setNoticeStatus(1);
            saveObj.setRemark(businessId + processInstance.getTitle());
            saveObj.setCreateTime(DateTime.now());
            saveList.add(saveObj);

            saveProcessRecord(processInstance, processInstanceId);
        }
        if (CollUtil.isNotEmpty(saveList)) {
            businessNoticeService.saveBatch(saveList);
        }
    }

    /**
     * 保存流程处理记录
     *
     * @param processInstance
     * @param processInstanceId
     */
    private boolean saveProcessRecord(GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult processInstance,
                                      String processInstanceId) {
        ProcessRecord processRecord = new ProcessRecord();
        processRecord.setProcessInstanceId(processInstanceId);
        processRecord.setTitle(processInstance.title);
        processRecord.setBusinessId(processInstance.businessId);
        processRecord.setOriginatorDeptId(processInstance.originatorDeptId);
        processRecord.setOriginatorDeptName(processInstance.originatorDeptName);
        processRecord.setIsPass(BoolFlagEnum.YES.getStatus());
        processRecord.setResult(processInstance.result);

        processRecord.setProcessInstanceId(processInstanceId);
        OapiV2UserGetResponse.UserGetResponse user = dingTalkOApiClient.getUser(processInstance.originatorUserId);
        processRecord.setOriginatorUserId(processInstance.originatorUserId);
        processRecord.setOriginatorUserName(Objects.nonNull(user) ? user.getName() : StringPool.EMPTY);
        processRecord.setProcessType(ProcessType.BILLING_APPLY.getValue());
        processRecord.setTenantId(TenantContextHolder.getTenantId());
        return processRecordService.save(processRecord);
    }


    /**
     * 执行通知
     */
    public void executeNotice() {
        Date now = new Date();
        List<BusinessNotice> notices = businessNoticeService.list(Wrappers.lambdaQuery(BusinessNotice.class)
                .eq(BusinessNotice::getNoticeStatus, 1).le(BusinessNotice::getNoticeTime, now));
        if (CollUtil.isEmpty(notices)) {
            log.info("无待通知记录");
            return;
        }
        List<BusinessNotice> updateList = new ArrayList<>(notices.size());
        notices.forEach(notice -> {
            DingTalkMsg msg = DingTalkMsg.builder()
                    .msgtype("markdown")
                    .markdown(
                            DingTalkMsg.MarkdownDTO.builder()
                                    .title("开票款项通知")
                                    .text(String.format("![markdown](https://e-pay.oss-cn-shenzhen.aliyuncs.com/prod/image/20230711/notice.jpeg)\n\n%s\n\n**%s**", notice.getRemark(), notice.getNoticeContent()))
                                    .build())
                    .build();
            boolean success = dingTalkOApiClient.sendWorkNotice(msg, notice.getNoticeObject());
            log.info("触发通知，通知状态：{}，通知对象：{}", success ? "成功" : "失败", notice.getNoticeObject());
            notice.setNoticeStatus(success ? 2 : 3);
            notice.setNoticeCount(notice.getNoticeCount() + 1);
            notice.setUpdateTime(now);
            updateList.add(notice);
        });
        if (CollUtil.isNotEmpty(updateList)) {
            businessNoticeService.updateBatchById(updateList);
        }
    }
}
