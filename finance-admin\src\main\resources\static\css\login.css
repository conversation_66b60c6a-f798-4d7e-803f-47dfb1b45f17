body,h1,h2,h3,h4,h5,h6,hr,p,blockquote,dl,dt,dd,ul,ol,li,pre,fieldset,lengend,button,input,textarea,form,th,td{margin:0;padding:0;}
body,button,input,select,textarea{font:12px/1.5 "Microsoft YaHei","\5b8b\4f53";}

address,cite,dfn,em,i,var{font-style:normal;}
small{font-size:12px;}
ul,ol,li{list-style:none;}
a{text-decoration:none; }
a:link,a:visited,a:hover,a:active{text-decoration:none; outline:none;}
abbr[title],acronym[title]{border-bottom:1px dotted;cursor:help;}
q:before,q:after{content:'';}:focus{outline:0;}legend{color:#000;}
fieldset,img,input{border:none; background:none;}button,input,select,textarea{font-size:100%;}
table{border-collapse:collapse;border-spacing:0;}
hr{border:none;height:1px;*color:#fff;}img{-ms-interpolation-mode:bicubic;}
.clearFix:after{content:".";display:block;height:0;clear:both;visibility:hidden}
.clearFix{zoom:1}
/*Flex 布局*/
.flex_row{display: flex; flex-direction: row;}
.flex_column{display: flex; flex-direction: column}
.flex_row_center{justify-content: center}
.flex_column_center{align-items: center;}
.flex_center{justify-content: center;align-items: center;}
.self_flex_start{align-self: flex-start;}
.self_flex_end{align-self: flex-end;}

.clearFix:after{content:".";display:block;height:0;clear:both;visibility:hidden}
.clearFix{zoom:1}
.fl{ float:left;}
.fr{ float: right;}
.rel{ position: relative;}
.abs{ position: absolute;}
.fix{ position: fixed}
.ell{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.text_center{ text-align: center;}
.midLine{ text-decoration:line-through;}

body { height: 100%; background-color: #f2f2f2; }
.login_wrap { position: relative; left: 0; top: 0; padding: 110px 0; min-height: 100%; box-sizing: border-box; }
.login_wrap .login_main { width: 375px; margin: 0 auto; box-sizing: border-box; }
.login_main .login_header { padding: 20px; text-align: center; }
.login_header h2 { margin-bottom: 10px; font-weight: 300; font-size: 30px; color: #000; }
.login_header p { font-weight: 300; color: #999; }
.login_main .login_box { padding: 20px; }
.login_box .login_item { position: relative; margin-bottom: 15px; clear: both; *zoom: 1; }
.login_input { display: block; width: 100%; height: 38px; padding-left: 38px; -height: 1.3; line-height: 38px\9; border: 1px solid #e6e6e6; background-color: #fff; background-repeat: no-repeat; background-position: left center; border-radius: 2px; outline: 0; -webkit-appearance: none; box-sizing: border-box; font: 400 13.3333px Arial; }
.login_input.username { background-image: url(../images/icon_username.jpg); }
.login_input.pwd { background-image: url(../images/icon_password.jpg); }
.login_btn { width: 100%; display: inline-block; height: 38px; line-height: 38px; padding: 0 18px; background-color: #009688; color: #fff; white-space: nowrap; text-align: center; font-size: 14px; border: none; border-radius: 2px; cursor: pointer; }