package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.common.util.IdUtil;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.mapper.SysEventMapper;
import com.qmqb.oa.system.service.SysEventService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 系统事件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Service
public class SysEventServiceImpl extends ServiceImpl<SysEventMapper, SysEvent> implements SysEventService {

    @Override
    public String saveSysEvent(Integer companyId,Integer eventSource, String eventType, String reqData, Long eventTime, String param, String failReason) {
        String requestNo = IdUtil.getIdStr();
        SysEvent sysEvent = new SysEvent();
        sysEvent.setRequestNo(requestNo);
        sysEvent.setCompanyId(companyId);
        sysEvent.setEventSource(eventSource);
        sysEvent.setEventType(eventType);
        sysEvent.setReqData(reqData);
        sysEvent.setEventTime(eventTime);
        sysEvent.setReqParams(param);
        sysEvent.setStatus(2);
        sysEvent.setFailReason(failReason);
        this.save(sysEvent);
        return requestNo;
    }

    @Override
    public void updateEventStatusByRequestNo(String requestNo, Integer status,String failReason) {
        SysEvent sysEvent = this.getOne(Wrappers.lambdaQuery(SysEvent.class).eq(SysEvent::getRequestNo, requestNo));
        SysEvent updateSysEvent = new SysEvent();
        updateSysEvent.setId(sysEvent.getId());
        updateSysEvent.setStatus(status);
        updateSysEvent.setFailReason(failReason);
        updateSysEvent.setUpdateTime(LocalDateTime.now());
        this.updateById(updateSysEvent);

    }

    /**
     * 更新失败原因
     *
     * @param sysEvent
     * @param failReason
     */
    @Override
    public void updateFailReasonById(SysEvent sysEvent, String failReason) {
        // 更新事件表，状态=失败，失败次数+1，失败原因
        SysEvent update = new SysEvent();
        update.setId(sysEvent.getId());
        update.setFailCount(sysEvent.getFailCount() + 1);
        update.setUpdateTime(LocalDateTime.now());
        update.setFailReason(failReason);
        this.updateById(update);
    }

    /**
     * 更新事件，状态=处理成功
     *
     * @param sysEvent
     */
    @Override
    public void update4SuccessById(SysEvent sysEvent) {
        SysEvent update = new SysEvent();
        update.setId(sysEvent.getId());
        update.setStatus(1);
        update.setFailReason("成功");
        update.setUpdateTime(LocalDateTime.now());
        this.updateById(update);
    }

    @Override
    public SysEvent getSysEventByTypeAndParams(Integer companyId, Integer eventSource, String eventType, String reqParams) {
        LambdaQueryWrapper<SysEvent> queryWrapper = Wrappers.lambdaQuery(SysEvent.class)
                .eq(SysEvent::getCompanyId, companyId)
                .eq(SysEvent::getEventSource, eventSource)
                .eq(SysEvent::getEventType, eventType)
                .eq(SysEvent::getReqParams, reqParams)
                .orderByDesc(SysEvent::getCreateTime);
        List<SysEvent> sysEvents = list(queryWrapper);
        return CollUtil.isNotEmpty(sysEvents) ? sysEvents.get(0) : null;
    }
}
