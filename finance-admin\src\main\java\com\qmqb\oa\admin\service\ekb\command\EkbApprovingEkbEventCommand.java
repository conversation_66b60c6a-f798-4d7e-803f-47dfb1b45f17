package com.qmqb.oa.admin.service.ekb.command;

import com.hzed.structure.common.api.ResultCode;
import com.hzed.structure.tool.api.ApiResponse;
import com.qmqb.oa.admin.domain.dto.EkbEventCommandDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbDingTodoResultDTO;
import com.qmqb.oa.admin.domain.request.internal.EkbReceiveEkbEventRequest;
import com.qmqb.oa.common.enums.EkbActionEnum;
import com.qmqb.oa.common.enums.EkbFlowStageNameEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Description ekb待审批事件
 * @Date 2024\10\12 0012 10:50
 * @Version 1.0
 */

@Slf4j
@Service(value = "ekbApprovingCommand")
public class EkbApprovingEkbEventCommand extends AbstractEkbEventCommand<EkbReceiveEkbEventRequest, EkbEventCommandDTO> {


    @Override
    public boolean validation() {
        if (!super.checkBaseParams()) {
            return false;
        }

        if (!getParamObject().getAction().equals(EkbActionEnum.BACKLOG_APPROVING.getAction())) {
            return false;
        }

        return true;
    }

    @Override
    public ApiResponse<EkbEventCommandDTO> execute() {
        if (!validation()) {
            return ApiResponse.fail("approving 事件参数校验失败");
        }

        //已经执行过不需要重复执行
        if (eventHasExecute()) {
            return ApiResponse.success(ResultCode.SUCCESS, "ekb approving event deal success");
        }

        EkbReceiveEkbEventRequest request = (EkbReceiveEkbEventRequest) getParamObject();
        boolean updateResult = sendToDoDingMsg(request);

        ApiResponse apiResponse = null;
        if (updateResult) {
            Long[] taskIds = new Long[1];
            taskIds[0] = getEkbEventCommandDTO().getEkbDingTodoMessage().getId();
            //更新待办消息结果处理状态
            EkbDingTodoResultDTO ekbDingTodoResultDTO = EkbDingTodoResultDTO.builder().build().
                    setTaskProcessedResult(EkbFlowStageNameEnums.NO_SUBMIT.getDbCode())
                    .setMsgAction(EkbActionEnum.BACKLOG_APPROVING.getAction());
            updateApprovingMsgRemark(ekbDingTodoResultDTO, taskIds);
            apiResponse = ApiResponse.success(ResultCode.SUCCESS, "ekb approving event deal success");
        } else {
            apiResponse = ApiResponse.fail();
        }
        //设置处理后的事件传输实体
        apiResponse.setData(getEkbEventCommandDTO());
        return apiResponse;
    }
}
