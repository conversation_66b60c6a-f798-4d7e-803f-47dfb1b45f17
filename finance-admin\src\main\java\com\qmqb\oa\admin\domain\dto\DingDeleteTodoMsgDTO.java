package com.qmqb.oa.admin.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 钉钉删除待办消息接口请求实体
 * <AUTHOR>
 * @date 2024/10/11
 */
@Data
@Accessors(chain = true)
@Builder
public class DingDeleteTodoMsgDTO {
    /**
     * 易快报消息id
     */
    private String messageId;
    /**
     * 我方公司主体
     */
    private Integer companyId;
    /**
     * 钉钉待办消息id
     */
    private String taskId;
    /**
     * 待办接收人的unionId
     */
    private String recipientUnionId;

    /**
     * 提交人的unionId
     */
    private String submitUnionId;

}
