package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.common.utils.DateUtils;
import com.qmqb.oa.system.domain.EmployeeSubject;
import com.qmqb.oa.system.mapper.EmployeeSubjectMapper;
import com.qmqb.oa.system.service.EmployeeSubjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工主体Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@Service
public class EmployeeSubjectServiceImpl extends ServiceImpl<EmployeeSubjectMapper, EmployeeSubject> implements EmployeeSubjectService {
    @Autowired
    private EmployeeSubjectMapper employeeSubjectMapper;

    /**
     * 查询员工主体
     *
     * @param id 员工主体ID
     * @return 员工主体
     */
    @Override
    public EmployeeSubject selectEmployeeSubjectById(Long id) {
        return employeeSubjectMapper.selectEmployeeSubjectById(id);
    }

    /**
     * 查询员工主体列表
     *
     * @param employeeSubject 员工主体
     * @return 员工主体
     */
    @Override
    public List<EmployeeSubject> selectEmployeeSubjectList(EmployeeSubject employeeSubject) {
        return employeeSubjectMapper.selectEmployeeSubjectList(employeeSubject);
    }

    /**
     * 新增员工主体
     *
     * @param employeeSubject 员工主体
     * @return 结果
     */
    @Override
    public int insertEmployeeSubject(EmployeeSubject employeeSubject) {
        employeeSubject.setCreateTime(DateUtils.getNowDate());
        return employeeSubjectMapper.insertEmployeeSubject(employeeSubject);
    }

    /**
     * 修改员工主体
     *
     * @param employeeSubject 员工主体
     * @return 结果
     */
    @Override
    public int updateEmployeeSubject(EmployeeSubject employeeSubject) {
        employeeSubject.setUpdateTime(DateUtils.getNowDate());
        return employeeSubjectMapper.updateEmployeeSubject(employeeSubject);
    }

    /**
     * 批量删除员工主体
     *
     * @param ids 需要删除的员工主体ID
     * @return 结果
     */
    @Override
    public int deleteEmployeeSubjectByIds(Long[] ids) {
        return employeeSubjectMapper.deleteEmployeeSubjectByIds(ids);
    }

    /**
     * 删除员工主体信息
     *
     * @param id 员工主体ID
     * @return 结果
     */
    @Override
    public int deleteEmployeeSubjectById(Long id) {
        return employeeSubjectMapper.deleteEmployeeSubjectById(id);
    }

    @Override
    public List<EmployeeSubject> listByNames(List<String> names) {
        return employeeSubjectMapper.listByNames(names);
    }

}
