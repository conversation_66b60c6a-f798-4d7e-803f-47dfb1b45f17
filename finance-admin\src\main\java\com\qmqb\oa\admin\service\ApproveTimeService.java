package com.qmqb.oa.admin.service;

import cn.hutool.core.date.DateUtil;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.common.util.date.SystemClock;
import com.qmqb.oa.common.core.domain.entity.SysDictData;
import com.qmqb.oa.common.enums.DictType;
import com.qmqb.oa.system.service.SysDictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023-03-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApproveTimeService {

    public static final String BEGIN_TIME = "开始时间";
    public static final String END_TIME = "结束时间";

    private final SysDictTypeService sysDictTypeService;

    /**
     * 审批时间列表
     *
     * @return
     */
    public Map<String, Long> approveListTime() {
        List<SysDictData> data = sysDictTypeService.selectDictDataByType(DictType.APPROVE_LIST_TIME.getType());
        Long beginTime = null;
        Long endTime = null;
        if (CollUtil.isNotEmpty(data)) {
            Optional<String> beginOpt = data.stream().filter(e -> BEGIN_TIME.equals(e.getDictLabel())).map(SysDictData::getDictValue).findFirst();
            if (beginOpt.isPresent()) {
                beginTime = Long.valueOf(beginOpt.get());
            }
            Optional<String> endOpt = data.stream().filter(e -> END_TIME.equals(e.getDictLabel())).map(SysDictData::getDictValue).findFirst();
            if (endOpt.isPresent()) {
                endTime = Long.valueOf(endOpt.get());
            }
        }
        // 时间为空以当天为准
        if (Objects.isNull(beginTime)) {
            beginTime = DateUtil.beginOfDay(new Date()).getTime();
        }
        if (Objects.isNull(endTime)) {
            endTime = SystemClock.now();
        }
        Map<String, Long> time = new HashMap<>(2);
        time.put(BEGIN_TIME, beginTime);
        time.put(END_TIME, endTime);
        return time;
    }
}
