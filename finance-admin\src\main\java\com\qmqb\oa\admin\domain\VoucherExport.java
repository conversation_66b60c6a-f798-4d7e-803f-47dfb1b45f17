package com.qmqb.oa.admin.domain;

import com.qmqb.oa.common.annotation.Excel;
import lombok.Data;

/**
 * <p>
 * 全民钱包导出模板
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Data
public class VoucherExport {
    @Excel(name = "制单日期", dateFormat = "yyyy/MM/dd")
    private String makeDate;
    @Excel(name = "凭证类别", dictType = "finance_voucher_type")
    private String voucherType;
    @Excel(name = "凭证编号")
    private String voucherNumber;
    @Excel(name = "差异凭证")
    private String diffVoucher;
    @Excel(name = "来源类型")
    private String sourceType;
    @Excel(name = "附单据数")
    private String attachedDocs;
    @Excel(name = "摘要")
    private String summary;
    @Excel(name = "科目编码")
    private String subjectCode;
    @Excel(name = "科目")
    private String subject;
    @Excel(name = "币种", dictType = "finance_currency")
    private String currency;
    @Excel(name = "数量")
    private String number;
    @Excel(name = "单价")
    private String unitPrice;
    @Excel(name = "借贷方向", dictType = "finance_borrow_direction")
    private String borrowDirection;
    @Excel(name = "原币")
    private String originalCurrency;
    @Excel(name = "本币")
    private String domesticCurrency;
    @Excel(name = "票据号")
    private String billNo;
    @Excel(name = "票据日期")
    private String billDate;
    @Excel(name = "业务单号")
    private String bizNo;
    @Excel(name = "业务日期")
    private String bizDate;
    @Excel(name = "到期日")
    private String maturityDate;
    @Excel(name = "业务员编码")
    private String saleManCode;
    @Excel(name = "业务员")
    private String saleMan;
    @Excel(name = "银行帐号")
    private String bankAccount;
    @Excel(name = "结算方式")
    private String paymentMethod;
    @Excel(name = "往来单位编码")
    private String orgCode;
    @Excel(name = "往来单位")
    private String org;
    @Excel(name = "部门编码")
    private String deptCode;
    @Excel(name = "部门")
    private String dept;
    @Excel(name = "存货编码")
    private String inventoryCode;
    @Excel(name = "存货")
    private String inventory;
    @Excel(name = "人员编码")
    private String personnelCode;
    @Excel(name = "人员")
    private String personnel;
    @Excel(name = "项目编码")
    private String itemCode;
    @Excel(name = "项目")
    private String item;
    @Excel(name = "扩展辅助1编码")
    private String extCode1;
    @Excel(name = "扩展辅助1")
    private String ext1;
    @Excel(name = "扩展辅助2编码")
    private String extCode2;
    @Excel(name = "扩展辅助2")
    private String ext2;
    @Excel(name = "扩展辅助3编码")
    private String extCode3;
    @Excel(name = "扩展辅助3")
    private String ext3;
    @Excel(name = "扩展辅助4编码")
    private String extCode4;
    @Excel(name = "扩展辅助4")
    private String ext4;
    @Excel(name = "扩展辅助5编码")
    private String extCode5;
    @Excel(name = "扩展辅助5")
    private String ext5;
    @Excel(name = "扩展辅助6编码")
    private String extCode6;
    @Excel(name = "扩展辅助6")
    private String ext6;
    @Excel(name = "扩展辅助7编码")
    private String extCode7;
    @Excel(name = "扩展辅助7")
    private String ext7;
    @Excel(name = "扩展辅助8编码")
    private String extCode8;
    @Excel(name = "扩展辅助8")
    private String ext8;
    @Excel(name = "扩展辅助9编码")
    private String extCode9;
    @Excel(name = "扩展辅助9")
    private String ext9;
    @Excel(name = "扩展辅助10编码")
    private String extCode10;
    @Excel(name = "扩展辅助10")
    private String ext10;
}
