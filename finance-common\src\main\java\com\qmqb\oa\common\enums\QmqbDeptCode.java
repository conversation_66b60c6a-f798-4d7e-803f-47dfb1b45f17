package com.qmqb.oa.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全民钱包部门编码
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QmqbDeptCode {
    /**
     * 部门编码, 拼音便于区分
     */
    FENG_KONG("0009", "风控部"),
    XING_ZHENG("001", "行政"),
    GUAN_LI("0012", "管理部"),
    YUN_YING("002", "运营部"),
    CAI_WU("003", "财务部"),
    JI_SHU("004", "技术部"),
    ZI_CHAN_SHI_YE("005", "资产事业中心"),
    JI_SHU_ZHONG_XIN("006", "技术中心"),
    YIN_NI_XIANG_MU("007", "印尼项目部"),
    WAI_BU_REN_YUAN("008", "外部人员"),
    ZI_CHAN_GUAN_LI("009", "资产管理中心"),
    JI_GOU_HE_ZUO("010", "机构合作部"),
    HAI_WAI_SHI_YE("011", "海外事业部"),
    JIN_RONG_SHI_YE("012", "金融事业部"),
    DIAN_SHANG_SHI_YE("013", "电商事业部"),
    BIAN_WAI("014", "编外"),
    HE_GUI_ZHONG_XIN("015", "合规中心"),
    FA_WU_ZHONG_XIN("016", "法务中心"),
    REN_LI_ZI_YUAN("017", "人力资源部"),
    ZONG_JING_BAN("018", "总经办"),
    ZHI_FU_JIAO_YI("023", "支付与交易项目组"),
    ;

    private final String code;
    private final String name;

}
