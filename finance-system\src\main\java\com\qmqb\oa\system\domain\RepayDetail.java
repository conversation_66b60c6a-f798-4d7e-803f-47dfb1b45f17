package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 对账明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_repay_detail")
public class RepayDetail implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 程序编号
     */
      private Integer programId;

      /**
     * 批次号
     */
      private String batchNo;

      /**
     * 开始日期
     */
      private LocalDateTime startDate;

      /**
     * 结束日期
     */
      private LocalDateTime endDate;

      /**
     * 文件路径
     */
      private String filePath;

      /**
     * 创建人
     */
      private String creator;

      /**
     * 更新人
     */
      private String updater;

      /**
     * 记录创建时间
     */
      private LocalDateTime dbCreateDt;

      /**
     * 记录更新时间
     */
      private LocalDateTime dbUpdateDt;

      /**
     * 数据类型:1-资金还款明细, 2-资金资产还款明细
     */
      private Integer dataType;


}
