package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.util.AssertUtil;
import com.qmqb.oa.system.domain.Subject;
import com.qmqb.oa.system.mapper.SubjectMapper;
import com.qmqb.oa.system.service.SubjectDeptService;
import com.qmqb.oa.system.service.SubjectService;
import com.qmqb.oa.system.service.SysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 科目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Service
public class SubjectServiceImpl extends ServiceImpl<SubjectMapper, Subject> implements SubjectService {

    @Autowired
    private SubjectDeptService subjectDeptService;
    @Autowired
    private SysDeptService sysDeptService;

    /**
     * 根据
     *
     * @param subjectName
     * @param dingTalkDeptId
     * @return
     */
    @Override
    public Subject selectBySubjectNameAndDingTalkDeptId(String subjectName, String dingTalkDeptId) {
        AssertUtil.hasLength(subjectName, "subjectName不能为空");
        AssertUtil.hasLength(dingTalkDeptId, "dingTalkDeptId不能为空");
        return this.baseMapper.selectBySubjectNameAndDingTalkDeptId(subjectName, dingTalkDeptId);
    }

    @Override
    public Subject selectBySubjectName(String subjectName) {
        QueryWrapper<Subject> wrapper = new QueryWrapper<>();
        wrapper.eq("subject_name", subjectName);
        return getOne(wrapper);
    }

    /**
     * 查询科目
     *
     * @param id 科目id
     * @return 科目
     */
    @Override
    public Subject selectSubjectById(Long id) {
        AssertUtil.notNull(id, "id不能为空");
        return this.baseMapper.selectSubjectById(id);
    }

    @Override
    public List<Subject> selectSubjectList(Subject subject) {
        return this.baseMapper.selectSubjectList(subject);
    }

    @Override
    public boolean deleteSubjectByIds(Long[] ids) {
        // 判断是否存在子级科目
        for (Long id : ids) {
            Subject subject = this.baseMapper.selectById(id);
            if (subject == null) {
                continue;
            }
            QueryWrapper<Subject> wrapper = new QueryWrapper<>();
            wrapper.eq("parent_subject_code", subject.getSubjectCode());
            Integer count = this.baseMapper.selectCount(wrapper);
            if (count > 0) {
                return false;
            }
        }
        // 删除中间表信息
        boolean deleteSubjectDept = subjectDeptService.deleteSubjectDeptBySubjectIds(ids);
        boolean deleteSubject = this.removeByIds(Arrays.asList(ids));
        return deleteSubjectDept && deleteSubject;
    }
}
