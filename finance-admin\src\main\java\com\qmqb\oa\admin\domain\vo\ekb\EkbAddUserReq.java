package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

import java.util.List;

/**
 * 易快报新增员工
 *
 * <AUTHOR>
 * @date 2024/7/31
 */
@Data
public class EkbAddUserReq {
    /**
     * 员工姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String cellphone;

    /**
     * 默认部门
     * 请确保默认部门在 departments 里。如果不在，系统会自动将departments的第一个元素视为默认部门
     */
    private String defaultDepartment;

    /**
     * 所在部门ID，至少1个
     * 兼职部门，请确保至少包含默认部门
     */
    private List<String> departments;

}