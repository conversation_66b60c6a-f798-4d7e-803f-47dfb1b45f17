package com.qmqb.oa.admin.controller.hsfk;

import com.qmqb.oa.admin.service.hsfk.HeSiFeiKongService;
import com.qmqb.oa.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2024/7/9
 */
@Api("用户信息管理")
@Controller
@Slf4j
@RequestMapping("/index")
public class IndexController extends BaseController {
    @Autowired
    private HeSiFeiKongService heSiFeiKongService;
    //dingdc0a16e71772ca06bc961a6cb783455b
    @ApiOperation("免登陆首页")
    @GetMapping("/dingAuthIndex")
    public String dingAuthIndex(Integer client,String corpId,Model model) {
        model.addAttribute("corpId",corpId);
        log.info(corpId);
        return "index";
    }

    @ApiOperation("获取免登code")
    @GetMapping("/getCode")
    public String getCode(String code,Model model) throws Exception{
        model.addAttribute("code",code);
        log.info(code);
        log.info("corPid:" + code + " code:" +code);
//        model.addAttribute("code",code);
        return "index";
    }

}
