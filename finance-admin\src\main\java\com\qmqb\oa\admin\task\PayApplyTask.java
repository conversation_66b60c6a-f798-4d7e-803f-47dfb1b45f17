package com.qmqb.oa.admin.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dingtalk.api.response.OapiOcrStructuredRecognizeResponse;
import com.dingtalk.api.response.OapiProcessinstanceFileUrlGetResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.common.util.CollUtil;
import com.hzed.structure.oss.AliOssTemplate;
import com.hzed.structure.oss.domain.OssResult;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.config.DingTalkConfig;
import com.qmqb.oa.admin.domain.*;
import com.qmqb.oa.admin.service.ApproveTimeService;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.constant.*;
import com.qmqb.oa.common.core.domain.entity.SysDept;
import com.qmqb.oa.common.core.domain.entity.SysDictData;
import com.qmqb.oa.common.core.redis.RedisCache;
import com.qmqb.oa.common.enums.*;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.common.utils.file.FileUtils;
import com.qmqb.oa.common.utils.file.MimeTypeUtils;
import com.qmqb.oa.system.domain.InvoiceOcrRecord;
import com.qmqb.oa.system.domain.ProcessRecord;
import com.qmqb.oa.system.domain.ReimbursementVoucher;
import com.qmqb.oa.system.domain.Subject;
import com.qmqb.oa.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 付款申请服务类
 * <AUTHOR>
 * @since 2021-09-10
 */
@Slf4j
@Component("payApplyTask")
@RequiredArgsConstructor
public class PayApplyTask {

    private final DingTalkOApiClient dingTalkOApiClient;
    private final DingTalkConfig dingTalkConfig;
    private final AliOssTemplate aliOssTemplate;
    private final InvoiceOcrRecordService invoiceOcrRecordService;
    private final SysDeptService sysDeptService;
    private final SubjectService subjectService;
    private final ReimbursementVoucherService reimbursementVoucherService;
    private final ProcessRecordService processRecordService;
    private final SysDictDataService sysDictDataService;
    private final SysDictTypeService sysDictTypeService;
    private final ApproveTimeService approveTimeService;
    private final RedisCache redisCache;

    /**
     * 凭证编号填充常量
     */
    private static final Character VOUCHER_NUMBER_FILL_CHAR = '0';
    private static final Integer VOUCHER_NUMBER_LENGTH = 4;

    /**
     * 审批表单域标识
     */
    private static final String DEPT = "部门";
    private static final String REMARK = "摘要";
    private static final String FEE_TYPE = "费用类别";
    private static final String AMOUNT = "金额（元）";
    private static final String BANK_NAME = "收款账户";
    private static final String INVOICE_CONDITION = "发票情况";
    private static final List<String> ADMINISTRATION_FEE = Arrays.asList("1123", "5602", "222105", "222126", "********", "********", "********", "********", "********", "********", "********");

    /**
     * 附件格式
     */
    private static final String FILE_TYPE_ZIP = "zip";

    /**
     * 发票情况
     */
    private static final String INVOICE_AFTER_PAY = "付款后补发票";
    private static final String SPECIAL_INVOICE = "已向财务提供专票";


    /**
     * 付款申请
     */
    public void payApply(Integer tenantId, String sourceStartTime, String sourceEndTime, Boolean isCachEndTime, Integer daysBefore) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "付款申请");
            // 查询审批实例ID列表

            Map<String, Long> time = approveTimeService.approveListTime();
            Long beginTime = time.get(ApproveTimeService.BEGIN_TIME);
            Long endTime = time.get(ApproveTimeService.END_TIME);
            String key = RedisCacheKey.PAY_APPLY_LAST_QUERY_TIME.getKey(tenantId);
            Long lastQueryTime = redisCache.getCacheObject(key);
            if(Objects.nonNull(lastQueryTime)) {
                beginTime = lastQueryTime;
            }
            if(StringUtil.isNotEmpty(sourceStartTime)) {
                beginTime = DateUtil.parseDateTime(sourceStartTime).getTime();
            }

            if(StringUtil.isNotEmpty(sourceEndTime)) {
                endTime = DateUtil.parseDateTime(sourceEndTime).getTime();
            }

            if(Objects.nonNull(daysBefore)) {
                Date dateBefore = DateUtils.addDays(new Date(), -daysBefore);
                beginTime = com.qmqb.oa.common.utils.DateUtils.dateToStart(dateBefore).getTime();
                endTime = com.qmqb.oa.common.utils.DateUtils.dateToEnd(dateBefore).getTime();
            }

            log.info("查询的开始时间：{}, 结束时间：{}", beginTime, endTime);
            List<String> processInstanceIds = dingTalkOApiClient.listProcessInstanceIds(
                    dingTalkConfig.getPayApply(tenantId), null, beginTime, endTime);
            if (CollectionUtil.isEmpty(processInstanceIds)) {
                redisCache.setCacheObject(key, endTime);
                log.info("获取付款申请实例ID列表为空,不执行此次任务");
                return;
            }
            log.info("本次查询付款申请个数:{}", processInstanceIds.size());
            handPayApply(processInstanceIds);
            if(!isCachEndTime) {
                return;
            }
            redisCache.setCacheObject(key, endTime);
            log.info("付款申请消费者执行结束");
        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }

    /**
     * 处理付款申请
     */
    private void handPayApply(List<String> processInstanceIds) {
        for (String processInstanceId : processInstanceIds) {
            MdcUtil.putProcess(processInstanceId);
            List<ReimbursementVoucher> reimbursementVouchers = reimbursementVoucherService.getByProcessInstanceId(processInstanceId);
            if (CollUtil.isNotEmpty(reimbursementVouchers)) {
                log.info("该报销凭证数据已生成过,审批实例ID:{}", processInstanceId);
                continue;
            }
            // 查询审批实例详情
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = dingTalkOApiClient.getProcessInstance(processInstanceId);

            String businessId = processInstance.getBusinessId();
            // 查询当前任务id
            Optional<OapiProcessinstanceGetResponse.TaskTopVo> taskOptional = processInstance.getTasks().stream()
                    .filter(task -> CashierApprover().contains(task.getUserid())).findFirst();
            if (!taskOptional.isPresent()) {
                log.info("当前流程非出纳审批状态,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                continue;
            }
            OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = taskOptional.get();
            if (!ProcessStatus.COMPLETED.equalsIgnoreCase(taskTopVo.getTaskStatus()) || !ApprovalOpt.AGREE.equalsIgnoreCase(taskTopVo.getTaskResult())) {
                log.info("当前流程审批未通过,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                continue;
            }

            log.info("开始生成付款申请报销凭证,审批实例ID:{}, 业务编号:{}", processInstanceId, businessId);


            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();

            // 获取表格信息域
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> tableFieldOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> TableFieldId.TABLE_FIELD.equals(formComponentValueVo.getComponentType())).findFirst();
            if (!tableFieldOptional.isPresent()) {
                log.info("未获取到表格信息域,审批实例ID:{}, 业务编号:{}", processInstanceId, businessId);
                continue;
            }
            List<PayApplyTableData> tableDataList = getTableData(processInstanceId, tableFieldOptional.get(), businessId);
            if (CollUtil.isEmpty(tableDataList)) {
                continue;
            }

            // 获取表格信息域
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> deptOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> DEPT.equals(formComponentValueVo.getName())).findFirst();
            if(!deptOptional.isPresent()) {
                log.info("未获取到表格部门信息,审批实例ID:{}, 业务编号:{}", processInstanceId, businessId);
                continue;
            }
            List<Map<String, String>> depts = JSON.parseObject(deptOptional.get().getExtValue(), new TypeReference<List<Map<String, String>>>() {
            });
            if (depts.size() > 1) {
                log.info("存在多个部门数据,审批实例ID:{}, 业务编号:{}", processInstanceId, businessId);
                continue;
            }

            String deptId = depts.get(0).get("id");
            // 获取表格户名信息域
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> bankNameOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> BANK_NAME.equals(formComponentValueVo.getName())).findFirst();
            String companyName = null;
            String companyCode = null;
            if(bankNameOptional.isPresent()) {
                companyName = bankNameOptional.get().getValue();
                companyCode = sysDictDataService.getDictValuByTypeAndLabel(DictType.COOPERATION_COMPANY.getType(), companyName);
            }
            boolean administrationFeeExist = false;
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> invoiceConditionOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> INVOICE_CONDITION.equals(formComponentValueVo.getName()))
                    .findFirst();
            Subject subject = null;
            //查询其他科目信息的标识
            boolean getOtherSubject = true;
            boolean isPayAffterInvoice = false;
            Integer specialInvoice = Constants.CommonVal.ZERO;
            if(invoiceConditionOptional.isPresent()) {
                String value = invoiceConditionOptional.get().getValue();
                if(INVOICE_AFTER_PAY.equals(value)) {
                    subject = subjectService.selectBySubjectName(SubjectName.ADVANCE_PAY_BILL.getName());
                    getOtherSubject = false;
                    isPayAffterInvoice = true;
                }else if(SPECIAL_INVOICE.equals(value)) {
                    specialInvoice = Constants.CommonVal.ONE;
                }
            }
            for (PayApplyTableData tableData : tableDataList) {
                ReimbursementVoucher reimbursementVoucher = new ReimbursementVoucher();
                reimbursementVoucher.setProcessInstanceId(processInstanceId);
                reimbursementVoucher.setMakeDate(taskTopVo.getFinishTime());
                reimbursementVoucher.setVoucherType(0);
                reimbursementVoucher.setSpecialInvoice(specialInvoice);
                // 生成凭证编号
                int number = 0;
                DateTime beginOfMonth = DateUtil.beginOfMonth(taskTopVo.getFinishTime());
                DateTime endOfMonth = DateUtil.endOfMonth(taskTopVo.getFinishTime());
                ReimbursementVoucher latestVoucher = reimbursementVoucherService.getLatestByMakeDate(beginOfMonth, endOfMonth);
                if (Objects.nonNull(latestVoucher)) {
                    number = new BigDecimal(latestVoucher.getVoucherNumber()).intValue();
                }
                reimbursementVoucher.setVoucherNumber(generateVoucherNumber(number));

                if(getOtherSubject) {
                    subject = subjectService.selectBySubjectName(tableData.getType());
                }

                if (Objects.isNull(subject)) {
                    log.info("查不到科目数据,设置默认值为其他，subjectName:{}, 业务编号:{}", tableData.getType(), businessId);
                    subject = new Subject();
                    subject.setSubjectCode("9999");
                }
                if(ADMINISTRATION_FEE.contains(subject.getSubjectCode())) {
                    administrationFeeExist = true;
                }
                reimbursementVoucher.setSummary(tableData.getRemark());
                reimbursementVoucher.setSubjectCode(subject.getSubjectCode());
                reimbursementVoucher.setCurrency(0);
                reimbursementVoucher.setBorrowDirection(0);
                reimbursementVoucher.setDomesticCurrency(new BigDecimal(tableData.getAmount()));
                SysDept sysDept = sysDeptService.selectDeptByDingTalkDeptId(deptId);
                if (Objects.isNull(sysDept)) {
                    log.info("查不到部门数据,deptId:{}, 业务编号:{}", deptId, businessId);
                    continue;
                }
                reimbursementVoucher.setDeptCode(sysDept.getDeptCode());
                if (TenantContextHolder.getTenantId() == 0) {
                    if (Lists.newArrayList("54011101", "54011102", "54011103").contains(subject.getSubjectCode())) {
                        reimbursementVoucher.setItemCode("01002");
                    }
                } else {
                    if (Lists.newArrayList("5602170401", "5602170402", "5602170403", "54011101", "54011102", "54011103").contains(subject.getSubjectCode())) {
                        reimbursementVoucher.setItemCode("01002");
                    }
                }
                reimbursementVoucher.setTenantId(TenantContextHolder.getTenantId());
                reimbursementVoucher.setCreateTime(new Date());
                reimbursementVoucher.setCompanyCode(companyCode);
                reimbursementVoucher.setCompanyName(companyName);
                // 数据量不大,先入库,上面查询会用到,性能影响较小
                reimbursementVoucherService.save(reimbursementVoucher);
            }

            //上传发票
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> fileComponentValueVoOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> TableFieldId.DD_ATTACHMENT.equals(formComponentValueVo.getComponentType()) && StrUtil.contains(formComponentValueVo.getName(), "发票附件")).findFirst();
            if (fileComponentValueVoOptional.isPresent()) {
                // 校验附件
                VerifyResult<List<String>> verifyAttachmentAndUploadResult = verifyAttachmentAndUpload(processInstanceId, fileComponentValueVoOptional.get(), businessId);
                List<String> images = verifyAttachmentAndUploadResult.getData();
                if (CollUtil.isNotEmpty(images)) {
                    // 校验发票并保存
                    verifyInvoice(processInstanceId, processInstance.getBusinessId(), images);
                }
            }
            //获取上传图片
            Optional<OapiProcessinstanceGetResponse.FormComponentValueVo> photoOptional = formComponentValues.stream()
                    .filter(formComponentValueVo -> TableFieldId.DD_PHOTO_FIELD.equals(formComponentValueVo.getComponentType()) && StrUtil.contains(formComponentValueVo.getName(), "图片")).findFirst();
            if(photoOptional.isPresent() && StringUtil.isNotBlank(photoOptional.get().getValue())) {

                List<String> images = JSON.parseArray(photoOptional.get().getValue(), String.class);
                if (CollUtil.isNotEmpty(images)) {
                    // 校验发票并保存
                    verifyInvoice(processInstanceId, processInstance.getBusinessId(), images);
                }
            }

            //保存审批处理记录（发票情况为付款后补发票和行政费用不需要记录）
            // 获取表格发票情况信息域
            if(isPayAffterInvoice || administrationFeeExist) {
                log.info("发票情况为付款后补发票和行政费用不需要记录, 审批单号:{}", businessId);
                continue;
            }
            saveProcessRecord(processInstance, processInstanceId);
        }
    }

    private String generateVoucherNumber(int initValue) {
        return StrUtil.fillBefore(String.valueOf(initValue + 1), VOUCHER_NUMBER_FILL_CHAR, VOUCHER_NUMBER_LENGTH);
    }

    /**
     * 获取表格数据
     *
     * @param processInstanceId
     * @param tableFiled
     * @return
     */
    private List<PayApplyTableData> getTableData(String processInstanceId, OapiProcessinstanceGetResponse.FormComponentValueVo tableFiled, String businessId) {
        // 获取表格报销日期范围
        String tableFieldValue = tableFiled.getValue();
        List<TableField> data = JSON.parseObject(tableFieldValue, new TypeReference<List<TableField>>() {
        });
        List<PayApplyTableData> tableDataList = new ArrayList<>();
        for (TableField datum : data) {
            List<TableField.RowValue> forms = datum.getRowValue();
            PayApplyTableData tableData = new PayApplyTableData();
            tableDataList.add(tableData);

            Optional<TableField.RowValue> remarkOpt = forms.stream().filter(form -> REMARK.equals(form.getLabel())).findFirst();
            remarkOpt.ifPresent(rowValue -> tableData.setRemark(rowValue.getValue()));

            Optional<TableField.RowValue> feeTypeOpt = forms.stream().filter(form -> FEE_TYPE.equals(form.getLabel())).findFirst();
            if (!feeTypeOpt.isPresent()) {
                log.info("未获取到表格中的费用类别数据,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                return Collections.emptyList();
            }
            // 费用类别
            feeTypeOpt.ifPresent(rowValue -> tableData.setType(rowValue.getValue()));

            Optional<TableField.RowValue> moneyOpt = forms.stream().filter(form -> AMOUNT.equals(form.getLabel())).findFirst();
            if (!moneyOpt.isPresent()) {
                log.info("未获取到表格中的金额数据,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                return Collections.emptyList();
            }
            // 金额
            moneyOpt.ifPresent(rowValue -> tableData.setAmount(rowValue.getValue()));
        }
        return tableDataList;
    }

    /**
     * 出纳审批人
     *
     * @return
     */
    private List<String> CashierApprover() {
        List<String> users = sysDictTypeService.selectDictDataByType(DictType.CASHIER_APPROVER.getType())
                .stream()
                .map(SysDictData::getDictValue)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(users)) {
            return users;
        }
        return Lists.newArrayList("090945395232008288", "1735650738133073", "181463073032532842", "0515683219689916");
    }
    /**
     * 保存流程处理记录
     *
     * @param processInstance
     * @param processInstanceId
     */
    private boolean saveProcessRecord(OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance,
                                      String processInstanceId) {
        ProcessRecord processRecord = BeanUtil.copyProperties(processInstance, ProcessRecord.class);
        processRecord.setProcessInstanceId(processInstanceId);
        OapiV2UserGetResponse.UserGetResponse user = dingTalkOApiClient.getUser(processInstance.getOriginatorUserid());
        processRecord.setOriginatorUserId(processInstance.getOriginatorUserid());
        processRecord.setOriginatorUserName(Objects.nonNull(user) ? user.getName() : StringPool.EMPTY);
        processRecord.setProcessType(ProcessType.PAY_APPLY.getValue());
        processRecord.setTenantId(TenantContextHolder.getTenantId());
        return processRecordService.save(processRecord);
    }

    /**
     * 校验发票附件
     *
     * @param ddAttachment
     * @return
     */
    private VerifyResult<List<String>> verifyAttachmentAndUpload(String processInstanceId, OapiProcessinstanceGetResponse.FormComponentValueVo ddAttachment, String businessId) {
        String ddAttachmentValue = ddAttachment.getValue();
        List<String> urls = new ArrayList<>();
        VerifyResult<List<String>> result = new VerifyResult<>();
        result.setData(urls);
        if("null".equals(ddAttachmentValue) || StringUtil.isEmpty(ddAttachmentValue)) {
            return result;
        }
        List<Attachment> attachments = JSON.parseArray(ddAttachmentValue, Attachment.class);
        for (Attachment attachment : attachments) {
            // 文件格式校验
            if (!FILE_TYPE_ZIP.equals(attachment.getFileType())) {
                log.info("发票附件压缩格式有误,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                break;
            }
            OapiProcessinstanceFileUrlGetResponse.AppSpaceResponse processFile = dingTalkOApiClient.getProcessFile(processInstanceId, attachment.getFileId());
            File downLoadFile = null;
            File folder = null;
            try {
                String fileName = IdUtil.fastSimpleUUID() + StringPool.UNDERSCORE + attachment.getFileName();
                downLoadFile = FileUtils.downloadFileFromUrl(processFile.getDownloadUri(), fileName);
                String suffix = FileUtil.getSuffix(fileName);
                if (!FILE_TYPE_ZIP.equals(suffix)) {
                    log.info("发票附件压缩格式有误,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                    break;
                }
                // 解压缩
                List<File> images;
                try {
                    folder = ZipUtil.unzip(downLoadFile, CharsetUtil.CHARSET_GBK);
                    images = FileUtil.loopFiles(folder);
                } catch (UtilException | IORuntimeException e) {
                    log.error("文件解压异常,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId, e);
                    result.setResult("文件解压异常,请重新压缩上传文件");
                    break;
                }
                if (CollUtil.isEmpty(images)) {
                    log.info("文件夹为空,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId);
                    result.setResult("未获取到发票文件，请检查压缩包文件存放格式是否有误");
                    break;
                }
                // 处理pdf类型的文件
                List<File> pdfFiles = images.stream().filter(file -> Objects.equals(MimeTypeUtils.PDF, FileTypeUtil.getType(file)))
                        .collect(Collectors.toList());
                images.removeAll(pdfFiles);
                List<File> disposedPdfFiles = pdfFiles.stream().map(file -> FileUtils.pdfToImage(file)).collect(Collectors.toList());
                images.addAll(disposedPdfFiles);

                // 处理ofd类型的文件
                List<File> ofdFiles = images.stream().filter(file -> Objects.equals(MimeTypeUtils.OFD, FileTypeUtil.getType(file)))
                        .collect(Collectors.toList());
                images.removeAll(ofdFiles);
                List<File> disposedOfdFiles = ofdFiles.stream().map(file -> FileUtils.ofdToImage(file)).collect(Collectors.toList());
                images.addAll(disposedOfdFiles);

                // 上传阿里云
                for (File file : images) {
                    String fileType = FileTypeUtil.getType(file);
                    if (Arrays.asList(MimeTypeUtils.IMAGE_EXTENSION).contains(fileType)) {
                        OssResult ossResult = aliOssTemplate.putFile(Constants.FILE_TYPE, file.getName(), file);
                        urls.add(ossResult.getAccessLink());
                    }
                }
            } catch (Exception e) {
                log.error("发票附件处理异常,审批实例ID:{}, 审批单号:{}", processInstanceId, businessId, e);
                result.setResult("发票附件处理异常,请重新压缩提交");
                break;
            } finally {
                FileUtil.del(downLoadFile);
                FileUtil.del(folder);
            }
        }
        return result;
    }

    /**
     * 校验发票
     * @param processInstanceId
     * @param businessId
     * @param images
     * @return
     */
    private VerifyResult<BoolFlagEnum> verifyInvoice(String processInstanceId, String businessId, List<String> images) {
        List<Invoice> invoices = new ArrayList<>();
        for (String image : images) {
            String filename = image.substring(image.lastIndexOf("/") + 1);
            OapiOcrStructuredRecognizeResponse.OcrStructuredResult result = dingTalkOApiClient.ocr(image, "invoice");
            if (Objects.isNull(result)) {
                log.info("发票识别失败, 实例id:{},审批单号:{}", processInstanceId, businessId);
            } else {
                Invoice invoice = JSON.parseObject(result.getData(), Invoice.class);
                invoice.setImageUrl(image);
                invoice.setFilename(filename);

                // 校验发票代码是否已用
                Boolean checkedRepeatInvoice = invoiceOcrRecordService.checkRepeatInvoice(invoice.getInvoiceCode(), invoice.getInvoiceNumber(), invoice.getSheet());
                if (checkedRepeatInvoice) {
                    log.info("已使用过的发票不可重复使用,发票号码:{},文件名:{}, 实例id:{},审批单号:{}", invoice.getInvoiceNumber(), invoice.getFilename(), processInstanceId, businessId);
                    break;
                }

                invoices.add(invoice);
            }
        }

        // 当用户提交的是同一张发票的图片和pdf时，需要进行去重处理
        invoices = invoices.stream().filter(distinctByKey(Invoice::getInvoiceCode)).collect(Collectors.toList());

        if(!CollectionUtil.isEmpty(invoices)) {
            // 保存发票记录
            saveInvoices(invoices, processInstanceId, businessId);
        }
        VerifyResult<BoolFlagEnum> result = new VerifyResult<>();
        result.setData(BoolFlagEnum.YES);
        return result;
    }

    /**
     * 根据发票代码去除重复发票
     * @param keyExtractor
     * @param <T>
     * @return
     */
    private <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor){
        ConcurrentHashMap<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 持久化发票记录
     *
     * @param invoices
     */
    private void saveInvoices(List<Invoice> invoices, String processInstanceId, String businessId) {
        if (!CollectionUtil.isEmpty(invoices)) {
            List<InvoiceOcrRecord> invoiceOcrRecordList = new ArrayList<>();
            invoices.forEach(invoice -> {
                InvoiceOcrRecord invoiceOcrRecord = new InvoiceOcrRecord();
                BeanUtils.copyProperties(invoice, invoiceOcrRecord);
                invoiceOcrRecord.setProcessInstanceId(processInstanceId);
                invoiceOcrRecord.setBusinessId(businessId);
                invoiceOcrRecord.setInvoiceDate(invoice.getParseInvoiceDate());
                invoiceOcrRecord.setUseState(UseState.TO_BE_CONFIRMED.getState());
                invoiceOcrRecord.setTenantId(TenantContextHolder.getTenantId());
                invoiceOcrRecord.setUseState(UseState.CONSUMED.getState());
                invoiceOcrRecordList.add(invoiceOcrRecord);
            });
            invoiceOcrRecordService.saveBatch(invoiceOcrRecordList);
        }
    }
}
