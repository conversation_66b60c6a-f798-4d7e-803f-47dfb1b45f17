package com.qmqb.oa.common.utils.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/7/20
 */
@Configuration
public class FinanceHttpTemplate {
    /**
     *定制的 RestTemplates需要使用
     * @return
     */
    @Bean("restTemplate")
    //@LoadBalanced
//    @ConditionalOnMissingBean(PubRestTemplate.class)
    //@SentinelRestTemplate(blockHandler = "blockHandle", blockHandlerClass = BlockExceptionUtil.class,fallback = "fallbackHandle",fallbackClass = BlockExceptionUtil.class)
    public RestTemplate pubRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        return restTemplate;
    }
}
