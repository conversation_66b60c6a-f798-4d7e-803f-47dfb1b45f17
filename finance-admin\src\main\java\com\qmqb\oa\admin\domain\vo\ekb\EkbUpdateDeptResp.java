package com.qmqb.oa.admin.domain.vo.ekb;

import lombok.Data;

/**
 * 易快报修改部门信息响应体
 * <AUTHOR>
 * @date 2024/7/29
 */
@Data
public class EkbUpdateDeptResp {

    /**
     * 易快报修改部门信息响应体
     */
    private EkbUpdateDept value;

    @Data
    public static class EkbUpdateDept {
        /**
         * 部门ID
         */
        private String id;
        /**
         * 部门名称
         */
        private String name;
        /**
         * 部门编码
         */
        private String code;
        /**
         * 上级部门ID
         */
        private String parentId;
        /**
         * 是否停用
         */
        private Boolean active;
        /**
         *部门关联法人实体ID和成本中心ID以及自定义字段
         */
        private String form;
        /**
         *排序序号
         */
        private Integer order;
    }
    
}
