package com.qmqb.oa.admin.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 发送钉钉通知消息接口请求实体
 * <AUTHOR>
 * @date 2024/10/12
 */
@Data
@Accessors(chain = true)
@Builder
public class DingSendNotifyMsgDTO {

    /**
     * 易快报userId
     */
    private String ekbUserId;

    /**
     * 易快报单据id
     */
    private String ekbFlowId;

    /**
     * 易快报消息id
     */
    private String ekbMessageId;
    /**
     * 钉钉用户的userId
     */
    private String userId;
    /**
     * 主体公司
     */
    private Integer companyId;
    /**
     * 消息跳转链接
     */
    private String msgUrl;
    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String text;

    /**
     * 消息通知图标id,通过钉钉接口上传获取
     */
    private String iconImgId;
}
