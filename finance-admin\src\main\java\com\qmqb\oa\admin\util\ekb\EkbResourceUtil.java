package com.qmqb.oa.admin.util.ekb;

import com.hzed.structure.common.util.AssertUtil;
import com.qmqb.oa.admin.domain.dto.EkbInvoiceTypeDTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description ekb 资源加载工具类
 * @Date 2024\11\6 0006 15:36
 * @Version 1.0
 */
public class EkbResourceUtil {
    /**
     * 存储所有的ekb invocie 票据类型,key 未票据类型id
     */
    public static Map<String, EkbInvoiceTypeDTO> ekbInvoiceTypeDTOMap = new HashMap<>(300);

    public static void initAllInvoiceTypes(List<EkbInvoiceTypeDTO> ekbInvoiceTypeDTOS){
        ekbInvoiceTypeDTOMap = ekbInvoiceTypeDTOS.stream().collect(Collectors.toMap(EkbInvoiceTypeDTO::getId, Function.identity()));
    }

    public static void invoiceTypesAdd(EkbInvoiceTypeDTO ekbInvoiceTypeDTO) {
        AssertUtil.notNull(ekbInvoiceTypeDTO, "添加的票据数据不能为空");
        ekbInvoiceTypeDTOMap.put(ekbInvoiceTypeDTO.getId(), ekbInvoiceTypeDTO);
    }
}
