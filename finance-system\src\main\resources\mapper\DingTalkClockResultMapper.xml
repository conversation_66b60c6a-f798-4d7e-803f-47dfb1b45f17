<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.oa.system.mapper.DingTalkClockResultMapper">
    <resultMap type="com.qmqb.oa.system.domain.DingTalkClockResult" id="DingtalkClockResultResult">
        <result property="id" column="id"/>
        <result property="sourceType" column="source_type"/>
        <result property="baseCheckTime" column="base_check_time"/>
        <result property="userCheckTime" column="user_check_time"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="locationResult" column="location_result"/>
        <result property="timeResult" column="time_result"/>
        <result property="checkType" column="check_type"/>
        <result property="userId" column="user_id"/>
        <result property="workDate" column="work_date"/>
        <result property="userName" column="user_name"/>
        <result property="recordId" column="record_id"/>
        <result property="planId" column="plan_id"/>
        <result property="groupId" column="group_id"/>
        <result property="isAfter20" column="is_after_20"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectDingtalkClockResultVo">
        select id,
               source_type,
               base_check_time,
               user_check_time,
               proc_inst_id,
               location_result,
               time_result,
               check_type,
               user_id,
               work_date,
               user_name,
               record_id,
               plan_id,
               group_id,
               is_after_20,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               is_deleted,
               tenant_id
        from t_dingtalk_clock_result
    </sql>

    <select id="selectDingtalkClockResultList" parameterType="com.qmqb.oa.system.domain.DingTalkClockResult"
            resultMap="DingtalkClockResultResult">
        <include refid="selectDingtalkClockResultVo"/>
        <where>
            <if test="baseCheckTime != null ">
                and base_check_time = #{baseCheckTime}
            </if>
            <if test="userCheckTime != null ">
                and user_check_time = #{userCheckTime}
            </if>
            <if test="procInstId != null  and procInstId != ''">
                and proc_inst_id = #{procInstId}
            </if>
            <if test="locationResult != null  and locationResult != ''">
                and location_result = #{locationResult}
            </if>
            <if test="timeResult != null  and timeResult != ''">
                and time_result = #{timeResult}
            </if>
            <if test="checkType != null  and checkType != ''">
                and check_type = #{checkType}
            </if>
            <if test="userId != null  and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="workDate != null ">
                and work_date = #{workDate}
            </if>
            <if test="userName != null  and userName != ''">
                and user_name like concat('%', #{userName}, '%')
            </if>
            <if test="isAfter20 != null ">
                and is_after_20 = #{isAfter20}
            </if>
        </where>
    </select>

    <select id="selectDingtalkClockResultById" parameterType="Long"
            resultMap="DingtalkClockResultResult">
        <include refid="selectDingtalkClockResultVo"/>
        where id = #{id}
    </select>

    <insert id="insertDingtalkClockResult" parameterType="com.qmqb.oa.system.domain.DingTalkClockResult"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into t_dingtalk_clock_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="baseCheckTime != null">
                base_check_time,
            </if>
            <if test="userCheckTime != null">
                user_check_time,
            </if>
            <if test="procInstId != null">
                proc_inst_id,
            </if>
            <if test="locationResult != null">
                location_result,
            </if>
            <if test="timeResult != null">
                time_result,
            </if>
            <if test="checkType != null">
                check_type,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="workDate != null">
                work_date,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="recordId != null">
                record_id,
            </if>
            <if test="planId != null">
                plan_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="isAfter20 != null">
                is_after_20,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sourceType != null">
                #{sourceType},
            </if>
            <if test="baseCheckTime != null">
                #{baseCheckTime},
            </if>
            <if test="userCheckTime != null">
                #{userCheckTime},
            </if>
            <if test="procInstId != null">
                #{procInstId},
            </if>
            <if test="locationResult != null">
                #{locationResult},
            </if>
            <if test="timeResult != null">
                #{timeResult},
            </if>
            <if test="checkType != null">
                #{checkType},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="workDate != null">
                #{workDate},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="recordId != null">
                #{recordId},
            </if>
            <if test="planId != null">
                #{planId},
            </if>
            <if test="groupId != null">
                #{groupId},
            </if>
            <if test="isAfter20 != null">
                #{isAfter20},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <update id="updateDingtalkClockResult" parameterType="com.qmqb.oa.system.domain.DingTalkClockResult">
        update t_dingtalk_clock_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceType != null">
                source_type =
                #{sourceType},
            </if>
            <if test="baseCheckTime != null">
                base_check_time =
                #{baseCheckTime},
            </if>
            <if test="userCheckTime != null">
                user_check_time =
                #{userCheckTime},
            </if>
            <if test="procInstId != null">
                proc_inst_id =
                #{procInstId},
            </if>
            <if test="locationResult != null">
                location_result =
                #{locationResult},
            </if>
            <if test="timeResult != null">
                time_result =
                #{timeResult},
            </if>
            <if test="checkType != null">
                check_type =
                #{checkType},
            </if>
            <if test="userId != null and userId != ''">
                user_id =
                #{userId},
            </if>
            <if test="workDate != null">
                work_date =
                #{workDate},
            </if>
            <if test="userName != null">
                user_name =
                #{userName},
            </if>
            <if test="recordId != null">
                record_id =
                #{recordId},
            </if>
            <if test="planId != null">
                plan_id =
                #{planId},
            </if>
            <if test="groupId != null">
                group_id =
                #{groupId},
            </if>
            <if test="isAfter20 != null">
                is_after_20 =
                #{isAfter20},
            </if>
            <if test="remark != null">
                remark =
                #{remark},
            </if>
            <if test="createBy != null">
                create_by =
                #{createBy},
            </if>
            <if test="createTime != null">
                create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">
                update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time =
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted =
                #{isDeleted},
            </if>
            <if test="tenantId != null">
                tenant_id =
                #{tenantId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDingtalkClockResultById" parameterType="Long">
        delete
        from t_dingtalk_clock_result
        where id = #{id}
    </delete>

    <delete id="deleteDingtalkClockResultByIds" parameterType="String">
        delete from t_dingtalk_clock_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="statistics" resultType="com.qmqb.oa.system.domain.vo.ClockStatisticsVo">
        SELECT
            user_name AS userName,
            user_id AS userId,
            tenant_id AS tenantId,
            COUNT(user_id) AS count
        FROM
            `t_dingtalk_clock_result`
        WHERE
            check_type = 'OffDuty'
          AND is_after_20 = 1
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userName != null and userName != ''">
            AND user_name = #{userName}
        </if>
        <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
            AND work_date BETWEEN #{beginTime} AND #{endTime}
        </if>
        GROUP BY user_id;
    </select>
</mapper>
