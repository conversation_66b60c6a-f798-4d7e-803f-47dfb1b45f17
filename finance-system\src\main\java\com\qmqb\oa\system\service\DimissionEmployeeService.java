package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.DimissionEmployee;

import java.util.List;

/**
 * 离职员工Service接口
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
public interface DimissionEmployeeService extends IService<DimissionEmployee> {
    /**
     * 查询离职员工
     *
     * @param id 离职员工ID
     * @return 离职员工
     */
    DimissionEmployee selectDimissionEmployeeById(Long id);

    /**
     * 查询离职员工列表
     *
     * @param dimissionEmployee 离职员工
     * @return 离职员工集合
     */
    List<DimissionEmployee> selectDimissionEmployeeList(DimissionEmployee dimissionEmployee);

    /**
     * 新增离职员工
     *
     * @param dimissionEmployee 离职员工
     * @return 结果
     */
    int insertDimissionEmployee(DimissionEmployee dimissionEmployee);

    /**
     * 修改离职员工
     *
     * @param dimissionEmployee 离职员工
     * @return 结果
     */
    int updateDimissionEmployee(DimissionEmployee dimissionEmployee);

    /**
     * 批量删除离职员工
     *
     * @param ids 需要删除的离职员工ID
     * @return 结果
     */
    int deleteDimissionEmployeeByIds(Long[] ids);

    /**
     * 删除离职员工信息
     *
     * @param id 离职员工ID
     * @return 结果
     */
    int deleteDimissionEmployeeById(Long id);

    /**
     * 导入离职员工数据
     *
     * @param dimissionEmployeeList 离职员工数据列表
     * @param isUpdateSupport       是否更新支持，如果已存在，则进行更新数据
     * @param operName              操作用户
     * @return 结果
     */
    String importData(List<DimissionEmployee> dimissionEmployeeList, boolean isUpdateSupport, String operName);

    /**
     * 更加姓名统计行数
     *
     * @param name
     * @return
     */
    int countByName(String name);
}
