package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qmqb.oa.common.enums.UseState;
import com.qmqb.oa.system.domain.InvoiceOcrRecord;
import com.qmqb.oa.system.mapper.InvoiceOcrRecordMapper;
import com.qmqb.oa.system.service.InvoiceOcrRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 发票OCR识别记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
public class InvoiceOcrRecordServiceImpl extends ServiceImpl<InvoiceOcrRecordMapper, InvoiceOcrRecord> implements InvoiceOcrRecordService {
    @Autowired
    private InvoiceOcrRecordMapper invoiceOcrRecordMapper;

    @Override
    public Boolean checkRepeatInvoice(String invoiceCode, String invoiceNumber, String sheet) {
        QueryWrapper<InvoiceOcrRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("invoice_code", invoiceCode);
        wrapper.eq("invoice_number", invoiceNumber);
        wrapper.eq("sheet", sheet);
        wrapper.eq("use_state", UseState.CONSUMED.getState());
        Integer count = invoiceOcrRecordMapper.selectCount(wrapper);
        return SqlHelper.retBool(count);
    }

    /**
     * 根据审批实例ID查询发票OCR记录
     *
     * @param processInstanceId 审批实例ID
     * @return
     */
    @Override
    public List<InvoiceOcrRecord> listByProcessInstanceId(String processInstanceId) {
        QueryWrapper<InvoiceOcrRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("process_instance_id", processInstanceId);
        return invoiceOcrRecordMapper.selectList(wrapper);
    }

    /**
     * 查询发票识别记录列表
     *
     * @param invoiceOcrRecord 发票识别记录
     * @return 发票识别记录
     */
    @Override
    public List<InvoiceOcrRecord> selectInvoiceOcrRecordList(InvoiceOcrRecord invoiceOcrRecord) {
        return invoiceOcrRecordMapper.selectInvoiceOcrRecordList(invoiceOcrRecord);
    }

    @Override
    public InvoiceOcrRecord getOneByProcessInstanceIdAndAmount(String processInstanceId, String amount) {
        QueryWrapper<InvoiceOcrRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("process_instance_id", processInstanceId);
        wrapper.eq("invoice_amount", amount);
        return getOne(wrapper);
    }
}
