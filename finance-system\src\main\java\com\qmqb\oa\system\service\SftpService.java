package com.qmqb.oa.system.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * SFTP服务接口
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface SftpService {
    /**
     * 从SFTP下载文件
     *
     * @param filePath SFTP上的文件路径
     * @param fileName 下载后的文件名称
     * @param request  HTTP请求
     * @param response HTTP响应
     */
    void downloadFile(String filePath, String fileName, HttpServletRequest request, HttpServletResponse response);

    /**
     * 从SFTP下载文件夹
     *
     * @param folderPath  SFTP上的文件夹路径
     * @param zipFileName 下载后的压缩包名称
     * @param request     HTTP请求
     * @param response    HTTP响应
     */
    void downloadFolder(String folderPath, String zipFileName, HttpServletRequest request, HttpServletResponse response);

    /**
     * 从SFTP下载还款明细文件夹
     *
     * @param batchNos        对账明细主键id列表
     * @param request    HTTP请求
     * @param response   HTTP响应
     */
    void downloadRepayDetailFolder(List<String> batchNos, HttpServletRequest request, HttpServletResponse response);
}