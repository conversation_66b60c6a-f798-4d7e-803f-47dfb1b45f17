package com.qmqb.oa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.qmqb.oa.system.domain.vo.RoleUserInfoVo;
import com.qmqb.oa.system.mapper.EkbDingUserMappingMapper;
import com.qmqb.oa.system.service.EkbDingUserMappingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 钉钉与易快报员工映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class EkbDingUserMappingServiceImpl extends ServiceImpl<EkbDingUserMappingMapper, EkbDingUserMapping> implements EkbDingUserMappingService {
    @Resource
    private EkbDingUserMappingMapper ekbDingUserMappingMapper;

    @Override
    public List<RoleUserInfoVo> selectUserIdAndCompanyDeptId() {
        return this.baseMapper.selectUserIdAndCompanyDeptId();
    }

    @Override
    public EkbDingUserMapping findOneByEkbUserId(String ekbUserId) {
        return ekbDingUserMappingMapper.findOneByEkbUserId(ekbUserId);
    }

    @Override
    public List<EkbDingUserMapping> findListByEkbUserIds(List<String> ekbUserIdList) {
        LambdaQueryWrapper<EkbDingUserMapping> queryWrapper = Wrappers.lambdaQuery(EkbDingUserMapping.class)
                .in(EkbDingUserMapping::getEkbUserId, ekbUserIdList);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public EkbDingUserMapping findOneByDingUserId(String dingUserId) {
        LambdaQueryWrapper<EkbDingUserMapping> queryWrapper = Wrappers.lambdaQuery(EkbDingUserMapping.class)
                .eq(EkbDingUserMapping::getDingUserId, dingUserId);
        return CollectionUtils.isEmpty(list(queryWrapper)) ? null : list(queryWrapper).get(0);
    }
}
