package com.qmqb.oa.system.service;

import com.qmqb.oa.system.domain.RepayStat;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface RepayStatService extends IService<RepayStat> {
    /**
     * 获取同一批次号信息
     * @param batchNo
     * @return
     */
    List<RepayStat> listExportBy(String batchNo);
}
