package com.qmqb.oa.admin.service;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.qmqb.oa.BaseTest;
import com.qmqb.oa.admin.domain.vo.RepayStatManualExecuteVo;
import com.qmqb.oa.admin.service.hsfk.DingTalkDeptService;
import com.qmqb.oa.common.constant.RepaymentGenConstant;
import com.qmqb.oa.system.domain.DingDept;
import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.qmqb.oa.system.service.DingDeptService;
import com.qmqb.oa.system.service.EkbDingDeptMappingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import shade.com.alibaba.fastjson2.JSONArray;
import shade.com.alibaba.fastjson2.JSONObject;

import java.util.List;


@Slf4j
public class RepayTest extends BaseTest {
    @Autowired
    private RepayStatisticService repayStatisticService;
    @Autowired
    private DingDeptService dingDeptService;
    @Autowired
    private EkbDingDeptMappingService ekbDingDeptMappingService;
    @Autowired
    private DingTalkDeptService dingTalkDeptService;


    @Test
    public void insertDepts(){
        int companyId = 0;
        List<DingDept> dingDeptList = Lists.newArrayList();
        DingDept dingDept1 = new DingDept();
        dingDept1.setDeptId(944756050L);
        dingDept1.setParentId(369530823L);
        dingDept1.setDeptName("400客服");
        dingDept1.setDeptLevel(4);
        dingDept1.setDeptCode("QM061501");
        dingDept1.setDeptChainName("广州市全民钱包科技有限公司-资产管理中心-400客服部-400客服");
        dingDept1.setCompanyId(companyId);
        dingDeptService.save(dingDept1);

        EkbDingDeptMapping mapping = new EkbDingDeptMapping();
        mapping.setDeptCode(dingDept1.getDeptCode());
        mapping.setDingDeptId(dingDept1.getDeptId());
        mapping.setDeptName(dingDept1.getDeptName());
        mapping.setDingDeptLevel(dingDept1.getDeptLevel());
        mapping.setDingParentId(dingDept1.getParentId());
        mapping.setCompanyId(companyId);
        ekbDingDeptMappingService.save(mapping);
    }
    @Test
    public void createDept() throws Exception{
        int companyId = 0;
        JSONArray deptIdArray = new JSONArray();
        deptIdArray.add(939978266L);
        JSONObject bizData = new JSONObject();
        bizData.put("deptId",deptIdArray);
        dingTalkDeptService.createDept4DingAndEkb(bizData, companyId);
    }




    @Test
    public void trigger() {
        RepayStatManualExecuteVo vo = new RepayStatManualExecuteVo();
        vo.setEndDate("2021-08-02");
        vo.setStartDate("2021-08-02");
        Integer method = RepaymentGenConstant.MANUAL_GEN;
        boolean result = repayStatisticService.triggerStat(vo, method);
        Assert.isTrue(result);

//        RepayGenRecordVo genVo = new RepayGenRecordVo();
//        genVo.setStartDate("2020-01-01");
//        genVo.setEndDate("2022-01-01");
//        genVo.setBatchNo("123123");
//
//        log.info(JSON.toJSONString(repayStatisticService.listRecord(genVo)));
//        List<RepayGenRecord> repayStats = repayStatisticService.listRecord(null);
//        AjaxResult result1 = repayStatisticService.detailExport(repayStats.get(4).getId());
//        log.info(JSON.toJSONString(result1));
    }
}
