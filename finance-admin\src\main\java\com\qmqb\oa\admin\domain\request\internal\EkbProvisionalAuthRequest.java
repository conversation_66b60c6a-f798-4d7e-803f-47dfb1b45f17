package com.qmqb.oa.admin.domain.request.internal;


import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Description 访问临时授权请求实体
 * @Date 2024\10\9 0009 19:50
 * @Version 1.0
 */
@Data
public class EkbProvisionalAuthRequest {
    @NotBlank(message = "公司corpId不能为空")
    private String corpId;
    @NotBlank(message = "ekbUserId 不能为空")
    private String ekbUserId;
    @Pattern(regexp = "^1|2$", message = "client：1-app;2-PC")
    @NotNull(message = "client 只能为1|2 (client：1-app;2-PC)")
    private Integer client;
    @NotBlank(message = "流程id 不能为空")
    private String flowId;

    /**
     * 页面类型
     */
    private String pageType;
    /**
     * 流程id
     */
}
