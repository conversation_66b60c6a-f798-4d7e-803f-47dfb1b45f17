package com.qmqb.oa.admin.service.hsfk;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiUserListidRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiUserListidResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.collect.Lists;
import com.hzed.structure.lock.annotation.Lock;
import com.qmqb.oa.admin.domain.vo.ekb.EkbAddUserReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchAddUserListReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbBatchUpdateUserListReq;
import com.qmqb.oa.admin.domain.vo.ekb.EkbUpdateUserReq;
import com.qmqb.oa.common.enums.EkbEventType;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.DingUser;
import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.qmqb.oa.system.service.*;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import shade.com.alibaba.fastjson2.JSON;
import shade.com.alibaba.fastjson2.JSONArray;
import shade.com.alibaba.fastjson2.JSONObject;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/7/27
 */
@Slf4j
@Component
public class DingTalkUserService {

    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private H5AppDingClientService h5AppDingClientService;
    @Autowired
    private DingUserService dingUserService;
    @Autowired
    private EkbDingUserMappingService ekbDingUserMappingService;
    @Autowired
    private EkbUserService ekbUserService;
    @Autowired
    private EkbDingDeptMappingService ekbDingDeptMappingService;
    @Autowired
    private SysEventService sysEventService;
    private final String USER_ID = "userId";

    /**
     * 新增员工
     *
     * @param bizData
     * @param companyId
     * @throws Exception
     */
    @Lock(keys = "lock.user", simpleKey = true, simplePrefix = "lock.user.org", waitTime = 10)
    public void addUser(JSONObject bizData, Integer companyId) throws Exception {
        // 获取accessToken
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, companyId));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        List<EkbAddUserReq> addUserList = new ArrayList<>();
        JSONArray userIdArray = getDingTalkUserId(bizData);
        List<EkbUpdateUserReq> updateUserList = new ArrayList<>();
        for (Object userId : userIdArray) {
            // 1、根据用户id调钉钉接口获取用户详情
            OapiV2UserGetResponse.UserGetResponse userDetail = getUserDetail(userId.toString(), accessToken);
            List<EkbDingUserMapping> ekbUserMappingList = ekbDingUserMappingService.list(Wrappers.lambdaQuery(EkbDingUserMapping.class).
                    eq(EkbDingUserMapping::getMobile, userDetail.getMobile()));
            // 2、不为空，调用易快报更新接口。用户表存在做更新，不存在做新增，映射表做更新
            if (CollectionUtils.isNotEmpty(ekbUserMappingList)) {
                List<DingUser> dingUserList = dingUserService.list(Wrappers.lambdaQuery(DingUser.class)
                        .eq(DingUser::getUserId, userId).eq(DingUser::getCompanyId, companyId));
                // 2.1存在用户，做修改处理，否做新增
                if (ObjectUtil.isNotEmpty(dingUserList)) {
                    log.info("用户存在，做更新处理，用户id：{}", userId.toString());
                    updateUserInfo(userId.toString(), userDetail, companyId);
                } else {
                    // 2.2保存员工信息至员工表，处理钉钉与易快报员工映射表
                    log.info("用户不存在，做新增处理，用户id：{}", userId.toString());
                    saveDingUser(userId.toString(), userDetail, companyId);
                }
                // 2.3更新钉钉与易快报员工映射表
                String dingDeptIdList = updateEkbDingUserMapping(userDetail, false);

                // 2.4组装易快报批量修改员工接口参数
                updateUserList = handleEkbUpdateUserReq(userDetail.getMobile(), dingDeptIdList, updateUserList, companyId);


            } else {
                // 3、保存员工信息至员工表，处理钉钉与易快报员工映射表
                saveDingUser(userId.toString(), userDetail, companyId);
                // 4、保存钉钉与易快报映射表，存在更新钉钉部门id为最新的
                String dingDeptIdString = saveEkbDingUserMapping(userId.toString(), userDetail);
                // 5、组装易快报批量新增员工接口参数
                EkbAddUserReq ekbAddUserReq = new EkbAddUserReq();
                ekbAddUserReq.setName(userDetail.getName());
                ekbAddUserReq.setCellphone(userDetail.getMobile());
                // 6、根据钉钉部门id列表挨个从钉钉与易快报部门映射表映射一一映射关系获取易快报部门id，组装列表请求
                List<String> ekbDeptIdList = getEkbDeptIdList(dingDeptIdString, companyId);
                ekbAddUserReq.setDefaultDepartment(ekbDeptIdList.get(0));
                ekbAddUserReq.setDepartments(ekbDeptIdList);
                addUserList.add(ekbAddUserReq);
            }

        }
        //新增员工结果
        boolean addUserResult = true;
        if (ObjectUtil.isNotEmpty(addUserList)) {
            // 调用易快报批量新增员工接口
            EkbBatchAddUserListReq ekbBatchAddUserReq = new EkbBatchAddUserListReq();
            ekbBatchAddUserReq.setStaffList(addUserList);
            try {
                ekbUserService.batchAddUser(ekbBatchAddUserReq);
            } catch (Exception e) {
                addUserResult = false;
                log.error("【易快报-批量新增员工】异常!", e);
                String errorMsg = this.getErrorMsg(e);
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.USER_BATCH_ADD.getEventType(), com.alibaba.fastjson.JSON.toJSONString(ekbBatchAddUserReq), null, null, errorMsg);
            }

        }
        //修改员工结果
        boolean updateUserResult = true;
        if (ObjectUtil.isNotEmpty(updateUserList)) {
            // 调用易快报批量修改员工接口
            EkbBatchUpdateUserListReq ekbBatchUpdateUserReq = new EkbBatchUpdateUserListReq();
            ekbBatchUpdateUserReq.setStaffList(updateUserList);
            try {
                ekbUserService.batchUpdateUser(ekbBatchUpdateUserReq);
            } catch (Exception e) {
                updateUserResult = false;
                log.error("【易快报-批量修改员工】异常!", e);
                String errorMsg = this.getErrorMsg(e);
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.USER_BATCH_MODIFY.getEventType(), com.alibaba.fastjson.JSON.toJSONString(ekbBatchUpdateUserReq), null, null, errorMsg);
            }
        }
        if (addUserResult && updateUserResult) {
            // 新增员工和修改员工都成功才能更新角色下员工信息
            try {
                ekbUserService.updateRoleUserInfo();
            } catch (Exception e) {
                log.error("【易快报-更新角色下员工信息】异常！", e);
                String errorMsg = com.qmqb.oa.common.utils.StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "";
                errorMsg = errorMsg.length() > 100 ? errorMsg.substring(0, 100) : errorMsg;
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.ROLE_USER_UPDATE.getEventType(), null, null, null, errorMsg);
            }

        }


    }

    /**
     * 获取钉钉用户id
     * 请求事件用户是userId，事件列表用户是userid，做兼容
     *
     * @param bizData
     * @return
     */
    private JSONArray getDingTalkUserId(JSONObject bizData) {
        if (ObjectUtil.isNotEmpty(bizData.getJSONArray(USER_ID))) {
            return bizData.getJSONArray("userId");
        } else {
            return bizData.getJSONArray("userid");
        }
    }

    /**
     * 保存钉钉与易快报员工映射表
     *
     * @param userId
     * @param userDetail
     */
    private String saveEkbDingUserMapping(String userId, OapiV2UserGetResponse.UserGetResponse userDetail) {
        String dingDeptIdString = handleDeptIdList(userDetail.getDeptIdList());
        // 钉钉与易快报员工映射表
        EkbDingUserMapping ekbDingUserMapping = new EkbDingUserMapping();
        ekbDingUserMapping.setDingUserId(userId);
        ekbDingUserMapping.setDingUserName(userDetail.getName());
        ekbDingUserMapping.setDingDeptIdList(dingDeptIdString);
        ekbDingUserMapping.setMobile(userDetail.getMobile());
        ekbDingUserMappingService.save(ekbDingUserMapping);

        return dingDeptIdString;
    }

    /***
     * 保存用户信息
     * @param userId
     * @param userDetail
     * @param companyId
     */
    private void saveDingUser(String userId, OapiV2UserGetResponse.UserGetResponse userDetail, Integer companyId) {
        // 处理部门id列表，以逗号分隔
        String deptIdString = handleDeptIdList(userDetail.getDeptIdList());
        // 保存钉钉员工表
        DingUser dingUser = new DingUser();
        dingUser.setUserId(userId);
        dingUser.setUnionId(userDetail.getUnionid());
        dingUser.setUserName(userDetail.getName());
        dingUser.setMobile(userDetail.getMobile());
        dingUser.setDeptIdList(deptIdString);
        dingUser.setCompanyId(companyId);
        dingUserService.save(dingUser);

    }

    /**
     * 根据用户id获取用户详情
     *
     * @param userId
     * @param accessToken
     * @throws ApiException
     */
    public OapiV2UserGetResponse.UserGetResponse getUserDetail(String userId, String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        OapiV2UserGetResponse resp = client.execute(req, accessToken);
        if (!resp.isSuccess()) {
            log.error("获取用户详情失败，用户id：{},响应code：{},响应msg:{}", userId, resp.getErrcode(), resp.getErrmsg());
        } else {
            log.info("获取用户详情成功，用户id：{},用户名：{},手机号:{},部门ID列表:{}", userId, resp.getResult().getName(),
                    resp.getResult().getMobile(), resp.getResult().getDeptIdList());
        }
        return resp.getResult();

    }

    public OapiUserListidResponse.ListUserByDeptResponse listDingUserByDeptId(Long deptId, String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/listid");
        OapiUserListidRequest req = new OapiUserListidRequest();
        req.setDeptId(deptId);
        OapiUserListidResponse rsp = client.execute(req, accessToken);
        if (!rsp.isSuccess()) {
            log.error("获取部门的用户列表失败，部门id：{},响应code：{},响应msg:{}", deptId, rsp.getErrcode(), rsp.getErrmsg());
            return null;
        } else {
            log.info("获取部门的用户列表成功，部门id：{},用户列表:{}", deptId, JSON.toJSONString(rsp.getResult()));
            return rsp.getResult();
        }
    }

    /**
     * 修改员工
     *
     * @param bizData
     * @param companyId
     */
    @Lock(keys = "lock.user", simpleKey = true, simplePrefix = "lock.user.org", waitTime = 10)
    public void modifyUser(JSONObject bizData, Integer companyId) throws Exception {
        ///1、获取accessToken
        DingAppConf dingAppConf = dingAppConfService.getOne(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getCompanyId, companyId));
        String accessToken = h5AppDingClientService.getAccessToken(dingAppConf.getDingAppKey(), dingAppConf.getDingAppSecret());
        JSONArray userIdArray = getDingTalkUserId(bizData);
        List<EkbUpdateUserReq> updateUserList = new ArrayList<>();
        // 2、挨个员工请求更新内部数据
        for (Object userId : userIdArray) {
            // 2.1、根据用户id调钉钉接口获取用户详情
            OapiV2UserGetResponse.UserGetResponse userDetail = getUserDetail(userId.toString(), accessToken);
            // 2.2、更新员工信息至员工表
            updateUserInfo(userId.toString(), userDetail, companyId);
            // 2.3、更新钉钉与易快报员工映射表
            String dingDeptIdList = updateEkbDingUserMapping(userDetail, false);
            // 2.4、组装易快报批量修改员工接口参数
            updateUserList = handleEkbUpdateUserReq(userDetail.getMobile(), dingDeptIdList, updateUserList, companyId);

        }
        // 3、调用易快报批量修改员工接口
        EkbBatchUpdateUserListReq ekbBatchUpdateUserReq = new EkbBatchUpdateUserListReq();
        ekbBatchUpdateUserReq.setStaffList(updateUserList);

        boolean updateUserResult = true;
        try {
            ekbUserService.batchUpdateUser(ekbBatchUpdateUserReq);
        } catch (Exception e) {
            updateUserResult = false;
            log.error("【易快报-批量修改员工】异常!", e);
            String errorMsg = this.getErrorMsg(e);
            // 保存事件表
            sysEventService.saveSysEvent(companyId, 2, EkbEventType.USER_BATCH_MODIFY.getEventType(), com.alibaba.fastjson.JSON.toJSONString(ekbBatchUpdateUserReq), null, null, errorMsg);
        }

        if (updateUserResult) {
            // 4、更新角色下员工信息
            try {
                ekbUserService.updateRoleUserInfo();
            } catch (Exception e) {
                log.error("【易快报-更新角色下员工信息】异常！", e);
                String errorMsg = com.qmqb.oa.common.utils.StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "";
                errorMsg = errorMsg.length() > 100 ? errorMsg.substring(0, 100) : errorMsg;
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.ROLE_USER_UPDATE.getEventType(), null, null, null, errorMsg);
            }
        }


    }

    /**
     * 组装易快报修改员工请求
     *
     * @param mobile
     * @param dingDeptIdList
     * @param updateUserList
     * @return
     */
    private List<EkbUpdateUserReq> handleEkbUpdateUserReq(String mobile, String dingDeptIdList,
                                                          List<EkbUpdateUserReq> updateUserList, Integer companyId) {
        EkbUpdateUserReq ekbUpdateUserReq = new EkbUpdateUserReq();
        List<EkbDingUserMapping> userMappingList = ekbDingUserMappingService.list(Wrappers.lambdaQuery(EkbDingUserMapping.class).
                eq(EkbDingUserMapping::getMobile, mobile)
                .isNotNull(EkbDingUserMapping::getEkbUserId)
        );
        EkbDingUserMapping ekbDingUserMapping = userMappingList.get(0);
        ekbUpdateUserReq.setId(ekbDingUserMapping.getEkbUserId());
        ekbUpdateUserReq.setName(ekbDingUserMapping.getEkbUserName());
        ekbUpdateUserReq.setCellphone(ekbDingUserMapping.getMobile());
        // 根据钉钉部门id列表挨个从钉钉与易快报部门映射表映射一一映射关系获取易快报部门id，组装列表请求
        List<String> ekbDeptIdList = getEkbDeptIdList(dingDeptIdList, companyId);

        ekbUpdateUserReq.setDefaultDepartment(ekbDeptIdList.get(0));
        ekbUpdateUserReq.setDepartments(ekbDeptIdList);
        updateUserList.add(ekbUpdateUserReq);
        return updateUserList;
    }

    /**
     * 根据钉钉部门id列表查询钉钉部门映射表获取易快报部门列表
     *
     * @param dingDeptIdList
     * @return
     */
    public List<String> getEkbDeptIdList(String dingDeptIdList, Integer companyId) {
        List<String> ekbDeptIdList = Lists.newArrayList();
        String[] dingDeptIds = dingDeptIdList.split(",");
        for (String deptId : dingDeptIds) {
            LambdaQueryWrapper<EkbDingDeptMapping> ekbDingDeptMappingWrapper = Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq("1".equals(deptId), EkbDingDeptMapping::getCompanyId, companyId)
                    .eq(EkbDingDeptMapping::getDingDeptId, deptId);
            EkbDingDeptMapping ekbDingDeptMapping = ekbDingDeptMappingService.getOne(ekbDingDeptMappingWrapper);
            ekbDeptIdList.add(ekbDingDeptMapping.getEkbDeptId());
        }
        return ekbDeptIdList;
    }

    /**
     * 更新用户信息
     *
     * @param userId
     * @param userDetail
     * @param companyId
     */
    private void updateUserInfo(String userId, OapiV2UserGetResponse.UserGetResponse userDetail, Integer companyId) {

        // 处理部门id列表，以逗号分隔
        String userDingDeptIdStr = handleDeptIdList(userDetail.getDeptIdList());

        // 更新钉钉员工表
        LambdaUpdateWrapper<DingUser> updateDingUserWrapper = Wrappers.lambdaUpdate(DingUser.class)
                .set(DingUser::getDeptIdList, userDingDeptIdStr)
                .set(DingUser::getRemark, "同步钉钉")
                .set(DingUser::getUpdateTime, LocalDateTime.now())
                .eq(DingUser::getCompanyId, companyId)
                .eq(DingUser::getUserId, userId);
        dingUserService.update(updateDingUserWrapper);
    }

    /**
     * 更新钉钉易快报映射表的钉钉部门id
     *
     * @param userDetail
     * @param isDeleteEvent 是否删除事件
     * @return
     */
    public String updateEkbDingUserMapping(OapiV2UserGetResponse.UserGetResponse userDetail,boolean isDeleteEvent) {
        // 拼接组成最新的钉钉部门列表(不同主体下的部门id列表)
        StringBuilder sb = new StringBuilder();

        if (!isDeleteEvent) {
            // 非删除事件则需要判断是否有该用户在映射表中
            // 通过userId查询该用户在映射表中是否存在记录
            EkbDingUserMapping userMapping = ekbDingUserMappingService.findOneByDingUserId(userDetail.getUserid());
            // 如果映射数据不存在，则需要新增一条记录
            if (ObjectUtil.isEmpty(userMapping)) {
                saveEkbDingUserMapping(userDetail.getUserid(), userDetail);
            }
        }
        // 查出该用户在不同主体下的所有部门id列表
        List<DingUser> allDingUserList = dingUserService.list(Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getMobile, userDetail.getMobile()));
        for (DingUser user : allDingUserList) {
            sb.append(user.getDeptIdList()).append(",");
        }
        // 整合该用户所有的钉钉部门id
        String allDingDeptIdString = sb.substring(0, sb.length() - 1);

        sb = new StringBuilder();
        //过滤出相同钉钉userId的用户
        List<DingUser> sameDingUserIdList = allDingUserList.stream().filter(dingUser -> dingUser.getUserId().equals(userDetail.getUserid()))
                .collect(Collectors.toList());
        for (DingUser user : sameDingUserIdList) {
            sb.append(user.getDeptIdList()).append(",");
        }
        // 钉钉相同userId的钉钉部门id
        String sameDingUserIdDeptIdString = sb.substring(0, sb.length() - 1);
        // 更新钉钉与易快报员工映射表
        LambdaUpdateWrapper<EkbDingUserMapping> updateEkbUserWrapper = Wrappers.lambdaUpdate(EkbDingUserMapping.class)
                .set(EkbDingUserMapping::getDingDeptIdList, sameDingUserIdDeptIdString)
                .set(EkbDingUserMapping::getUpdateTime, LocalDateTime.now())
                .eq(EkbDingUserMapping::getDingUserId, userDetail.getUserid());
        ekbDingUserMappingService.update(updateEkbUserWrapper);
        return allDingDeptIdString;
    }

    public String handleDeptIdList(List<Long> deptIdList) {
        String deptIdString = "";
        StringBuilder sb = new StringBuilder();
        for (Long deptId : deptIdList) {
            sb.append(deptId).append(",");
        }
        if (sb.length() > 0) {
            deptIdString = sb.substring(0, sb.length() - 1);
        }
        return deptIdString;
    }

    /**
     * 员工离职
     *
     * @param bizData
     * @param companyId
     */
    public void deleteUser(JSONObject bizData, Integer companyId) throws Exception {
        // 停启用员工集合
        List<String> ekbDelUserIdList = Lists.newArrayList();
        // 修改员工集合
        List<EkbUpdateUserReq> ekbUpdateUserList = new ArrayList<>();
        JSONArray userIdArray = getDingTalkUserId(bizData);

        for (Object userIdObj : userIdArray) {
            String userId = userIdObj.toString();
            // 2.1、根据用户id调钉钉接口获取用户详情
            DingUser dingUser = dingUserService.getOne(Wrappers.lambdaQuery(DingUser.class)
                    .eq(DingUser::getCompanyId, companyId).eq(DingUser::getUserId, userId));
            // 2、删除钉钉用户
            dingUserService.remove(Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getCompanyId, companyId)
                    .eq(DingUser::getUserId, userId));
            // 3、查询该用户存在其他主体数据
            List<DingUser> dingUserList = dingUserService.list(Wrappers.lambdaQuery(DingUser.class).eq(DingUser::getMobile, dingUser.getMobile()));
            // 4、用户存在，更新钉钉与易快报员工映射表，调用易快报批量修改修改接口
            if (ObjectUtil.isNotEmpty(dingUserList)) {
                log.info("用户存在其他部门，做更新处理，用户id：{}", userId);
                OapiV2UserGetResponse.UserGetResponse userDetail = new OapiV2UserGetResponse.UserGetResponse();
                userDetail.setUserid(dingUser.getUserId());
                userDetail.setMobile(dingUser.getMobile());
                // 5、更新钉钉部门映射表，更新为最新的钉钉部门id
                String dingDeptIdList = updateEkbDingUserMapping(userDetail, true);

                // 6、组装易快报批量修改员工接口参数
                ekbUpdateUserList = handleEkbUpdateUserReq(dingUser.getMobile(), dingDeptIdList, ekbUpdateUserList, companyId);
            } else {
                // 7、处理调用易快报的用户id
                List<EkbDingUserMapping> ekbDingUserMappingList = ekbDingUserMappingService.list(Wrappers.lambdaQuery(EkbDingUserMapping.class).
                        eq(EkbDingUserMapping::getMobile, dingUser.getMobile()));
                if (CollectionUtils.isNotEmpty(ekbDingUserMappingList)) {
                    ekbDelUserIdList.add(ekbDingUserMappingList.get(0).getEkbUserId());
                }
                log.info("用户不存在其他部门，做删除处理，用户id：{}", userId);
                //11、不存在，删除映射关系
                ekbDingUserMappingService.remove(Wrappers.lambdaQuery(EkbDingUserMapping.class).eq(EkbDingUserMapping::getDingUserId, userId));
            }

        }
        //停用员工结果
        boolean disableUserResult = true;
        if (ObjectUtil.isNotEmpty(ekbDelUserIdList)) {

            // 12、调用易快报批量停启用员工接口
            try {
                ekbUserService.disableOrEnableStaff(ekbDelUserIdList);
            } catch (Exception e) {
                disableUserResult = false;
                log.error("【易快报-停启用员工】异常!", e);
                String errorMsg = this.getErrorMsg(e);
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.USER_BATCH_DELETE.getEventType(), JSON.toJSONString(ekbDelUserIdList), null, null, errorMsg);
            }
        }
        //更新员工结果
        boolean updateUserResult = true;
        if (ObjectUtil.isNotEmpty(ekbUpdateUserList)) {
            // 13、调用易快报批量修改员工接口
            EkbBatchUpdateUserListReq ekbBatchUpdateUserReq = new EkbBatchUpdateUserListReq();
            ekbBatchUpdateUserReq.setStaffList(ekbUpdateUserList);
            try {
                ekbUserService.batchUpdateUser(ekbBatchUpdateUserReq);
            } catch (Exception e) {
                updateUserResult = false;
                log.error("【易快报-批量修改员工】异常!", e);
                String errorMsg = this.getErrorMsg(e);
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.USER_BATCH_MODIFY.getEventType(), com.alibaba.fastjson.JSON.toJSONString(ekbBatchUpdateUserReq), null, null, errorMsg);
            }

        }

        if (disableUserResult && updateUserResult) {
            // 14、停用员工并且更新员工成功，才能更新角色下员工信息
            try {
                ekbUserService.updateRoleUserInfo();
            } catch (Exception e) {
                log.error("【易快报-更新角色下员工信息】异常！", e);
                String errorMsg = com.qmqb.oa.common.utils.StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "";
                errorMsg = errorMsg.length() > 100 ? errorMsg.substring(0, 100) : errorMsg;
                // 保存事件表
                sysEventService.saveSysEvent(companyId, 2, EkbEventType.ROLE_USER_UPDATE.getEventType(), null, null, null, errorMsg);
            }
        }

    }

    /**
     * 获取异常信息
     *
     * @param e
     * @return
     */
    public String getErrorMsg(Exception e) {
        String errorMsg = com.qmqb.oa.common.utils.StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "";
        return errorMsg.length() > 100 ? errorMsg.substring(0, 100) : errorMsg;
    }
}
