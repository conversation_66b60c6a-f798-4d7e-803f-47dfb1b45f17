package com.qmqb.oa.admin.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.response.OapiAttendanceListResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.qmqb.oa.admin.api.client.DingTalkOApiClient;
import com.qmqb.oa.admin.support.TenantContextHolder;
import com.qmqb.oa.common.enums.Tenant;
import com.qmqb.oa.common.utils.MdcUtil;
import com.qmqb.oa.system.domain.DingTalkClockResult;
import com.qmqb.oa.system.domain.DingTalkUser;
import com.qmqb.oa.system.service.DingTalkClockResultService;
import com.qmqb.oa.system.service.DingTalkUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步打卡结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Slf4j
@Component("syncClockResultTask")
@RequiredArgsConstructor
public class SyncClockResultTask {

    private final DingTalkOApiClient dingTalkOApiClient;
    private final DingTalkUserService dingTalkUserService;
    private final DingTalkClockResultService dingtalkClockResultService;

    /**
     * 整月
     */
    public void syncMonth() {
        for (Integer tenantId : Arrays.asList(Tenant.QMQB.getValue(), Tenant.FSBYL.getValue(), Tenant.OAOP.getValue())) {
            execute(tenantId, null);
        }
    }


    /**
     * 执行同步
     * 每月10号同步上一月打卡数据
     */
    public void execute(Integer tenantId, String date) {
        try {
            TenantContextHolder.setTenantId(tenantId);
            MdcUtil.setTrace();
            MdcUtil.setModuleName(Tenant.getNameByValue(tenantId) + "打卡结果");
//            DateTime now = DateTime.now();
//            DateTime beginOfDay = DateUtil.beginOfWeek(now);
//            DateTime endOfDay = DateUtil.endOfWeek(now);
//            if (StrUtil.isAllNotBlank(begin, end)) {
//                beginOfDay = DateUtil.parseDate(begin);
//                endOfDay = DateUtil.parseDate(end);
//            } else {
//                int dayOfMonth = DateUtil.dayOfMonth(now);
//                int lengthOfMonth = DateUtil.lengthOfMonth(now.month(), DateUtil.isLeapYear(now.year()));
//                int dayOfWeek = DateUtil.dayOfWeek(now);
//                boolean endOfWeek = dayOfWeek == Week.SUNDAY.getValue();
//                boolean endOfMonth = dayOfMonth == lengthOfMonth;
//                if (!(endOfWeek || endOfMonth)) {
//                    log.info("非周末或月末不执行此次任务...");
//                    return;
//                }
//                if (endOfMonth) {
//                    endOfDay = DateUtil.endOfMonth(now);
//                }
//            }
            LocalDate lastMonth = LocalDate.now().minusMonths(1L);
            if (StrUtil.isNotBlank(date)) {
                lastMonth = LocalDateTimeUtil.parseDate(date, DatePattern.PURE_DATE_PATTERN);
            }
            // 本月的第一天
            LocalDate start = LocalDate.of(lastMonth.getYear(), lastMonth.getMonthValue(), 1);
            // 本月的最后一天
            LocalDate end = start.plusMonths(1).minusDays(1);

            List<WeekRange> weekRanges = getWeeksInRange(start, end);
            for (WeekRange weekRange : weekRanges) {
                LocalDateTime startDate = LocalDateTime.of(weekRange.getStartDate(), LocalTime.MIN);
                LocalDateTime endDate = LocalDateTime.of(weekRange.getEndDate(), LocalTime.MAX);
                List<DingTalkUser> users = dingTalkUserService.list(Wrappers.lambdaQuery(DingTalkUser.class).eq(DingTalkUser::getTenantId, tenantId));
                List<String> userIds = users.stream().map(DingTalkUser::getUserId).collect(Collectors.toList());
                int pageSize = 50;
                int total = userIds.size();
                int totalPage = PageUtil.totalPage(total, pageSize);
                List<OapiAttendanceListResponse.Recordresult> result = new ArrayList<>();
                for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
                    List<String> page = page(userIds, pageNo, pageSize);
                    List<OapiAttendanceListResponse.Recordresult> recordresults = dingTalkOApiClient.listAttendance(
                            Date.from(startDate.atZone(ZoneId.systemDefault()).toInstant()), Date.from(endDate.atZone(ZoneId.systemDefault()).toInstant()),
                            page, 0L);
                    result.addAll(recordresults);
                }
                List<DingTalkClockResult> results = result.stream().map(e -> {
                    DingTalkClockResult clockResult = new DingTalkClockResult();
                    clockResult.setId(e.getId());
                    clockResult.setSourceType(e.getSourceType());
                    clockResult.setBaseCheckTime(DateTime.of(e.getBaseCheckTime()));
                    clockResult.setUserCheckTime(DateTime.of(e.getUserCheckTime()));
                    clockResult.setProcInstId(e.getProcInstId());
                    clockResult.setLocationResult(e.getLocationResult());
                    clockResult.setTimeResult(e.getTimeResult());
                    clockResult.setCheckType(e.getCheckType());
                    clockResult.setUserId(e.getUserId());
                    Optional<String> any = users.stream().filter(o -> StrUtil.equals(o.getUserId(), e.getUserId())).map(DingTalkUser::getUserName).findAny();
                    if (any.isPresent()) {
                        clockResult.setUserName(any.get());
                    } else {
                        OapiV2UserGetResponse.UserGetResponse user = dingTalkOApiClient.getUser(e.getUserId());
                        clockResult.setUserName(user.getName());
                    }
                    clockResult.setWorkDate(DateTime.of(e.getWorkDate()));
                    clockResult.setRecordId(e.getRecordId());
                    clockResult.setPlanId(e.getPlanId());
                    clockResult.setGroupId(e.getGroupId());
                    if ("OffDuty".equals(clockResult.getCheckType())) {
                        LocalDateTime at20 = LocalDateTime.of(LocalDateTimeUtil.of(e.getBaseCheckTime()).toLocalDate(), LocalTime.of(20, 0, 0));
                        LocalDateTime userCheckTime = LocalDateTimeUtil.of(e.getUserCheckTime());
                        // 20点后打卡下班
                        if (userCheckTime.isAfter(at20) || userCheckTime.isEqual(at20)) {
                            clockResult.setIsAfter20(BoolFlagEnum.YES.getStatus());
                        }
                        // 隔天凌晨00：00~05:00打卡下班
                        LocalDateTime at0 = LocalDateTime.of(LocalDateTimeUtil.of(e.getBaseCheckTime()).toLocalDate(), LocalTime.of(0, 0, 0));
                        LocalDateTime at5 = LocalDateTime.of(LocalDateTimeUtil.of(e.getBaseCheckTime()).toLocalDate(), LocalTime.of(5, 0, 0));
                        boolean after0 = userCheckTime.isAfter(at0) || userCheckTime.isEqual(at0);
                        boolean before5 = userCheckTime.isBefore(at5) || userCheckTime.isEqual(at5);
                        if (after0 && before5) {
                            clockResult.setIsAfter20(BoolFlagEnum.YES.getStatus());
                        }
                    }
                    clockResult.setTenantId(tenantId);
                    return clockResult;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(results)) {
                    dingtalkClockResultService.saveBatch(results);
                }
            }


        } finally {
            MdcUtil.clear();
            TenantContextHolder.clearTenantId();
        }
    }

    /**
     * 内存分页
     *
     * @param data     数据
     * @param pageNo   页码
     * @param pageSize 页大小
     * @param <T>
     * @return
     */
    public static <T> List<T> page(List<T> data, Integer pageNo, Integer pageSize) {
        if (Objects.isNull(data) || data.isEmpty()) {
            return Collections.emptyList();
        }
        pageNo = Objects.isNull(pageNo) || pageNo < 1 ? 1 : pageNo;
        pageSize = Objects.isNull(pageSize) || pageSize < 1 ? 10 : pageSize;
        return data.stream()
                .skip((long) pageSize * (pageNo - 1))
                .limit(pageSize).collect(Collectors.toList());
    }

    public static void main(String[] args) {
        LocalDate lastMonth = LocalDate.now().minusMonths(1L);

        LocalDate startDate = LocalDate.of(lastMonth.getYear(), lastMonth.getMonthValue(), 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1); // 本月的最后一天

        List<WeekRange> weekRanges = getWeeksInRange(startDate, endDate);

        // 打印每周的起始和结束日期
        for (WeekRange weekRange : weekRanges) {
            System.out.println("Week starting on " + weekRange.getStartDate() + " and ending on " + weekRange.getEndDate());
        }
    }

    // 一个简单的类来表示一周的起始和结束日期
    static class WeekRange {
        private LocalDate startDate;
        private LocalDate endDate;

        public WeekRange(LocalDate startDate, LocalDate endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public LocalDate getStartDate() {
            return startDate;
        }

        public LocalDate getEndDate() {
            return endDate;
        }
    }

    // 获取给定日期范围内的所有周
    public static List<WeekRange> getWeeksInRange(LocalDate startDate, LocalDate endDate) {
        List<WeekRange> weekRanges = new ArrayList<>();

        LocalDate currentDate = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)); // 调整为当前周的周一

        while (!currentDate.isAfter(endDate)) {
            // 计算本周的结束日期（周六）
            LocalDate endDateOfWeek = currentDate.plusDays(6);
            if (endDateOfWeek.isAfter(endDate)) {
                // 如果本周的周六超出了endDate，则只包含到endDate的部分
                endDateOfWeek = endDate;
            }

            weekRanges.add(new WeekRange(currentDate, endDateOfWeek));

            // 移动到下一周的周一
            currentDate = endDateOfWeek.plusDays(1).with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
        }

        return weekRanges;
    }
}
