package com.qmqb.oa.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qmqb.oa.common.exception.CustomException;
import com.qmqb.oa.common.utils.DateUtils;
import com.qmqb.oa.common.utils.StringUtils;
import com.qmqb.oa.system.domain.DimissionEmployee;
import com.qmqb.oa.system.mapper.DimissionEmployeeMapper;
import com.qmqb.oa.system.service.DimissionEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 离职员工Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Service
public class DimissionEmployeeServiceImpl extends ServiceImpl<DimissionEmployeeMapper, DimissionEmployee> implements DimissionEmployeeService {
    @Autowired
    private DimissionEmployeeMapper dimissionEmployeeMapper;

    /**
     * 查询离职员工
     *
     * @param id 离职员工ID
     * @return 离职员工
     */
    @Override
    public DimissionEmployee selectDimissionEmployeeById(Long id) {
        return dimissionEmployeeMapper.selectDimissionEmployeeById(id);
    }

    /**
     * 查询离职员工列表
     *
     * @param dimissionEmployee 离职员工
     * @return 离职员工
     */
    @Override
    public List<DimissionEmployee> selectDimissionEmployeeList(DimissionEmployee dimissionEmployee) {
        return dimissionEmployeeMapper.selectDimissionEmployeeList(dimissionEmployee);
    }

    /**
     * 新增离职员工
     *
     * @param dimissionEmployee 离职员工
     * @return 结果
     */
    @Override
    public int insertDimissionEmployee(DimissionEmployee dimissionEmployee) {
        dimissionEmployee.setCreateTime(DateUtils.getNowDate());
        return dimissionEmployeeMapper.insertDimissionEmployee(dimissionEmployee);
    }

    /**
     * 修改离职员工
     *
     * @param dimissionEmployee 离职员工
     * @return 结果
     */
    @Override
    public int updateDimissionEmployee(DimissionEmployee dimissionEmployee) {
        dimissionEmployee.setUpdateTime(DateUtils.getNowDate());
        return dimissionEmployeeMapper.updateDimissionEmployee(dimissionEmployee);
    }

    /**
     * 批量删除离职员工
     *
     * @param ids 需要删除的离职员工ID
     * @return 结果
     */
    @Override
    public int deleteDimissionEmployeeByIds(Long[] ids) {
        return dimissionEmployeeMapper.deleteDimissionEmployeeByIds(ids);
    }

    /**
     * 删除离职员工信息
     *
     * @param id 离职员工ID
     * @return 结果
     */
    @Override
    public int deleteDimissionEmployeeById(Long id) {
        return dimissionEmployeeMapper.deleteDimissionEmployeeById(id);
    }

    /**
     * 导入离职员工数据
     *
     * @param dimissionEmployeeList 离职员工数据列表
     * @param isUpdateSupport       是否更新支持，如果已存在，则进行更新数据
     * @param operName              操作用户
     * @return 结果
     */
    @Override
    public String importData(List<DimissionEmployee> dimissionEmployeeList, boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(dimissionEmployeeList) || dimissionEmployeeList.size() == 0) {
            throw new CustomException("导入离职员工数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DimissionEmployee employee : dimissionEmployeeList) {
            String name = employee.getName();
            if (StrUtil.isBlank(name)) {
                continue;
            }
            try {
                // 验证是否存在这个用户
                int count = this.countByName(name);
                if (!SqlHelper.retBool(count)) {
                    employee.setCreateBy(operName);
                    this.insertDimissionEmployee(employee);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、离职员工 " + name + " 导入成功");
                } else if (isUpdateSupport) {
                    employee.setUpdateBy(operName);
                    this.updateDimissionEmployee(employee);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、离职员工 " + name + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、离职员工 " + name + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、离职员工 " + name + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 更加姓名统计行数
     *
     * @param name
     * @return
     */
    @Override
    public int countByName(String name) {
        return this.count(Wrappers.lambdaQuery(DimissionEmployee.class).eq(DimissionEmployee::getName, name));
    }

}
