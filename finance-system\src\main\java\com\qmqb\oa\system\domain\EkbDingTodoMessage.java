package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 易快报到钉钉消息二次处理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_ekb_ding_todo_message")
public class EkbDingTodoMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 易快报消息id
     */
    private String ekbMessageId;
    /**
     * 易快报单据id
     */
    private String ekbFlowId;

    /**
     * 业务系统侧的唯一标识ID，即业务ID
     */
    private String dingSourceId;

    /**
     * 钉钉代办任务id
     */
    private String dingTaskId;

    /**
     * 公司主键id: 0-全民, 1-合众, 2-公共，3-佛山百益来，4-OA办公平台
     */
    private Integer companyId;

    /**
     * 用户所在公司钉钉主体的unionId
     */
    private String dingUnionId;

    /**
     * 0-待处理;1-处理成功;2-处理失败
     */
    private Integer dingTaskStatus;
    /**
     * 代办内容（易快报-活动名称）
     */
    private String actionName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * ekb出站消息类别: flow.rejected=被驳回;freeflow.retract=单据撤回;freeflow.delete=单据删除;backlog.sending=待寄送;flow.paid=已支付/审批完成;freeflow.mention=被@;backlog.paying=待支付;freeflow.comment=评论;backlog.approving=待审批;freeflow.carbonCopy=抄送
     */
    private String ekbMsgAction;
}
