package com.qmqb.oa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.oa.system.domain.DimissionEmployee;

import java.util.List;

/**
 * 离职员工Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
public interface DimissionEmployeeMapper extends BaseMapper<DimissionEmployee> {
    /**
     * 查询离职员工
     *
     * @param id 离职员工ID
     * @return 离职员工
     */
    DimissionEmployee selectDimissionEmployeeById(Long id);

    /**
     * 查询离职员工列表
     *
     * @param dimissionEmployee 离职员工
     * @return 离职员工集合
     */
    List<DimissionEmployee> selectDimissionEmployeeList(DimissionEmployee dimissionEmployee);

    /**
     * 新增离职员工
     *
     * @param dimissionEmployee 离职员工
     * @return 结果
     */
    int insertDimissionEmployee(DimissionEmployee dimissionEmployee);

    /**
     * 修改离职员工
     *
     * @param dimissionEmployee 离职员工
     * @return 结果
     */
    int updateDimissionEmployee(DimissionEmployee dimissionEmployee);

    /**
     * 删除离职员工
     *
     * @param id 离职员工ID
     * @return 结果
     */
    int deleteDimissionEmployeeById(Long id);

    /**
     * 批量删除离职员工
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteDimissionEmployeeByIds(Long[] ids);

}
