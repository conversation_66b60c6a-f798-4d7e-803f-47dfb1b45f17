package com.qmqb.oa;

import com.hzed.structure.lock.annotation.EnableLock;
import com.qmqb.oa.common.constant.Constants;
import com.qmqb.oa.common.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@Slf4j
@EnableLock
@EnableCaching
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class AdminApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext application = SpringApplication.run(AdminApplication.class, args);
        ConfigurableEnvironment env = application.getEnvironment();
        String ip = IpUtils.getHostIp();
        String name = env.getProperty("spring.application.name");
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        String active = env.getProperty("spring.profiles.active");
        log.info("\n--------------------------------------------------------------------------\n\t" +
                "Application " + name + " is running, Active " + active + ".\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "Swagger Doc:http://" + ip + ":" + port + path + "/swagger-ui.html\n" +
                "--------------------------------------------------------------------------");
    }
}
