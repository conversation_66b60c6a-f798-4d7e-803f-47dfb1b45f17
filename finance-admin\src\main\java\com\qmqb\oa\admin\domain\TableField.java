package com.qmqb.oa.admin.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-10
 */
@NoArgsConstructor
@Data
public class TableField {

    @JSONField(name = "rowValue")
    private List<RowValue> rowValue;

    @NoArgsConstructor
    @Data
    public static class RowValue {
        @JSONField(name = "componentType")
        private String componentType;
        @JSONField(name = "label")
        private String label;
        @JSONField(name = "extendValue")
        private String extendValue;
        @JSONField(name = "value")
        private String value;
        @J<PERSON>NField(name = "key")
        private String key;
    }
}
