package com.qmqb.oa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.oa.system.domain.Subject;

import java.util.List;

/**
 * <p>
 * 科目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
public interface SubjectService extends IService<Subject> {

    /**
     * 根据
     *
     * @param subjectName
     * @param dingTalkDeptId
     * @return
     */
    Subject selectBySubjectNameAndDingTalkDeptId(String subjectName, String dingTalkDeptId);

    /**
     * 根据名称找部门
     *
     * @param subjectName
     * @return
     */
    Subject selectBySubjectName(String subjectName);

    /**
     * 查询科目
     *
     * @param id 科目id
     * @return 科目
     */
    Subject selectSubjectById(Long id);

    /**
     * 查询科目列表
     *
     * @param subject 科目
     * @return 科目集合
     */
    List<Subject> selectSubjectList(Subject subject);

    /**
     * 批量删除科目
     *
     * @param ids 需要删除的科目ID
     * @return 结果
     */
    boolean deleteSubjectByIds(Long[] ids);
}
