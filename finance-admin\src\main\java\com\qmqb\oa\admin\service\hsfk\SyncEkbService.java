package com.qmqb.oa.admin.service.hsfk;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.oa.admin.config.EkbConfig;
import com.qmqb.oa.admin.domain.dto.EkbInvoiceTypeDTO;
import com.qmqb.oa.admin.domain.remote.reponse.EkbFeeTypeSearchResponse;
import com.qmqb.oa.admin.domain.remote.request.EkbFeeTypeSearchRequest;
import com.qmqb.oa.admin.domain.vo.ekb.*;
import com.qmqb.oa.common.utils.StringUtils;
import com.qmqb.oa.common.utils.http.HttpRestService;
import com.qmqb.oa.system.domain.DingAppConf;
import com.qmqb.oa.system.domain.EkbDingDeptMapping;
import com.qmqb.oa.system.domain.EkbDingUserMapping;
import com.qmqb.oa.system.service.DingAppConfService;
import com.qmqb.oa.system.service.EkbDingDeptMappingService;
import com.qmqb.oa.system.service.EkbDingUserMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步易快报数据
 * <AUTHOR>
 * @date 2024/7/22
 */
@Slf4j
@Service
public class SyncEkbService {
    /**
     * 部门等级最大值
     */
    private final static  int TOP_LEVEL = 6;


    @Autowired
    private EkbDingDeptMappingService ekbDingDeptMappingService;
    @Autowired
    private EkbDeptService ekbDeptService;
    @Autowired
    private EkbLoginService ekbLoginService;
    @Autowired
    private DingAppConfService dingAppConfService;
    @Autowired
    private EkbConfig ekbConfig;
    @Autowired
    private HttpRestService httpRestService;
    @Autowired
    private EkbDingUserMappingService ekbDingUserMappingService;
    @Autowired
    private EkbUserService ekbUserService;
//    public void syncDept(){
//        for(int level=0; level <= TOP_LEVEL; level ++  ){
//            //找出level相同的部门
//            LambdaQueryWrapper<EkbDingDeptMapping> queryWrapper = Wrappers.lambdaQuery(EkbDingDeptMapping.class)
//                    .eq(EkbDingDeptMapping::getDingDeptLevel, level);
//            List<EkbDingDeptMapping> mappingList = ekbDingDeptMappingService.list(queryWrapper);
//            if(mappingList.isEmpty()){
//                continue;
//            }
//
//            List<EkbAddDeptReq> ekbAddDeptReqList = Lists.newArrayListWithCapacity(mappingList.size());
//            for (EkbDingDeptMapping mapping : mappingList) {
//                String parentId = "";
//                String code = "";
//                if( level > 0){
//                    //非跟部门，需要通过ding的parent_id找到ekb的parent_id
//                    EkbDingDeptMapping parentDept = ekbDingDeptMappingService.getOne(Wrappers.lambdaQuery(EkbDingDeptMapping.class)
//                            .eq(EkbDingDeptMapping::getDingDeptId, mapping.getDingParentId()));
//                    parentId = parentDept.getEkbDeptId();
//                }
//                EkbAddDeptReq ekbAddDeptReq = new EkbAddDeptReq();
//                ekbAddDeptReq.setCode(code);
//                ekbAddDeptReq.setName(mapping.getDeptName());
//                ekbAddDeptReq.setParentId(parentId);
//                ekbAddDeptReqList.add(ekbAddDeptReq);
//            }
//            //开始调用易快报批量新增部门接口
//            EkbBatchAddDeptReq ekbBatchAddDeptReq = new EkbBatchAddDeptReq();
//            ekbBatchAddDeptReq.setDepartmentList(ekbAddDeptReqList);
//            ekbDeptService.batchAddDept(ekbBatchAddDeptReq);
//        }
//    }


    public void pushUserToEkb4BatchAdd(Integer start,Integer count){
        LambdaQueryWrapper<EkbDingUserMapping> wrapper = Wrappers.lambdaQuery(EkbDingUserMapping.class)
                .in(EkbDingUserMapping::getDingUserId,
                        "116955613237456395"
                       );
        Page<EkbDingUserMapping> page = new Page();
        page.setTotal(count);
        page.setSize(count);
        page = ekbDingUserMappingService.page(page, wrapper);
        List<EkbDingUserMapping> mappingList = page.getRecords();
        log.info("待处理的用户列表{}",JSON.toJSONString(mappingList));
        EkbBatchAddUserListReq req = new EkbBatchAddUserListReq();
        List<EkbAddUserReq> staffList = Lists.newArrayListWithCapacity(mappingList.size());
        mappingList.forEach( m ->{
            log.info("用户userId{}",m.getDingUserId());
            EkbAddUserReq addUserReq = new EkbAddUserReq();
            addUserReq.setName(m.getDingUserName());
            addUserReq.setCellphone(m.getMobile());
            List<String> ekbDeptIdList = new ArrayList<>();
            String[] split = m.getDingDeptIdList().split(",");
            for(int i = 0; i < split.length; i++){

                EkbDingDeptMapping deptMapping = ekbDingDeptMappingService.getOne(
                        Wrappers.lambdaQuery(EkbDingDeptMapping.class).eq(EkbDingDeptMapping::getDingDeptId, split[i]));
                if(StringUtils.isNotBlank(deptMapping.getEkbDeptId())){
                    ekbDeptIdList.add(deptMapping.getEkbDeptId());
                }
            }
            addUserReq.setDefaultDepartment(ekbDeptIdList.get(0));
            addUserReq.setDepartments(ekbDeptIdList);
            staffList.add(addUserReq);
        });
        log.info("易快报用户列表{}",JSON.toJSONString(staffList));
        req.setStaffList(staffList);
        ekbUserService.batchAddUser(req);
    }

    /**
     *
     * @param start
     * @param count
     */
    public void pushUserToEkb4BatchUpdate(Integer start, Integer count){
        LambdaQueryWrapper<EkbDingUserMapping> wrapper = Wrappers.lambdaQuery(EkbDingUserMapping.class)
                .in(EkbDingUserMapping::getDingUserId,"116955613237456395");
        Page<EkbDingUserMapping> page = new Page();
        page.setTotal(count);
        page.setSize(count);
        page = ekbDingUserMappingService.page(page, wrapper);
        List<EkbDingUserMapping> mappingList = page.getRecords();
        log.info("待处理的用户列表{}",JSON.toJSONString(mappingList));
        EkbBatchUpdateUserListReq req = new EkbBatchUpdateUserListReq();
        List<EkbUpdateUserReq> staffList = Lists.newArrayListWithCapacity(mappingList.size());
        mappingList.forEach( m ->{
            log.info("用户userId{}",m.getDingUserId());
            EkbUpdateUserReq updateUserReq = new EkbUpdateUserReq();
            updateUserReq.setId(m.getEkbUserId());
            updateUserReq.setName(m.getEkbUserName());
            updateUserReq.setCellphone(m.getMobile());
            List<String> ekbDeptIdList = new ArrayList<>();
            String[] split = m.getDingDeptIdList().split(",");
            for(int i = 0; i < split.length; i++){

                EkbDingDeptMapping deptMapping = ekbDingDeptMappingService.getOne(
                        Wrappers.lambdaQuery(EkbDingDeptMapping.class).eq(EkbDingDeptMapping::getDingDeptId, split[i]));
                if(StringUtils.isNotBlank(deptMapping.getEkbDeptId())){
                    ekbDeptIdList.add(deptMapping.getEkbDeptId());
                }
            }
            updateUserReq.setDefaultDepartment(ekbDeptIdList.get(0));
            updateUserReq.setDepartments(ekbDeptIdList);
            staffList.add(updateUserReq);
        });
        log.info("易快报用户列表{}",JSON.toJSONString(staffList));
        req.setStaffList(staffList);
        ekbUserService.batchUpdateUser(req);
    }


    public void pullUsersFromEkb4UpdateEkbUserId(Integer start,Integer count){
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getStaffListUrl() + "?accessToken=" + accessTokenRep.getAccessToken() + "&start=" + start + "&count=" +  count;
        EkbUserListRsp ekbUserListRsp = httpRestService.get(url, EkbUserListRsp.class, "【查询已激活员工列表】");
        log.info("员工数量：{}",ekbUserListRsp.getItems());
        List<EkbUserRsp> rspList = ekbUserListRsp.getItems();
        if(rspList.size() == 0){
            return;
        }
        List<EkbDingUserMapping> userMappingList = Lists.newArrayListWithCapacity(rspList.size());
        rspList.forEach(s ->{
            if(!s.getActive()){
                return;
            }
            EkbDingUserMapping selectData = ekbDingUserMappingService.getOne(Wrappers.lambdaQuery(EkbDingUserMapping.class).eq(EkbDingUserMapping::getMobile, s.getCellphone()));
            EkbDingUserMapping userMapping = new EkbDingUserMapping();
            userMapping.setId(selectData.getId());
            userMapping.setEkbUserId(s.getId());
            userMapping.setEkbUserName(s.getName());
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < s.getDepartments().size(); i++) {
                sb.append(s.getDepartments().get(i));
                if( i != s.getDepartments().size() -1){
                    sb.append(",");
                }
            }
            userMapping.setEkbDeptIdList(sb.toString());
            userMapping.setEkbDefaultDeptId(s.getDefaultDepartment());
            userMapping.setUpdateTime(LocalDateTime.now());
            userMappingList.add(userMapping);
        });

        log.info("整理后的数据,{}",JSON.toJSONString(userMappingList));
        ekbDingUserMappingService.updateBatchById(userMappingList);

    }


    /**
     * 易快报存量部门数据处理
     * @param start
     * @param count
     */
    public void pullDeptFromEkbList(Integer start, Integer count){
        EkbDepartmentListRsp rsp = ekbDeptService.getDepartmentList(start, count);
        List<EkbDepartmentRsp> ekbDeptList = rsp.getItems();
        log.info("部门总数{}",ekbDeptList.size());
        ekbDeptList.forEach(e -> {
            if(!e.getActive()){
                log.info(e.getName() + "部门停用");
                return;
            }
            EkbDingDeptMapping deptMapping = new EkbDingDeptMapping();
            deptMapping.setEkbDeptId(e.getId());
            deptMapping.setEkbDeptName(e.getName());
            deptMapping.setEkbParentId(e.getParentId());
            deptMapping.setDeptCode(e.getCode());
            deptMapping.setUpdateTime(LocalDateTime.now());
            LambdaQueryWrapper<EkbDingDeptMapping> queryWrapper = Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getDeptCode, e.getCode());

            if(ekbDingDeptMappingService.list(queryWrapper).isEmpty()){

                log.info("deptId:{}，deptName:{},deptCode:{}  ,无法找到对应的部门",e.getId(),e.getName(),e.getCode());
            }
            LambdaUpdateWrapper<EkbDingDeptMapping> updateWrapper = Wrappers.lambdaUpdate(EkbDingDeptMapping.class)
                    .set(EkbDingDeptMapping::getEkbDeptId, e.getId())
                    .set(EkbDingDeptMapping::getEkbDeptName, e.getName())
                    .set(EkbDingDeptMapping::getEkbParentId, e.getParentId())
                    .set(EkbDingDeptMapping::getUpdateTime, LocalDateTime.now())
                    .eq(EkbDingDeptMapping::getDeptCode, e.getCode());

            ekbDingDeptMappingService.update(updateWrapper);
        });

    }

    public void pushDepts2Ekb(int level){
//        for( int level = 2;level <= 6;level++){
            LambdaQueryWrapper<EkbDingDeptMapping> queryWrapper = Wrappers.lambdaQuery(EkbDingDeptMapping.class)
                    .eq(EkbDingDeptMapping::getDingDeptLevel,level)
                    .isNull(EkbDingDeptMapping::getEkbDeptId);
            List<EkbDingDeptMapping> mappingList = ekbDingDeptMappingService.list(queryWrapper);
            if(mappingList.isEmpty()){
                log.info("没有数据！");
                return;
            }
            EkbBatchAddDeptReq batchAddDeptReq = new EkbBatchAddDeptReq();
            List<EkbAddDeptReq> ekbAddDeptReqList = Lists.newArrayListWithCapacity(mappingList.size());
            for (EkbDingDeptMapping deptMapping : mappingList) {
                LambdaQueryWrapper<EkbDingDeptMapping> pW = Wrappers.lambdaQuery(EkbDingDeptMapping.class)
//                        .eq(EkbDingDeptMapping::getCompanyId,deptMapping.getCompanyId())
                        .eq(EkbDingDeptMapping::getDingDeptId,deptMapping.getDingParentId());
                EkbDingDeptMapping pDept = ekbDingDeptMappingService.getOne(pW);
                EkbAddDeptReq req = new EkbAddDeptReq();
                req.setParentId(pDept.getEkbDeptId());
                req.setName(deptMapping.getDeptName());
                req.setCode(deptMapping.getDeptCode());
                ekbAddDeptReqList.add(req);
            }
            batchAddDeptReq.setDepartmentList(ekbAddDeptReqList);
            log.info(level + "---- {}", JSON.toJSONString(batchAddDeptReq));

            ekbDeptService.batchAddDept(batchAddDeptReq);
//        }
    }

    /**
     * 获取ekb 流程详情
     * @param flowIds 多个用,隔开
     * @return
     */
    public EkbWorkFlowItemsRsp getEkbFlowDetailByFlowIds(String flowIds){
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getApproveStatesUrl() + StringPool.SLASH + StringPool.LEFT_SQ_BRACKET +
                flowIds + StringPool.RIGHT_SQ_BRACKET + "?accessToken=" + accessTokenRep.getAccessToken();

        EkbWorkFlowItemsRsp ekbWorkflowItemsRsp = httpRestService.get(url, EkbWorkFlowItemsRsp.class, "【获取单据审批状态】");
        log.info("获取单据审批状态：{}", JacksonUtil.toJson(ekbWorkflowItemsRsp));
        return ekbWorkflowItemsRsp;
    }


    /**
     * 获取ekb 流程详情
     * @param flowId 流程id
     * @return
     */
    public EkbWorkFlowDetailRsp getEkbFlowDetailDescByFlowId(String flowId){
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getEkbFlowDetailUrl() + "?flowId="+ flowId + "&accessToken=" + accessTokenRep.getAccessToken();

        EkbWorkFlowDetailRsp ekbWorkflowItemsRsp = httpRestService.get(url, EkbWorkFlowDetailRsp.class, "【根据单据ID获取单据详情】");
        log.info("根据单据ID获取单据详情：{}", JacksonUtil.toJson(ekbWorkflowItemsRsp));
        return ekbWorkflowItemsRsp;
    }

    /**
     * 根据费用类型id获取费用类型详情
     * @param ids
     * @return
     */
    public EkbFeeTypeSearchResponse getEkbInvoiceTypeByIds(List<String> ids) {
        List<DingAppConf> list = dingAppConfService.list(Wrappers.lambdaQuery(DingAppConf.class).eq(DingAppConf::getPartnerType, 1));
        EkbAccessTokenRsp accessTokenRep = ekbLoginService.getAccessToken(list.get(0).getPartnerAppKey(), list.get(0).getPartnerAppSecret());
        String url = ekbConfig.getEkbGetFeeTypeByIdUrl() + StringPool.SLASH + "?accessToken=" + accessTokenRep.getAccessToken();
        EkbFeeTypeSearchRequest request = EkbFeeTypeSearchRequest.builder().build().setIds(ids);

        EkbFeeTypeSearchResponse ekbInvoiceTypeDTO = httpRestService.post(url, JacksonUtil.toJson(request), EkbFeeTypeSearchResponse.class, "【根据ID或CODE获取费用类型模板信息】");
        log.info("根据ID或CODE获取费用类型模板信息：{}", JacksonUtil.toJson(ekbInvoiceTypeDTO));
        return ekbInvoiceTypeDTO;
    }
}
