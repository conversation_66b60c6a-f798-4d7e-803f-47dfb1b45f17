package com.qmqb.oa.admin.service.hsfk;

import com.qmqb.oa.common.enums.DingTalkEventType;
import com.qmqb.oa.system.domain.SysEvent;
import com.qmqb.oa.system.service.SysEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 钉钉事件处理handler
 *
 * <AUTHOR>
 * @date 2024/8/31
 */
@Slf4j
@Component
public class DingTalkSysEventHandler {
    @Autowired
    private SysEventService sysEventService;
    @Autowired
    private DingTalkUserService dingTalkUserService;
    @Autowired
    private List<DingTalkEventPolicyPatternService> eventPolicyPatternService;

    public void dealFailedEvent(SysEvent sysEvent) {
        if (sysEvent.getEventSource() == 2) {
            log.info("无法处理非钉钉事件");
            return;
        }
        try {
            DingTalkEventPolicyPatternService service = eventPolicyPatternService.stream().filter(l ->
                    l.isCeLueModel(sysEvent.getEventType())).findFirst().orElse(null);
            service.toExecuteEvent(sysEvent);
        } catch (Exception e) {
            log.error("【{}】异常", DingTalkEventType.getEventTypeByType(sysEvent.getEventType()).getEventDesc(), e);
            String errorMsg = dingTalkUserService.getErrorMsg(e);
            sysEventService.updateFailReasonById(sysEvent, errorMsg);
        }

    }

}
