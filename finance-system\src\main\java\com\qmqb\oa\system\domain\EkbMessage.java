package com.qmqb.oa.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 易快报消息通知记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
  @TableName("t_ekb_message")
public class EkbMessage implements Serializable {

    private static final long serialVersionUID = 1L;

      /**
     * 主键
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 消息id
     */
      private String messageId;

      /**
     * 单据id
     */
      private String flowId;

      /**
     * 接收人名字
     */
      private String userName;

      /**
     * 接收人userid
     */
      private String userId;

      /**
     * 出站消息类别: flow.rejected=被驳回;freeflow.retract=单据撤回;freeflow.delete=单据删除;backlog.sending=待寄送;flow.paid=已支付/审批完成;freeflow.mention=被@;backlog.paying=待支付;freeflow.comment=评论;backlog.approving=待审批;freeflow.carbonCopy=抄送
     */
      private String msgAction;

      /**
     * 单据状态：approving=待审批/审批中;paid=已支付/审批完成;rejected=已驳回;draft=删除/撤回
     */
      private String state;

      /**
       *站内信标题
       */
      private String actionName;

      /**
     * 推送钉钉的状态：0-待推送;1-推送成功;2-推送失败
     */
      private Integer dingtalkPushStatus;

      /**
     * 创建时间
     */
      private LocalDateTime createTime;

      /**
     * 修改时间
     */
      private LocalDateTime editTime;

      /**
       * 备注信息
       */
      private String remark;



}
