@startuml
participant 系统
participant 钉钉API

autonumber

== 餐费车费团建费发票信息核验 ==

系统 -> 钉钉API: 获取审批实例ID列表
钉钉API --> 系统: 审批实例ID列表

系统 -> 系统: 核对发票
note right
1、查询t_process_record审批实例处理记录表， 判断该流程是否已处理过
2、查询审批实例详情
3、判断当前流程是否财务机器人处理中状态
4、检验审批单表格数据
5、获取发票附件信息
6、校验发票附件
7、校验发票
8、添加审批评论，通过审批
end note

== 生成报销凭证数据 ==
系统 -> 钉钉API: 获取审批实例ID列表
钉钉API --> 系统: 审批实例ID列表
系统 -> 系统: 生成报销凭证
note right
1、查询t_reimbursement_voucher报销凭证表， 判断该报销凭证数据是否已生成过
2、查询审批实例详情
3、判断当前流程是否已审核通过
4、生成凭证
5、核销发票
end note
@enduml
